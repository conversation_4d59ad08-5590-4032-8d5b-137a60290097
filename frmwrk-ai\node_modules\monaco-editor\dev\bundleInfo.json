{"graph": {"===anonymous1===": ["require"], "===anonymous2===": ["vs/editor/editor.main", "vs/css", "vs/base/common/worker/simpleWorker", "vs/editor/common/services/editorSimpleWorker"], "vs/editor/editor.main": ["require", "exports", "vs/editor/editor.api", "vs/editor/editor.all", "vs/editor/standalone/browser/iPadShowKeyboard/iPadShowKeyboard", "vs/editor/standalone/browser/inspectTokens/inspectTokens", "vs/editor/standalone/browser/quickAccess/standaloneHelpQuickAccess", "vs/editor/standalone/browser/quickAccess/standaloneGotoLineQuickAccess", "vs/editor/standalone/browser/quickAccess/standaloneGotoSymbolQuickAccess", "vs/editor/standalone/browser/quickAccess/standaloneCommandsQuickAccess", "vs/editor/standalone/browser/referenceSearch/standaloneReferenceSearch", "vs/editor/standalone/browser/toggleHighContrast/toggleHighContrast"], "vs/css": ["require", "exports"], "vs/base/common/worker/simpleWorker": ["require", "exports", "vs/base/common/errors", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/network", "vs/base/common/platform", "vs/base/common/strings"], "vs/editor/common/services/editorSimpleWorker": ["require", "exports", "vs/base/common/diff/diff", "vs/editor/common/core/range", "vs/editor/common/languages/linkComputer", "vs/editor/common/languages/supports/inplaceReplaceSupport", "vs/editor/common/services/editorBaseApi", "vs/editor/common/services/editorWorkerHost", "vs/base/common/stopwatch", "vs/editor/common/services/unicodeTextModelHighlighter", "vs/editor/common/diff/linesDiffComputers", "vs/base/common/objects", "vs/base/common/network", "vs/editor/common/languages/defaultDocumentColorsComputer", "vs/editor/common/services/findSectionHeaders", "vs/editor/common/services/textModelSync/textModelSync.impl"], "vs/editor/editor.api": ["require", "exports", "vs/editor/common/config/editorOptions", "vs/editor/common/services/editorBaseApi", "vs/editor/standalone/browser/standaloneEditor", "vs/editor/standalone/browser/standaloneLanguages", "vs/editor/contrib/format/browser/format"], "vs/editor/editor.all": ["require", "exports", "vs/editor/browser/coreCommands", "vs/editor/browser/widget/codeEditor/codeEditorWidget", "vs/editor/browser/widget/diffEditor/diffEditor.contribution", "vs/editor/contrib/anchorSelect/browser/anchorSelect", "vs/editor/contrib/bracketMatching/browser/bracketMatching", "vs/editor/contrib/caretOperations/browser/caretOperations", "vs/editor/contrib/caretOperations/browser/transpose", "vs/editor/contrib/clipboard/browser/clipboard", "vs/editor/contrib/codeAction/browser/codeActionContributions", "vs/editor/contrib/codelens/browser/codelensController", "vs/editor/contrib/colorPicker/browser/colorContributions", "vs/editor/contrib/colorPicker/browser/standaloneColorPickerActions", "vs/editor/contrib/comment/browser/comment", "vs/editor/contrib/contextmenu/browser/contextmenu", "vs/editor/contrib/cursorUndo/browser/cursorUndo", "vs/editor/contrib/dnd/browser/dnd", "vs/editor/contrib/dropOrPasteInto/browser/copyPasteContribution", "vs/editor/contrib/dropOrPasteInto/browser/dropIntoEditorContribution", "vs/editor/contrib/find/browser/findController", "vs/editor/contrib/folding/browser/folding", "vs/editor/contrib/fontZoom/browser/fontZoom", "vs/editor/contrib/format/browser/formatActions", "vs/editor/contrib/documentSymbols/browser/documentSymbols", "vs/editor/contrib/inlineCompletions/browser/inlineCompletions.contribution", "vs/editor/contrib/inlineProgress/browser/inlineProgress", "vs/editor/contrib/gotoSymbol/browser/goToCommands", "vs/editor/contrib/gotoSymbol/browser/link/goToDefinitionAtPosition", "vs/editor/contrib/gotoError/browser/gotoError", "vs/editor/contrib/hover/browser/hoverContribution", "vs/editor/contrib/indentation/browser/indentation", "vs/editor/contrib/inlayHints/browser/inlayHintsContribution", "vs/editor/contrib/inPlaceReplace/browser/inPlaceReplace", "vs/editor/contrib/lineSelection/browser/lineSelection", "vs/editor/contrib/linesOperations/browser/linesOperations", "vs/editor/contrib/linkedEditing/browser/linkedEditing", "vs/editor/contrib/links/browser/links", "vs/editor/contrib/longLinesHelper/browser/longLinesHelper", "vs/editor/contrib/multicursor/browser/multicursor", "vs/editor/contrib/inlineEdit/browser/inlineEdit.contribution", "vs/editor/contrib/inlineEdits/browser/inlineEdits.contribution", "vs/editor/contrib/parameterHints/browser/parameterHints", "vs/editor/contrib/placeholderText/browser/placeholderText.contribution", "vs/editor/contrib/rename/browser/rename", "vs/editor/contrib/sectionHeaders/browser/sectionHeaders", "vs/editor/contrib/semanticTokens/browser/documentSemanticTokens", "vs/editor/contrib/semanticTokens/browser/viewportSemanticTokens", "vs/editor/contrib/smartSelect/browser/smartSelect", "vs/editor/contrib/snippet/browser/snippetController2", "vs/editor/contrib/stickyScroll/browser/stickyScrollContribution", "vs/editor/contrib/suggest/browser/suggestController", "vs/editor/contrib/suggest/browser/suggestInlineCompletions", "vs/editor/contrib/tokenization/browser/tokenization", "vs/editor/contrib/toggleTabFocusMode/browser/toggleTabFocusMode", "vs/editor/contrib/unicodeHighlighter/browser/unicodeHighlighter", "vs/editor/contrib/unusualLineTerminators/browser/unusualLineTerminators", "vs/editor/contrib/wordHighlighter/browser/wordHighlighter", "vs/editor/contrib/wordOperations/browser/wordOperations", "vs/editor/contrib/wordPartOperations/browser/wordPartOperations", "vs/editor/contrib/readOnlyMessage/browser/contribution", "vs/editor/contrib/diffEditorBreadcrumbs/browser/contribution", "vs/editor/common/standaloneStrings", "vs/base/browser/ui/codicons/codiconStyles"], "vs/editor/standalone/browser/iPadShowKeyboard/iPadShowKeyboard": ["require", "exports", "vs/base/browser/dom", "vs/base/common/lifecycle", "vs/editor/browser/editorExtensions", "vs/base/common/platform", "vs/css!vs/editor/standalone/browser/iPadShowKeyboard/iPadShowKeyboard"], "vs/editor/standalone/browser/inspectTokens/inspectTokens": ["require", "exports", "vs/base/browser/dom", "vs/base/common/color", "vs/base/common/lifecycle", "vs/editor/browser/editorExtensions", "vs/editor/common/languages", "vs/editor/common/encodedTokenAttributes", "vs/editor/common/languages/nullTokenize", "vs/editor/common/languages/language", "vs/editor/standalone/common/standaloneTheme", "vs/editor/common/standaloneStrings", "vs/css!vs/editor/standalone/browser/inspectTokens/inspectTokens"], "vs/editor/standalone/browser/quickAccess/standaloneHelpQuickAccess": ["require", "exports", "vs/platform/registry/common/platform", "vs/platform/quickinput/common/quickAccess", "vs/editor/common/standaloneStrings", "vs/platform/quickinput/browser/helpQuickAccess"], "vs/editor/standalone/browser/quickAccess/standaloneGotoLineQuickAccess": ["require", "exports", "vs/editor/contrib/quickAccess/browser/gotoLineQuickAccess", "vs/platform/registry/common/platform", "vs/platform/quickinput/common/quickAccess", "vs/editor/browser/services/codeEditorService", "vs/editor/common/standaloneStrings", "vs/base/common/event", "vs/editor/browser/editorExtensions", "vs/editor/common/editorContext<PERSON>eys", "vs/platform/quickinput/common/quickInput"], "vs/editor/standalone/browser/quickAccess/standaloneGotoSymbolQuickAccess": ["require", "exports", "vs/editor/contrib/quickAccess/browser/gotoSymbolQuickAccess", "vs/platform/registry/common/platform", "vs/platform/quickinput/common/quickAccess", "vs/editor/browser/services/codeEditorService", "vs/editor/common/standaloneStrings", "vs/base/common/event", "vs/editor/browser/editorExtensions", "vs/editor/common/editorContext<PERSON>eys", "vs/platform/quickinput/common/quickInput", "vs/editor/contrib/documentSymbols/browser/outlineModel", "vs/editor/common/services/languageFeatures", "vs/base/browser/ui/codicons/codiconStyles", "vs/editor/contrib/symbolIcons/browser/symbolIcons"], "vs/editor/standalone/browser/quickAccess/standaloneCommandsQuickAccess": ["require", "exports", "vs/platform/registry/common/platform", "vs/platform/quickinput/common/quickAccess", "vs/editor/common/standaloneStrings", "vs/editor/browser/services/codeEditorService", "vs/editor/contrib/quickAccess/browser/commandsQuickAccess", "vs/platform/instantiation/common/instantiation", "vs/platform/keybinding/common/keybinding", "vs/platform/commands/common/commands", "vs/platform/telemetry/common/telemetry", "vs/platform/dialogs/common/dialogs", "vs/editor/browser/editorExtensions", "vs/editor/common/editorContext<PERSON>eys", "vs/platform/quickinput/common/quickInput"], "vs/editor/standalone/browser/referenceSearch/standaloneReferenceSearch": ["require", "exports", "vs/editor/browser/editorExtensions", "vs/editor/browser/services/codeEditorService", "vs/editor/contrib/gotoSymbol/browser/peek/referencesController", "vs/platform/configuration/common/configuration", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation", "vs/platform/notification/common/notification", "vs/platform/storage/common/storage"], "vs/editor/standalone/browser/toggleHighContrast/toggleHighContrast": ["require", "exports", "vs/editor/browser/editorExtensions", "vs/editor/standalone/common/standaloneTheme", "vs/editor/common/standaloneStrings", "vs/platform/theme/common/theme", "vs/editor/standalone/browser/standaloneThemeService"], "vs/base/common/errors": ["require", "exports"], "vs/base/common/event": ["require", "exports", "vs/base/common/errors", "vs/base/common/functional", "vs/base/common/lifecycle", "vs/base/common/linkedList", "vs/base/common/stopwatch"], "vs/base/common/lifecycle": ["require", "exports", "vs/base/common/functional", "vs/base/common/iterator"], "vs/base/common/network": ["require", "exports", "vs/base/common/errors", "vs/base/common/platform", "vs/base/common/strings", "vs/base/common/uri", "vs/base/common/path"], "vs/base/common/platform": ["require", "exports", "vs/nls"], "vs/base/common/strings": ["require", "exports", "vs/base/common/cache", "vs/base/common/lazy"], "vs/base/common/diff/diff": ["require", "exports", "vs/base/common/diff/diffChange", "vs/base/common/hash"], "vs/editor/common/core/range": ["require", "exports", "vs/editor/common/core/position"], "vs/editor/common/languages/linkComputer": ["require", "exports", "vs/editor/common/core/characterClassifier"], "vs/editor/common/languages/supports/inplaceReplaceSupport": ["require", "exports"], "vs/editor/common/services/editorBaseApi": ["require", "exports", "vs/base/common/cancellation", "vs/base/common/event", "vs/base/common/keyCodes", "vs/base/common/uri", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/editor/common/languages", "vs/editor/common/standalone/standaloneEnums"], "vs/editor/common/services/editorWorkerHost": ["require", "exports"], "vs/base/common/stopwatch": ["require", "exports"], "vs/editor/common/services/unicodeTextModelHighlighter": ["require", "exports", "vs/editor/common/core/range", "vs/editor/common/model/textModelSearch", "vs/base/common/strings", "vs/base/common/assert", "vs/editor/common/core/wordHelper"], "vs/editor/common/diff/linesDiffComputers": ["require", "exports", "vs/editor/common/diff/legacyLinesDiffComputer", "vs/editor/common/diff/defaultLinesDiffComputer/defaultLinesDiffComputer"], "vs/base/common/objects": ["require", "exports", "vs/base/common/types"], "vs/editor/common/languages/defaultDocumentColorsComputer": ["require", "exports", "vs/base/common/color"], "vs/editor/common/services/findSectionHeaders": ["require", "exports"], "vs/editor/common/services/textModelSync/textModelSync.impl": ["require", "exports", "vs/base/common/async", "vs/base/common/lifecycle", "vs/base/common/uri", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/core/wordHelper", "vs/editor/common/model/mirrorTextModel"], "vs/editor/browser/coreCommands": ["require", "exports", "vs/nls", "vs/base/browser/browser", "vs/base/common/types", "vs/base/browser/ui/aria/aria", "vs/editor/browser/editorExtensions", "vs/editor/browser/services/codeEditorService", "vs/editor/common/cursor/cursorColumnSelection", "vs/editor/common/cursor<PERSON><PERSON>mon", "vs/editor/common/cursor/cursorDeleteOperations", "vs/editor/common/cursor/cursorMoveCommands", "vs/editor/common/cursor/cursorTypeOperations", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/editorContext<PERSON>eys", "vs/platform/contextkey/common/contextkey", "vs/platform/keybinding/common/keybindingsRegistry", "vs/base/browser/dom", "vs/editor/common/cursor/cursorTypeEditOperations"], "vs/editor/browser/widget/codeEditor/codeEditorWidget": ["require", "exports", "vs/base/browser/dom", "vs/base/common/errors", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/network", "vs/editor/browser/config/domFontInfo", "vs/editor/browser/config/editorConfiguration", "vs/editor/browser/config/tabFocus", "vs/editor/browser/editorExtensions", "vs/editor/browser/services/codeEditorService", "vs/editor/browser/view", "vs/editor/browser/view/domLineBreaksComputer", "vs/editor/browser/view/viewUserInputEvents", "vs/editor/browser/widget/codeEditor/codeEditorContributions", "vs/editor/common/config/editorOptions", "vs/editor/common/core/cursorColumns", "vs/editor/common/core/editorColorRegistry", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/editor/common/cursor/cursorWordOperations", "vs/editor/common/editorAction", "vs/editor/common/editor<PERSON><PERSON><PERSON>", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/common/languages/languageConfigurationRegistry", "vs/editor/common/model/textModel", "vs/editor/common/services/languageFeatures", "vs/editor/common/viewModel/monospaceLineBreaksComputer", "vs/editor/common/viewModel/viewModelImpl", "vs/nls", "vs/platform/accessibility/common/accessibility", "vs/platform/commands/common/commands", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation", "vs/platform/instantiation/common/serviceCollection", "vs/platform/notification/common/notification", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/themeService", "vs/platform/actions/common/actions", "vs/editor/browser/services/markerDecorations", "vs/css!vs/editor/browser/widget/codeEditor/editor"], "vs/editor/browser/widget/diffEditor/diffEditor.contribution": ["require", "exports", "vs/base/common/codicons", "vs/editor/browser/widget/diffEditor/commands", "vs/editor/common/editorContext<PERSON>eys", "vs/nls", "vs/platform/actions/common/actions", "vs/platform/commands/common/commands", "vs/platform/contextkey/common/contextkey", "vs/editor/browser/widget/diffEditor/registrations.contribution"], "vs/editor/contrib/anchorSelect/browser/anchorSelect": ["require", "exports", "vs/base/browser/ui/aria/aria", "vs/base/common/htmlContent", "vs/base/common/keyCodes", "vs/editor/browser/editorExtensions", "vs/editor/common/core/selection", "vs/editor/common/editorContext<PERSON>eys", "vs/nls", "vs/platform/contextkey/common/contextkey", "vs/css!vs/editor/contrib/anchorSelect/browser/anchorSelect"], "vs/editor/contrib/bracketMatching/browser/bracketMatching": ["require", "exports", "vs/base/common/async", "vs/base/common/lifecycle", "vs/editor/browser/editorExtensions", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/common/model", "vs/editor/common/model/textModel", "vs/nls", "vs/platform/actions/common/actions", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/themeService", "vs/css!vs/editor/contrib/bracketMatching/browser/bracketMatching"], "vs/editor/contrib/caretOperations/browser/caretOperations": ["require", "exports", "vs/editor/browser/editorExtensions", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/contrib/caretOperations/browser/moveCaretCommand", "vs/nls"], "vs/editor/contrib/caretOperations/browser/transpose": ["require", "exports", "vs/editor/browser/editorExtensions", "vs/editor/common/commands/replaceCommand", "vs/editor/common/cursor/cursorMoveOperations", "vs/editor/common/core/range", "vs/editor/common/editorContext<PERSON>eys", "vs/nls"], "vs/editor/contrib/clipboard/browser/clipboard": ["require", "exports", "vs/base/browser/browser", "vs/base/browser/dom", "vs/base/common/platform", "vs/editor/browser/controller/textAreaInput", "vs/editor/browser/editorExtensions", "vs/editor/browser/services/codeEditorService", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/contrib/dropOrPasteInto/browser/copyPasteController", "vs/nls", "vs/platform/actions/common/actions", "vs/platform/clipboard/common/clipboardService", "vs/platform/contextkey/common/contextkey"], "vs/editor/contrib/codeAction/browser/codeActionContributions": ["require", "exports", "vs/editor/browser/editorExtensions", "vs/editor/common/config/editorConfigurationSchema", "vs/editor/contrib/codeAction/browser/codeActionCommands", "vs/editor/contrib/codeAction/browser/codeActionController", "vs/editor/contrib/codeAction/browser/lightBulbWidget", "vs/nls", "vs/platform/configuration/common/configurationRegistry", "vs/platform/registry/common/platform"], "vs/editor/contrib/codelens/browser/codelensController": ["require", "exports", "vs/base/common/async", "vs/base/common/errors", "vs/base/common/lifecycle", "vs/editor/browser/stableEditorScroll", "vs/editor/browser/editorExtensions", "vs/editor/common/config/editorOptions", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/contrib/codelens/browser/codelens", "vs/editor/contrib/codelens/browser/codeLensCache", "vs/editor/contrib/codelens/browser/codelensWidget", "vs/nls", "vs/platform/commands/common/commands", "vs/platform/notification/common/notification", "vs/platform/quickinput/common/quickInput", "vs/editor/common/services/languageFeatureDebounce", "vs/editor/common/services/languageFeatures"], "vs/editor/contrib/colorPicker/browser/colorContributions": ["require", "exports", "vs/base/common/lifecycle", "vs/editor/browser/editorExtensions", "vs/editor/common/core/range", "vs/editor/contrib/colorPicker/browser/colorDetector", "vs/editor/contrib/colorPicker/browser/colorHoverParticipant", "vs/editor/contrib/hover/browser/contentHoverController2", "vs/editor/contrib/hover/browser/hoverTypes"], "vs/editor/contrib/colorPicker/browser/standaloneColorPickerActions": ["require", "exports", "vs/editor/browser/editorExtensions", "vs/nls", "vs/editor/contrib/colorPicker/browser/standaloneColorPickerWidget", "vs/editor/common/editorContext<PERSON>eys", "vs/platform/actions/common/actions", "vs/css!vs/editor/contrib/colorPicker/browser/colorPicker"], "vs/editor/contrib/comment/browser/comment": ["require", "exports", "vs/base/common/keyCodes", "vs/editor/browser/editorExtensions", "vs/editor/common/core/range", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/common/languages/languageConfigurationRegistry", "vs/editor/contrib/comment/browser/blockCommentCommand", "vs/editor/contrib/comment/browser/lineCommentCommand", "vs/nls", "vs/platform/actions/common/actions"], "vs/editor/contrib/contextmenu/browser/contextmenu": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/actionbar/actionViewItems", "vs/base/common/actions", "vs/base/common/lifecycle", "vs/base/common/platform", "vs/editor/browser/editorExtensions", "vs/editor/common/editorContext<PERSON>eys", "vs/nls", "vs/platform/actions/common/actions", "vs/platform/contextkey/common/contextkey", "vs/platform/contextview/browser/contextView", "vs/platform/keybinding/common/keybinding", "vs/platform/configuration/common/configuration", "vs/platform/workspace/common/workspace"], "vs/editor/contrib/cursorUndo/browser/cursorUndo": ["require", "exports", "vs/base/common/lifecycle", "vs/editor/browser/editorExtensions", "vs/editor/common/editorContext<PERSON>eys", "vs/nls"], "vs/editor/contrib/dnd/browser/dnd": ["require", "exports", "vs/base/common/lifecycle", "vs/base/common/platform", "vs/editor/browser/editorExtensions", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/editor/common/model/textModel", "vs/editor/contrib/dnd/browser/dragAndDropCommand", "vs/css!vs/editor/contrib/dnd/browser/dnd"], "vs/editor/contrib/dropOrPasteInto/browser/copyPasteContribution": ["require", "exports", "vs/base/common/hierarchicalKind", "vs/editor/browser/editorExtensions", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/common/editorFeatures", "vs/editor/contrib/dropOrPasteInto/browser/copyPasteController", "vs/editor/contrib/dropOrPasteInto/browser/defaultProviders", "vs/nls"], "vs/editor/contrib/dropOrPasteInto/browser/dropIntoEditorContribution": ["require", "exports", "vs/editor/browser/editorExtensions", "vs/editor/common/config/editorConfigurationSchema", "vs/editor/common/editorFeatures", "vs/editor/contrib/dropOrPasteInto/browser/defaultProviders", "vs/nls", "vs/platform/configuration/common/configurationRegistry", "vs/platform/registry/common/platform", "vs/editor/contrib/dropOrPasteInto/browser/dropIntoEditorController"], "vs/editor/contrib/find/browser/findController": ["require", "exports", "vs/base/common/async", "vs/base/common/lifecycle", "vs/base/common/strings", "vs/editor/browser/editorExtensions", "vs/editor/common/core/editorColorRegistry", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/common/model", "vs/editor/contrib/find/browser/findModel", "vs/editor/contrib/find/browser/findOptionsWidget", "vs/editor/contrib/find/browser/findState", "vs/editor/contrib/find/browser/findWidget", "vs/nls", "vs/platform/actions/common/actions", "vs/platform/clipboard/common/clipboardService", "vs/platform/contextkey/common/contextkey", "vs/platform/contextview/browser/contextView", "vs/platform/keybinding/common/keybinding", "vs/platform/notification/common/notification", "vs/platform/quickinput/common/quickInput", "vs/platform/storage/common/storage", "vs/platform/theme/common/themeService", "vs/platform/hover/browser/hover"], "vs/editor/contrib/folding/browser/folding": ["require", "exports", "vs/base/common/async", "vs/base/common/cancellation", "vs/base/common/errors", "vs/base/common/keyCodes", "vs/base/common/lifecycle", "vs/base/common/strings", "vs/base/common/types", "vs/editor/browser/stableEditorScroll", "vs/editor/browser/editorExtensions", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/common/languages", "vs/editor/common/languages/languageConfigurationRegistry", "vs/editor/contrib/folding/browser/foldingModel", "vs/editor/contrib/folding/browser/hiddenRangeModel", "vs/editor/contrib/folding/browser/indentRangeProvider", "vs/nls", "vs/platform/contextkey/common/contextkey", "vs/editor/contrib/folding/browser/foldingDecorations", "vs/editor/contrib/folding/browser/foldingRanges", "vs/editor/contrib/folding/browser/syntaxRangeProvider", "vs/platform/notification/common/notification", "vs/editor/common/services/languageFeatureDebounce", "vs/base/common/stopwatch", "vs/editor/common/services/languageFeatures", "vs/base/common/event", "vs/platform/commands/common/commands", "vs/base/common/uri", "vs/editor/common/services/model", "vs/platform/configuration/common/configuration", "vs/css!vs/editor/contrib/folding/browser/folding"], "vs/editor/contrib/fontZoom/browser/fontZoom": ["require", "exports", "vs/editor/browser/editorExtensions", "vs/editor/common/config/editorZoom", "vs/nls"], "vs/editor/contrib/format/browser/formatActions": ["require", "exports", "vs/base/common/arrays", "vs/base/common/cancellation", "vs/base/common/errors", "vs/base/common/keyCodes", "vs/base/common/lifecycle", "vs/editor/browser/editorExtensions", "vs/editor/browser/services/codeEditorService", "vs/editor/common/core/characterClassifier", "vs/editor/common/core/range", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/common/services/editorW<PERSON>ker", "vs/editor/common/services/languageFeatures", "vs/editor/contrib/format/browser/format", "vs/editor/contrib/format/browser/formattingEdit", "vs/nls", "vs/platform/accessibilitySignal/browser/accessibilitySignalService", "vs/platform/commands/common/commands", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation", "vs/platform/progress/common/progress"], "vs/editor/contrib/documentSymbols/browser/documentSymbols": ["require", "exports", "vs/base/common/cancellation", "vs/base/common/types", "vs/base/common/uri", "vs/editor/common/services/resolverService", "vs/editor/contrib/documentSymbols/browser/outlineModel", "vs/platform/commands/common/commands"], "vs/editor/contrib/inlineCompletions/browser/inlineCompletions.contribution": ["require", "exports", "vs/editor/browser/editorExtensions", "vs/editor/contrib/hover/browser/hoverTypes", "vs/editor/contrib/inlineCompletions/browser/controller/commands", "vs/editor/contrib/inlineCompletions/browser/hintsWidget/hoverParticipant", "vs/editor/contrib/inlineCompletions/browser/inlineCompletionsAccessibleView", "vs/editor/contrib/inlineCompletions/browser/controller/inlineCompletionsController", "vs/platform/accessibility/browser/accessibleViewRegistry", "vs/platform/actions/common/actions"], "vs/editor/contrib/inlineProgress/browser/inlineProgress": ["require", "exports", "vs/base/browser/dom", "vs/base/common/async", "vs/base/common/codicons", "vs/base/common/lifecycle", "vs/base/common/strings", "vs/base/common/themables", "vs/editor/common/core/range", "vs/editor/common/model/textModel", "vs/platform/instantiation/common/instantiation", "vs/css!vs/editor/contrib/inlineProgress/browser/inlineProgressWidget"], "vs/editor/contrib/gotoSymbol/browser/goToCommands": ["require", "exports", "vs/base/browser/ui/aria/aria", "vs/base/common/async", "vs/base/common/keyCodes", "vs/base/common/types", "vs/base/common/uri", "vs/editor/contrib/editorState/browser/editorState", "vs/editor/browser/editorBrowser", "vs/editor/browser/editorExtensions", "vs/editor/browser/services/codeEditorService", "vs/editor/browser/widget/codeEditor/embeddedCodeEditorWidget", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/common/languages", "vs/editor/contrib/gotoSymbol/browser/peek/referencesController", "vs/editor/contrib/gotoSymbol/browser/referencesModel", "vs/editor/contrib/gotoSymbol/browser/symbolNavigation", "vs/editor/contrib/message/browser/messageController", "vs/editor/contrib/peekView/browser/peekView", "vs/nls", "vs/platform/actions/common/actions", "vs/platform/commands/common/commands", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation", "vs/platform/notification/common/notification", "vs/platform/progress/common/progress", "vs/editor/contrib/gotoSymbol/browser/goToSymbol", "vs/editor/common/services/languageFeatures", "vs/base/common/iterator", "vs/platform/contextkey/common/contextkeys"], "vs/editor/contrib/gotoSymbol/browser/link/goToDefinitionAtPosition": ["require", "exports", "vs/base/common/async", "vs/base/common/errors", "vs/base/common/htmlContent", "vs/base/common/lifecycle", "vs/editor/contrib/editorState/browser/editorState", "vs/editor/browser/editorExtensions", "vs/editor/common/core/range", "vs/editor/common/languages/language", "vs/editor/common/services/resolverService", "vs/editor/contrib/gotoSymbol/browser/link/clickLinkGesture", "vs/editor/contrib/peekView/browser/peekView", "vs/nls", "vs/platform/contextkey/common/contextkey", "vs/editor/contrib/gotoSymbol/browser/goToCommands", "vs/editor/contrib/gotoSymbol/browser/goToSymbol", "vs/editor/common/services/languageFeatures", "vs/editor/common/model/textModel", "vs/css!vs/editor/contrib/gotoSymbol/browser/link/goToDefinitionAtPosition"], "vs/editor/contrib/gotoError/browser/gotoError": ["require", "exports", "vs/base/common/codicons", "vs/base/common/lifecycle", "vs/editor/browser/editorExtensions", "vs/editor/browser/services/codeEditorService", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/contrib/gotoError/browser/markerNavigationService", "vs/nls", "vs/platform/actions/common/actions", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation", "vs/platform/theme/common/iconRegistry", "vs/editor/contrib/gotoError/browser/gotoErrorWidget"], "vs/editor/contrib/hover/browser/hoverContribution": ["require", "exports", "vs/editor/contrib/hover/browser/hoverActions", "vs/editor/browser/editorExtensions", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/themeService", "vs/editor/contrib/hover/browser/hoverTypes", "vs/editor/contrib/hover/browser/markdownHoverParticipant", "vs/editor/contrib/hover/browser/markerHoverParticipant", "vs/editor/contrib/hover/browser/contentHoverController2", "vs/editor/contrib/hover/browser/marginHoverController", "vs/platform/accessibility/browser/accessibleViewRegistry", "vs/editor/contrib/hover/browser/hoverAccessibleViews", "vs/css!vs/editor/contrib/hover/browser/hover"], "vs/editor/contrib/indentation/browser/indentation": ["require", "exports", "vs/base/common/lifecycle", "vs/base/common/strings", "vs/editor/browser/editorExtensions", "vs/editor/common/commands/shiftCommand", "vs/editor/common/core/range", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/common/languages/languageConfigurationRegistry", "vs/editor/common/services/model", "vs/editor/contrib/indentation/common/indentUtils", "vs/nls", "vs/platform/quickinput/common/quickInput", "vs/editor/common/languages/autoIndent", "vs/editor/contrib/indentation/common/indentation", "vs/editor/common/tokens/lineTokens"], "vs/editor/contrib/inlayHints/browser/inlayHintsContribution": ["require", "exports", "vs/editor/browser/editorExtensions", "vs/editor/contrib/hover/browser/hoverTypes", "vs/editor/contrib/inlayHints/browser/inlayHintsController", "vs/editor/contrib/inlayHints/browser/inlayHintsHover"], "vs/editor/contrib/inPlaceReplace/browser/inPlaceReplace": ["require", "exports", "vs/base/common/async", "vs/base/common/errors", "vs/editor/contrib/editorState/browser/editorState", "vs/editor/browser/editorExtensions", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/common/model/textModel", "vs/editor/common/services/editorW<PERSON>ker", "vs/nls", "vs/editor/contrib/inPlaceReplace/browser/inPlaceReplaceCommand", "vs/css!vs/editor/contrib/inPlaceReplace/browser/inPlaceReplace"], "vs/editor/contrib/lineSelection/browser/lineSelection": ["require", "exports", "vs/editor/browser/editorExtensions", "vs/editor/common/cursor/cursorMoveCommands", "vs/editor/common/editorContext<PERSON>eys", "vs/nls"], "vs/editor/contrib/linesOperations/browser/linesOperations": ["require", "exports", "vs/base/common/keyCodes", "vs/editor/browser/coreCommands", "vs/editor/browser/editorExtensions", "vs/editor/common/commands/replaceCommand", "vs/editor/common/commands/trimTrailingWhitespaceCommand", "vs/editor/common/cursor/cursorTypeOperations", "vs/editor/common/cursor/cursorTypeEditOperations", "vs/editor/common/core/editOperation", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/contrib/linesOperations/browser/copyLinesCommand", "vs/editor/contrib/linesOperations/browser/moveLinesCommand", "vs/editor/contrib/linesOperations/browser/sortLinesCommand", "vs/nls", "vs/platform/actions/common/actions", "vs/editor/common/languages/languageConfigurationRegistry", "vs/platform/configuration/common/configuration"], "vs/editor/contrib/linkedEditing/browser/linkedEditing": ["require", "exports", "vs/base/common/arrays", "vs/base/common/async", "vs/base/common/cancellation", "vs/base/common/color", "vs/base/common/errors", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/strings", "vs/base/common/uri", "vs/editor/browser/editorExtensions", "vs/editor/browser/services/codeEditorService", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/common/model/textModel", "vs/editor/common/languages/languageConfigurationRegistry", "vs/nls", "vs/platform/contextkey/common/contextkey", "vs/editor/common/services/languageFeatures", "vs/platform/theme/common/colorRegistry", "vs/editor/common/services/languageFeatureDebounce", "vs/base/common/stopwatch", "vs/css!vs/editor/contrib/linkedEditing/browser/linkedEditing"], "vs/editor/contrib/links/browser/links": ["require", "exports", "vs/base/common/async", "vs/base/common/cancellation", "vs/base/common/errors", "vs/base/common/htmlContent", "vs/base/common/lifecycle", "vs/base/common/network", "vs/base/common/platform", "vs/base/common/resources", "vs/base/common/stopwatch", "vs/base/common/uri", "vs/editor/browser/editorExtensions", "vs/editor/common/model/textModel", "vs/editor/common/services/languageFeatureDebounce", "vs/editor/common/services/languageFeatures", "vs/editor/contrib/gotoSymbol/browser/link/clickLinkGesture", "vs/editor/contrib/links/browser/getLinks", "vs/nls", "vs/platform/notification/common/notification", "vs/platform/opener/common/opener", "vs/css!vs/editor/contrib/links/browser/links"], "vs/editor/contrib/longLinesHelper/browser/longLinesHelper": ["require", "exports", "vs/base/common/lifecycle", "vs/editor/browser/editorExtensions"], "vs/editor/contrib/multicursor/browser/multicursor": ["require", "exports", "vs/base/browser/ui/aria/aria", "vs/base/common/async", "vs/base/common/keyCodes", "vs/base/common/lifecycle", "vs/editor/browser/editorExtensions", "vs/editor/common/cursor/cursorMoveCommands", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/contrib/find/browser/findController", "vs/nls", "vs/platform/actions/common/actions", "vs/platform/contextkey/common/contextkey", "vs/editor/common/services/languageFeatures", "vs/editor/contrib/wordHighlighter/browser/highlightDecorations", "vs/platform/instantiation/common/instantiation"], "vs/editor/contrib/inlineEdit/browser/inlineEdit.contribution": ["require", "exports", "vs/editor/browser/editorExtensions", "vs/editor/contrib/inlineEdit/browser/commands", "vs/editor/contrib/inlineEdit/browser/inlineEditController"], "vs/editor/contrib/inlineEdits/browser/inlineEdits.contribution": ["require", "exports", "vs/editor/browser/editorExtensions", "vs/editor/contrib/inlineEdits/browser/commands", "vs/editor/contrib/inlineEdits/browser/inlineEditsController"], "vs/editor/contrib/parameterHints/browser/parameterHints": ["require", "exports", "vs/base/common/lazy", "vs/base/common/lifecycle", "vs/editor/browser/editorExtensions", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/common/languages", "vs/editor/common/services/languageFeatures", "vs/editor/contrib/parameterHints/browser/parameterHintsModel", "vs/editor/contrib/parameterHints/browser/provideSignatureHelp", "vs/nls", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation", "vs/editor/contrib/parameterHints/browser/parameterHintsWidget"], "vs/editor/contrib/placeholderText/browser/placeholderText.contribution": ["require", "exports", "vs/editor/browser/editorExtensions", "vs/editor/common/core/editorColorRegistry", "vs/nls", "vs/platform/theme/common/colorUtils", "vs/editor/contrib/placeholderText/browser/placeholderTextContribution", "vs/platform/observable/common/wrapInReloadableClass", "vs/css!vs/editor/contrib/placeholderText/browser/placeholderText"], "vs/editor/contrib/rename/browser/rename": ["require", "exports", "vs/base/browser/ui/aria/aria", "vs/base/common/async", "vs/base/common/cancellation", "vs/base/common/errors", "vs/base/common/htmlContent", "vs/base/common/lifecycle", "vs/base/common/types", "vs/base/common/uri", "vs/editor/browser/editorExtensions", "vs/editor/browser/services/bulkEditService", "vs/editor/browser/services/codeEditorService", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/common/languages", "vs/editor/common/services/languageFeatures", "vs/editor/common/services/textResourceConfiguration", "vs/editor/contrib/editorState/browser/editorState", "vs/editor/contrib/message/browser/messageController", "vs/nls", "vs/platform/actions/common/actions", "vs/platform/configuration/common/configurationRegistry", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation", "vs/platform/log/common/log", "vs/platform/notification/common/notification", "vs/platform/progress/common/progress", "vs/platform/registry/common/platform", "vs/platform/telemetry/common/telemetry", "vs/editor/contrib/rename/browser/renameWidget"], "vs/editor/contrib/sectionHeaders/browser/sectionHeaders": ["require", "exports", "vs/base/common/async", "vs/base/common/lifecycle", "vs/editor/browser/editorExtensions", "vs/editor/common/languages/languageConfigurationRegistry", "vs/editor/common/model/textModel", "vs/editor/common/services/editorW<PERSON>ker"], "vs/editor/contrib/semanticTokens/browser/documentSemanticTokens": ["require", "exports", "vs/base/common/lifecycle", "vs/base/common/errors", "vs/editor/common/services/model", "vs/platform/configuration/common/configuration", "vs/base/common/async", "vs/base/common/cancellation", "vs/platform/theme/common/themeService", "vs/editor/common/services/semanticTokensProviderStyling", "vs/editor/contrib/semanticTokens/common/getSemanticTokens", "vs/editor/common/services/languageFeatureDebounce", "vs/base/common/stopwatch", "vs/editor/common/services/languageFeatures", "vs/editor/common/services/semanticTokensStyling", "vs/editor/common/editorFeatures", "vs/editor/contrib/semanticTokens/common/semanticTokensConfig"], "vs/editor/contrib/semanticTokens/browser/viewportSemanticTokens": ["require", "exports", "vs/base/common/async", "vs/base/common/lifecycle", "vs/editor/browser/editorExtensions", "vs/editor/contrib/semanticTokens/common/getSemanticTokens", "vs/editor/contrib/semanticTokens/common/semanticTokensConfig", "vs/editor/common/services/semanticTokensProviderStyling", "vs/platform/configuration/common/configuration", "vs/platform/theme/common/themeService", "vs/editor/common/services/languageFeatureDebounce", "vs/base/common/stopwatch", "vs/editor/common/services/languageFeatures", "vs/editor/common/services/semanticTokensStyling"], "vs/editor/contrib/smartSelect/browser/smartSelect": ["require", "exports", "vs/base/common/arrays", "vs/base/common/cancellation", "vs/base/common/errors", "vs/editor/browser/editorExtensions", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/contrib/smartSelect/browser/bracketSelections", "vs/editor/contrib/smartSelect/browser/wordSelections", "vs/nls", "vs/platform/actions/common/actions", "vs/platform/commands/common/commands", "vs/editor/common/services/languageFeatures", "vs/editor/common/services/resolverService", "vs/base/common/types", "vs/base/common/uri"], "vs/editor/contrib/snippet/browser/snippetController2": ["require", "exports", "vs/base/common/lifecycle", "vs/base/common/types", "vs/editor/browser/editorExtensions", "vs/editor/common/core/position", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/common/languages/languageConfigurationRegistry", "vs/editor/common/services/languageFeatures", "vs/editor/contrib/suggest/browser/suggest", "vs/nls", "vs/platform/contextkey/common/contextkey", "vs/platform/log/common/log", "vs/editor/contrib/snippet/browser/snippetSession"], "vs/editor/contrib/stickyScroll/browser/stickyScrollContribution": ["require", "exports", "vs/editor/browser/editorExtensions", "vs/editor/contrib/stickyScroll/browser/stickyScrollActions", "vs/editor/contrib/stickyScroll/browser/stickyScrollController", "vs/platform/actions/common/actions"], "vs/editor/contrib/suggest/browser/suggestController": ["require", "exports", "vs/base/browser/ui/aria/aria", "vs/base/common/arrays", "vs/base/common/cancellation", "vs/base/common/errors", "vs/base/common/event", "vs/base/common/keybindings", "vs/base/common/lifecycle", "vs/base/common/platform", "vs/base/common/stopwatch", "vs/base/common/types", "vs/editor/browser/stableEditorScroll", "vs/editor/browser/editorExtensions", "vs/editor/common/core/editOperation", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/contrib/snippet/browser/snippetController2", "vs/editor/contrib/snippet/browser/snippetParser", "vs/editor/contrib/suggest/browser/suggestMemory", "vs/editor/contrib/suggest/browser/wordContextKey", "vs/nls", "vs/platform/commands/common/commands", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation", "vs/platform/log/common/log", "vs/editor/contrib/suggest/browser/suggest", "vs/editor/contrib/suggest/browser/suggestAlternatives", "vs/editor/contrib/suggest/browser/suggestCommitCharacters", "vs/editor/contrib/suggest/browser/suggestModel", "vs/editor/contrib/suggest/browser/suggestOvertypingCapturer", "vs/editor/contrib/suggest/browser/suggestWidget", "vs/platform/telemetry/common/telemetry", "vs/base/common/resources", "vs/base/common/hash", "vs/base/browser/dom", "vs/editor/common/model/textModel"], "vs/editor/contrib/suggest/browser/suggestInlineCompletions": ["require", "exports", "vs/base/common/cancellation", "vs/base/common/filters", "vs/base/common/iterator", "vs/base/common/lifecycle", "vs/editor/browser/services/codeEditorService", "vs/editor/common/core/range", "vs/editor/common/editorFeatures", "vs/editor/common/services/languageFeatures", "vs/editor/contrib/suggest/browser/completionModel", "vs/editor/contrib/suggest/browser/suggest", "vs/editor/contrib/suggest/browser/suggestMemory", "vs/editor/contrib/suggest/browser/suggestModel", "vs/editor/contrib/suggest/browser/wordDistance", "vs/platform/clipboard/common/clipboardService"], "vs/editor/contrib/tokenization/browser/tokenization": ["require", "exports", "vs/base/common/stopwatch", "vs/editor/browser/editorExtensions", "vs/nls"], "vs/editor/contrib/toggleTabFocusMode/browser/toggleTabFocusMode": ["require", "exports", "vs/base/browser/ui/aria/aria", "vs/editor/browser/config/tabFocus", "vs/nls", "vs/platform/actions/common/actions"], "vs/editor/contrib/unicodeHighlighter/browser/unicodeHighlighter": ["require", "exports", "vs/base/common/async", "vs/base/common/codicons", "vs/base/common/htmlContent", "vs/base/common/lifecycle", "vs/base/common/platform", "vs/base/common/strings", "vs/editor/browser/editorExtensions", "vs/editor/common/config/editorOptions", "vs/editor/common/model/textModel", "vs/editor/common/services/unicodeTextModelHighlighter", "vs/editor/common/services/editorW<PERSON>ker", "vs/editor/common/languages/language", "vs/editor/common/viewModel/viewModelDecorations", "vs/editor/contrib/hover/browser/hoverTypes", "vs/editor/contrib/hover/browser/markdownHoverParticipant", "vs/editor/contrib/unicodeHighlighter/browser/bannerController", "vs/nls", "vs/platform/configuration/common/configuration", "vs/platform/instantiation/common/instantiation", "vs/platform/opener/common/opener", "vs/platform/quickinput/common/quickInput", "vs/platform/theme/common/iconRegistry", "vs/platform/workspace/common/workspaceTrust", "vs/css!vs/editor/contrib/unicodeHighlighter/browser/unicodeHighlighter"], "vs/editor/contrib/unusualLineTerminators/browser/unusualLineTerminators": ["require", "exports", "vs/base/common/lifecycle", "vs/base/common/resources", "vs/editor/browser/editorExtensions", "vs/editor/browser/services/codeEditorService", "vs/nls", "vs/platform/dialogs/common/dialogs"], "vs/editor/contrib/wordHighlighter/browser/wordHighlighter": ["require", "exports", "vs/nls", "vs/base/browser/ui/aria/aria", "vs/base/common/async", "vs/base/common/cancellation", "vs/base/common/errors", "vs/base/common/lifecycle", "vs/editor/browser/editorBrowser", "vs/editor/browser/editorExtensions", "vs/editor/browser/services/codeEditorService", "vs/editor/common/core/range", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/common/model", "vs/editor/common/services/languageFeatures", "vs/editor/contrib/wordHighlighter/browser/highlightDecorations", "vs/platform/contextkey/common/contextkey", "vs/base/common/network", "vs/base/common/map", "vs/editor/common/languageSelector", "vs/base/common/resources", "vs/editor/contrib/wordHighlighter/browser/textualHighlightProvider", "vs/editor/common/editorFeatures"], "vs/editor/contrib/wordOperations/browser/wordOperations": ["require", "exports", "vs/editor/browser/editorExtensions", "vs/editor/common/commands/replaceCommand", "vs/editor/common/config/editorOptions", "vs/editor/common/cursor<PERSON><PERSON>mon", "vs/editor/common/cursor/cursorWordOperations", "vs/editor/common/core/wordCharacterClassifier", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/common/languages/languageConfigurationRegistry", "vs/nls", "vs/platform/accessibility/common/accessibility", "vs/platform/contextkey/common/contextkey", "vs/platform/contextkey/common/contextkeys"], "vs/editor/contrib/wordPartOperations/browser/wordPartOperations": ["require", "exports", "vs/editor/browser/editorExtensions", "vs/editor/common/cursor/cursorWordOperations", "vs/editor/common/core/range", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/contrib/wordOperations/browser/wordOperations", "vs/platform/commands/common/commands"], "vs/editor/contrib/readOnlyMessage/browser/contribution": ["require", "exports", "vs/base/common/htmlContent", "vs/base/common/lifecycle", "vs/editor/browser/editorExtensions", "vs/editor/contrib/message/browser/messageController", "vs/nls"], "vs/editor/contrib/diffEditorBreadcrumbs/browser/contribution": ["require", "exports", "vs/base/common/arrays", "vs/base/common/observable", "vs/editor/browser/widget/diffEditor/features/hideUnchangedRegionsFeature", "vs/editor/browser/widget/diffEditor/utils", "vs/editor/common/services/languageFeatures", "vs/editor/contrib/documentSymbols/browser/outlineModel", "vs/base/common/lifecycle", "vs/base/common/event"], "vs/editor/common/standaloneStrings": ["require", "exports", "vs/nls"], "vs/base/browser/ui/codicons/codiconStyles": ["require", "exports", "vs/css!vs/base/browser/ui/codicons/codicon/codicon", "vs/css!vs/base/browser/ui/codicons/codicon/codicon-modifiers"], "vs/editor/common/config/editorOptions": ["require", "exports", "vs/base/common/arrays", "vs/base/common/objects", "vs/base/common/platform", "vs/editor/common/core/textModelDefaults", "vs/editor/common/core/wordHelper", "vs/nls"], "vs/editor/standalone/browser/standaloneEditor": ["require", "exports", "vs/base/browser/window", "vs/base/common/lifecycle", "vs/base/common/strings", "vs/base/common/uri", "vs/editor/browser/config/fontMeasurements", "vs/editor/browser/editorExtensions", "vs/editor/browser/services/codeEditorService", "vs/editor/standalone/browser/standaloneWebWorker", "vs/editor/common/config/editorOptions", "vs/editor/common/config/editorZoom", "vs/editor/common/config/fontInfo", "vs/editor/common/editor<PERSON><PERSON><PERSON>", "vs/editor/common/languages", "vs/editor/common/languages/language", "vs/editor/common/languages/modesRegistry", "vs/editor/common/languages/nullTokenize", "vs/editor/common/model", "vs/editor/common/services/model", "vs/editor/common/standalone/standaloneEnums", "vs/editor/standalone/browser/colorizer", "vs/editor/standalone/browser/standaloneCodeEditor", "vs/editor/standalone/browser/standaloneServices", "vs/editor/standalone/common/standaloneTheme", "vs/platform/actions/common/actions", "vs/platform/commands/common/commands", "vs/platform/contextkey/common/contextkey", "vs/platform/keybinding/common/keybinding", "vs/platform/markers/common/markers", "vs/platform/opener/common/opener", "vs/editor/browser/widget/multiDiffEditor/multiDiffEditorWidget", "vs/css!vs/editor/standalone/browser/standalone-tokens"], "vs/editor/standalone/browser/standaloneLanguages": ["require", "exports", "vs/base/common/color", "vs/editor/common/core/range", "vs/editor/common/languages", "vs/editor/common/languages/language", "vs/editor/common/languages/languageConfigurationRegistry", "vs/editor/common/languages/modesRegistry", "vs/editor/common/services/languageFeatures", "vs/editor/common/standalone/standaloneEnums", "vs/editor/standalone/browser/standaloneServices", "vs/editor/standalone/common/monarch/monarchCompile", "vs/editor/standalone/common/monarch/monarchLexer", "vs/editor/standalone/common/standaloneTheme", "vs/platform/configuration/common/configuration", "vs/platform/markers/common/markers"], "vs/editor/contrib/format/browser/format": ["require", "exports", "vs/base/common/arrays", "vs/base/common/cancellation", "vs/base/common/errors", "vs/base/common/iterator", "vs/base/common/linkedList", "vs/base/common/types", "vs/base/common/uri", "vs/editor/contrib/editorState/browser/editorState", "vs/editor/browser/editorBrowser", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/editor/common/services/editorW<PERSON>ker", "vs/editor/common/services/resolverService", "vs/editor/contrib/format/browser/formattingEdit", "vs/platform/commands/common/commands", "vs/platform/extensions/common/extensions", "vs/platform/instantiation/common/instantiation", "vs/editor/common/services/languageFeatures", "vs/platform/log/common/log", "vs/platform/accessibilitySignal/browser/accessibilitySignalService"], "vs/base/browser/dom": ["require", "exports", "vs/base/browser/browser", "vs/base/browser/canIUse", "vs/base/browser/keyboardEvent", "vs/base/browser/mouseEvent", "vs/base/common/async", "vs/base/common/errors", "vs/base/common/event", "vs/base/browser/dompurify/dompurify", "vs/base/common/lifecycle", "vs/base/common/network", "vs/base/common/platform", "vs/base/common/hash", "vs/base/browser/window"], "vs/editor/browser/editorExtensions": ["require", "exports", "vs/nls", "vs/base/common/uri", "vs/editor/browser/services/codeEditorService", "vs/editor/common/core/position", "vs/editor/common/services/model", "vs/editor/common/services/resolverService", "vs/platform/actions/common/actions", "vs/platform/commands/common/commands", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation", "vs/platform/keybinding/common/keybindingsRegistry", "vs/platform/registry/common/platform", "vs/platform/telemetry/common/telemetry", "vs/base/common/types", "vs/platform/log/common/log", "vs/base/browser/dom"], "vs/css!vs/editor/standalone/browser/iPadShowKeyboard/iPadShowKeyboard": [], "vs/base/common/color": ["require", "exports"], "vs/editor/common/languages": ["require", "exports", "vs/base/common/codicons", "vs/base/common/uri", "vs/editor/common/core/range", "vs/editor/common/tokenizationRegistry", "vs/nls"], "vs/editor/common/encodedTokenAttributes": ["require", "exports"], "vs/editor/common/languages/nullTokenize": ["require", "exports", "vs/editor/common/languages"], "vs/editor/common/languages/language": ["require", "exports", "vs/platform/instantiation/common/instantiation"], "vs/editor/standalone/common/standaloneTheme": ["require", "exports", "vs/platform/instantiation/common/instantiation"], "vs/css!vs/editor/standalone/browser/inspectTokens/inspectTokens": [], "vs/platform/registry/common/platform": ["require", "exports", "vs/base/common/assert", "vs/base/common/types"], "vs/platform/quickinput/common/quickAccess": ["require", "exports", "vs/base/common/arrays", "vs/base/common/lifecycle", "vs/platform/registry/common/platform"], "vs/platform/quickinput/browser/helpQuickAccess": ["require", "exports", "vs/nls", "vs/platform/registry/common/platform", "vs/base/common/lifecycle", "vs/platform/keybinding/common/keybinding", "vs/platform/quickinput/common/quickAccess", "vs/platform/quickinput/common/quickInput"], "vs/editor/contrib/quickAccess/browser/gotoSymbolQuickAccess": ["require", "exports", "vs/base/common/async", "vs/base/common/cancellation", "vs/base/common/codicons", "vs/base/common/themables", "vs/base/common/fuzzyScorer", "vs/base/common/lifecycle", "vs/base/common/strings", "vs/editor/common/core/range", "vs/editor/common/languages", "vs/editor/contrib/documentSymbols/browser/outlineModel", "vs/editor/contrib/quickAccess/browser/editorNavigationQuickAccess", "vs/nls", "vs/editor/common/services/languageFeatures", "vs/base/common/arraysFind"], "vs/editor/browser/services/codeEditorService": ["require", "exports", "vs/platform/instantiation/common/instantiation"], "vs/editor/common/editorContextKeys": ["require", "exports", "vs/nls", "vs/platform/contextkey/common/contextkey"], "vs/platform/quickinput/common/quickInput": ["require", "exports", "vs/platform/instantiation/common/instantiation"], "vs/editor/contrib/documentSymbols/browser/outlineModel": ["require", "exports", "vs/base/common/arrays", "vs/base/common/cancellation", "vs/base/common/errors", "vs/base/common/iterator", "vs/base/common/map", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/services/languageFeatureDebounce", "vs/platform/instantiation/common/instantiation", "vs/platform/instantiation/common/extensions", "vs/editor/common/services/model", "vs/base/common/lifecycle", "vs/editor/common/services/languageFeatures"], "vs/editor/common/services/languageFeatures": ["require", "exports", "vs/platform/instantiation/common/instantiation"], "vs/editor/contrib/symbolIcons/browser/symbolIcons": ["require", "exports", "vs/nls", "vs/platform/theme/common/colorRegistry", "vs/css!vs/editor/contrib/symbolIcons/browser/symbolIcons"], "vs/editor/contrib/quickAccess/browser/commandsQuickAccess": ["require", "exports", "vs/base/common/iconLabels", "vs/platform/action/common/action", "vs/platform/quickinput/browser/commandsQuickAccess"], "vs/platform/instantiation/common/instantiation": ["require", "exports"], "vs/platform/keybinding/common/keybinding": ["require", "exports", "vs/platform/instantiation/common/instantiation"], "vs/platform/commands/common/commands": ["require", "exports", "vs/base/common/event", "vs/base/common/iterator", "vs/base/common/lifecycle", "vs/base/common/linkedList", "vs/base/common/types", "vs/platform/instantiation/common/instantiation"], "vs/platform/telemetry/common/telemetry": ["require", "exports", "vs/platform/instantiation/common/instantiation"], "vs/platform/dialogs/common/dialogs": ["require", "exports", "vs/platform/instantiation/common/instantiation"], "vs/editor/contrib/quickAccess/browser/gotoLineQuickAccess": ["require", "exports", "vs/base/common/lifecycle", "vs/editor/browser/editorBrowser", "vs/editor/contrib/quickAccess/browser/editorNavigationQuickAccess", "vs/nls"], "vs/editor/contrib/gotoSymbol/browser/peek/referencesController": ["require", "exports", "vs/base/common/async", "vs/base/common/errors", "vs/base/common/keyCodes", "vs/base/common/lifecycle", "vs/editor/browser/services/codeEditorService", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/contrib/peekView/browser/peekView", "vs/nls", "vs/platform/commands/common/commands", "vs/platform/configuration/common/configuration", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation", "vs/platform/keybinding/common/keybindingsRegistry", "vs/platform/list/browser/listService", "vs/platform/notification/common/notification", "vs/platform/storage/common/storage", "vs/editor/contrib/gotoSymbol/browser/referencesModel", "vs/editor/contrib/gotoSymbol/browser/peek/referencesWidget", "vs/editor/common/editorContext<PERSON>eys", "vs/platform/contextkey/common/contextkeys"], "vs/platform/configuration/common/configuration": ["require", "exports", "vs/platform/instantiation/common/instantiation"], "vs/platform/contextkey/common/contextkey": ["require", "exports", "vs/base/common/platform", "vs/base/common/strings", "vs/platform/contextkey/common/scanner", "vs/platform/instantiation/common/instantiation", "vs/nls"], "vs/platform/notification/common/notification": ["require", "exports", "vs/base/common/severity", "vs/platform/instantiation/common/instantiation"], "vs/platform/storage/common/storage": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/types", "vs/base/parts/storage/common/storage", "vs/platform/instantiation/common/instantiation"], "vs/platform/theme/common/theme": ["require", "exports"], "vs/editor/standalone/browser/standaloneThemeService": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/browser", "vs/base/common/color", "vs/base/common/event", "vs/editor/common/languages", "vs/editor/common/encodedTokenAttributes", "vs/editor/common/languages/supports/tokenization", "vs/editor/standalone/common/themes", "vs/platform/registry/common/platform", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/themeService", "vs/base/common/lifecycle", "vs/platform/theme/common/theme", "vs/platform/theme/browser/iconsStyleSheet", "vs/base/browser/window"], "vs/base/common/functional": ["require", "exports"], "vs/base/common/iterator": ["require", "exports"], "vs/base/common/uri": ["require", "exports", "vs/base/common/path", "vs/base/common/platform"], "vs/base/common/path": ["require", "exports", "vs/base/common/process"], "vs/nls": ["require", "exports", "vs/nls.messages", "vs/nls.messages"], "vs/base/common/linkedList": ["require", "exports"], "vs/base/common/cache": ["require", "exports"], "vs/base/common/lazy": ["require", "exports"], "vs/base/common/diff/diffChange": ["require", "exports"], "vs/base/common/hash": ["require", "exports", "vs/base/common/strings"], "vs/editor/common/core/characterClassifier": ["require", "exports", "vs/base/common/uint"], "vs/editor/common/core/position": ["require", "exports"], "vs/base/common/cancellation": ["require", "exports", "vs/base/common/event"], "vs/base/common/keyCodes": ["require", "exports"], "vs/editor/common/core/selection": ["require", "exports", "vs/editor/common/core/position", "vs/editor/common/core/range"], "vs/editor/common/standalone/standaloneEnums": ["require", "exports"], "vs/editor/common/model/textModelSearch": ["require", "exports", "vs/base/common/strings", "vs/editor/common/core/wordCharacterClassifier", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/model"], "vs/base/common/assert": ["require", "exports", "vs/base/common/errors"], "vs/editor/common/core/wordHelper": ["require", "exports", "vs/base/common/iterator", "vs/base/common/linkedList"], "vs/editor/common/diff/legacyLinesDiffComputer": ["require", "exports", "vs/base/common/diff/diff", "vs/editor/common/diff/linesDiffComputer", "vs/editor/common/diff/rangeMapping", "vs/base/common/strings", "vs/editor/common/core/range", "vs/base/common/assert", "vs/editor/common/core/lineRange"], "vs/editor/common/diff/defaultLinesDiffComputer/defaultLinesDiffComputer": ["require", "exports", "vs/base/common/arrays", "vs/base/common/assert", "vs/editor/common/core/lineRange", "vs/editor/common/core/offsetRange", "vs/editor/common/core/range", "vs/editor/common/diff/defaultLinesDiffComputer/algorithms/diffAlgorithm", "vs/editor/common/diff/defaultLinesDiffComputer/algorithms/dynamicProgrammingDiffing", "vs/editor/common/diff/defaultLinesDiffComputer/algorithms/myersDiffAlgorithm", "vs/editor/common/diff/defaultLinesDiffComputer/computeMovedLines", "vs/editor/common/diff/defaultLinesDiffComputer/heuristicSequenceOptimizations", "vs/editor/common/diff/defaultLinesDiffComputer/lineSequence", "vs/editor/common/diff/defaultLinesDiffComputer/linesSliceCharSequence", "vs/editor/common/diff/linesDiffComputer", "vs/editor/common/diff/rangeMapping"], "vs/base/common/types": ["require", "exports"], "vs/base/common/async": ["require", "exports", "vs/base/common/cancellation", "vs/base/common/errors", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/platform", "vs/base/common/symbols"], "vs/editor/common/model/mirrorTextModel": ["require", "exports", "vs/base/common/strings", "vs/editor/common/core/position", "vs/editor/common/model/prefixSumComputer"], "vs/editor/browser/config/domFontInfo": ["require", "exports", "vs/base/browser/fastDomNode"], "vs/editor/browser/config/editorConfiguration": ["require", "exports", "vs/base/browser/browser", "vs/base/common/arrays", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/objects", "vs/base/common/platform", "vs/editor/browser/config/elementSizeObserver", "vs/editor/browser/config/fontMeasurements", "vs/editor/browser/config/migrateOptions", "vs/editor/browser/config/tabFocus", "vs/editor/common/config/editorOptions", "vs/editor/common/config/editorZoom", "vs/editor/common/config/fontInfo", "vs/platform/accessibility/common/accessibility", "vs/base/browser/dom", "vs/base/browser/pixelRatio"], "vs/editor/browser/config/tabFocus": ["require", "exports", "vs/base/common/event"], "vs/editor/browser/view": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/fastDomNode", "vs/base/browser/performance", "vs/base/common/errors", "vs/editor/browser/controller/mouseTarget", "vs/editor/browser/controller/pointerHandler", "vs/editor/browser/controller/text<PERSON><PERSON>Hand<PERSON>", "vs/editor/browser/view/renderingContext", "vs/editor/browser/view/viewController", "vs/editor/browser/view/viewOverlays", "vs/editor/browser/view/viewPart", "vs/editor/browser/view/viewUserInputEvents", "vs/editor/browser/viewParts/blockDecorations/blockDecorations", "vs/editor/browser/viewParts/contentWidgets/contentWidgets", "vs/editor/browser/viewParts/currentLineHighlight/currentLineHighlight", "vs/editor/browser/viewParts/decorations/decorations", "vs/editor/browser/viewParts/editorScrollbar/editorScrollbar", "vs/editor/browser/viewParts/glyphMargin/glyphMargin", "vs/editor/browser/viewParts/indentGuides/indentGuides", "vs/editor/browser/viewParts/lineNumbers/lineNumbers", "vs/editor/browser/viewParts/lines/viewLines", "vs/editor/browser/viewParts/linesDecorations/linesDecorations", "vs/editor/browser/viewParts/margin/margin", "vs/editor/browser/viewParts/marginDecorations/marginDecorations", "vs/editor/browser/viewParts/minimap/minimap", "vs/editor/browser/viewParts/overlayWidgets/overlayWidgets", "vs/editor/browser/viewParts/overviewRuler/decorationsOverviewRuler", "vs/editor/browser/viewParts/overviewRuler/overviewRuler", "vs/editor/browser/viewParts/rulers/rulers", "vs/editor/browser/viewParts/scrollDecoration/scrollDecoration", "vs/editor/browser/viewParts/selections/selections", "vs/editor/browser/viewParts/viewCursors/viewCursors", "vs/editor/browser/viewParts/viewZones/viewZones", "vs/editor/browser/viewParts/whitespace/whitespace", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/editor/common/model", "vs/editor/common/viewEventHandler", "vs/editor/common/viewLayout/viewLinesViewportData", "vs/editor/common/viewModel/viewContext", "vs/platform/instantiation/common/instantiation", "vs/platform/theme/common/themeService"], "vs/editor/browser/view/domLineBreaksComputer": ["require", "exports", "vs/base/browser/trustedTypes", "vs/base/common/strings", "vs/base/common/types", "vs/editor/browser/config/domFontInfo", "vs/editor/common/core/stringBuilder", "vs/editor/common/modelLineProjectionData", "vs/editor/common/textModelEvents"], "vs/editor/browser/view/viewUserInputEvents": ["require", "exports", "vs/editor/common/core/position"], "vs/editor/browser/widget/codeEditor/codeEditorContributions": ["require", "exports", "vs/base/browser/dom", "vs/base/common/errors", "vs/base/common/lifecycle"], "vs/editor/common/core/cursorColumns": ["require", "exports", "vs/base/common/strings"], "vs/editor/common/core/editorColorRegistry": ["require", "exports", "vs/nls", "vs/base/common/color", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/themeService"], "vs/editor/common/cursor/cursorWordOperations": ["require", "exports", "vs/base/common/strings", "vs/editor/common/cursor<PERSON><PERSON>mon", "vs/editor/common/cursor/cursorDeleteOperations", "vs/editor/common/core/wordCharacterClassifier", "vs/editor/common/core/position", "vs/editor/common/core/range"], "vs/editor/common/editorAction": ["require", "exports"], "vs/editor/common/editorCommon": ["require", "exports"], "vs/editor/common/languages/languageConfigurationRegistry": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/strings", "vs/editor/common/core/wordHelper", "vs/editor/common/languages/languageConfiguration", "vs/editor/common/languages/supports/characterPair", "vs/editor/common/languages/supports/electricCharacter", "vs/editor/common/languages/supports/indentRules", "vs/editor/common/languages/supports/onEnter", "vs/editor/common/languages/supports/richEditBrackets", "vs/platform/instantiation/common/instantiation", "vs/platform/configuration/common/configuration", "vs/editor/common/languages/language", "vs/platform/instantiation/common/extensions", "vs/editor/common/languages/modesRegistry", "vs/editor/common/languages/supports/languageBracketsConfiguration"], "vs/editor/common/model/textModel": ["require", "exports", "vs/base/common/arrays", "vs/base/common/color", "vs/base/common/errors", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/strings", "vs/base/common/uri", "vs/editor/common/core/eolCounter", "vs/editor/common/core/indentation", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/editor/common/core/textModelDefaults", "vs/editor/common/languages/language", "vs/editor/common/languages/languageConfigurationRegistry", "vs/editor/common/model", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsImpl", "vs/editor/common/model/bracketPairsTextModelPart/colorizedBracketPairsDecorationProvider", "vs/editor/common/model/editStack", "vs/editor/common/model/guidesTextModelPart", "vs/editor/common/model/indentationG<PERSON>ser", "vs/editor/common/model/intervalTree", "vs/editor/common/model/pieceTreeTextBuffer/pieceTreeTextBuffer", "vs/editor/common/model/pieceTreeTextBuffer/pieceTreeTextBufferBuilder", "vs/editor/common/model/textModelSearch", "vs/editor/common/model/tokenizationTextModelPart", "vs/editor/common/model/tokens", "vs/editor/common/textModelEvents", "vs/platform/instantiation/common/instantiation", "vs/platform/undoRedo/common/undoRedo"], "vs/editor/common/viewModel/monospaceLineBreaksComputer": ["require", "exports", "vs/base/common/strings", "vs/editor/common/core/characterClassifier", "vs/editor/common/textModelEvents", "vs/editor/common/modelLineProjectionData"], "vs/editor/common/viewModel/viewModelImpl": ["require", "exports", "vs/base/common/arrays", "vs/base/common/async", "vs/base/common/color", "vs/base/common/lifecycle", "vs/base/common/platform", "vs/base/common/strings", "vs/editor/common/config/editorOptions", "vs/editor/common/cursor/cursor", "vs/editor/common/cursor<PERSON><PERSON>mon", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/textModelEvents", "vs/editor/common/languages", "vs/editor/common/languages/modesRegistry", "vs/editor/common/languages/textToHtmlTokenizer", "vs/editor/common/viewEvents", "vs/editor/common/viewLayout/viewLayout", "vs/editor/common/viewModel/minimapTokensColorTracker", "vs/editor/common/viewModel", "vs/editor/common/viewModel/viewModelDecorations", "vs/editor/common/viewModelEventDispatcher", "vs/editor/common/viewModel/viewModelLines", "vs/editor/common/viewModel/glyphLanesModel"], "vs/platform/accessibility/common/accessibility": ["require", "exports", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation"], "vs/platform/instantiation/common/serviceCollection": ["require", "exports"], "vs/platform/theme/common/colorRegistry": ["require", "exports", "vs/platform/theme/common/colorUtils", "vs/platform/theme/common/colors/baseColors", "vs/platform/theme/common/colors/chartsColors", "vs/platform/theme/common/colors/editorColors", "vs/platform/theme/common/colors/inputColors", "vs/platform/theme/common/colors/listColors", "vs/platform/theme/common/colors/menuColors", "vs/platform/theme/common/colors/minimapColors", "vs/platform/theme/common/colors/miscColors", "vs/platform/theme/common/colors/quickpickColors", "vs/platform/theme/common/colors/searchColors"], "vs/platform/theme/common/themeService": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle", "vs/platform/instantiation/common/instantiation", "vs/platform/registry/common/platform", "vs/platform/theme/common/theme"], "vs/platform/actions/common/actions": ["require", "exports", "vs/base/common/actions", "vs/base/common/themables", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/linkedList", "vs/platform/commands/common/commands", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation", "vs/platform/keybinding/common/keybindingsRegistry"], "vs/editor/browser/services/markerDecorations": ["require", "exports", "vs/editor/common/services/markerDecorations", "vs/editor/browser/editorExtensions"], "vs/css!vs/editor/browser/widget/codeEditor/editor": [], "vs/base/browser/browser": ["require", "exports", "vs/base/browser/window"], "vs/base/browser/ui/aria/aria": ["require", "exports", "vs/base/browser/dom", "vs/css!vs/base/browser/ui/aria/aria"], "vs/editor/common/cursor/cursorColumnSelection": ["require", "exports", "vs/editor/common/cursor<PERSON><PERSON>mon", "vs/editor/common/core/position", "vs/editor/common/core/range"], "vs/editor/common/cursorCommon": ["require", "exports", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/editor/common/languages/supports", "vs/editor/common/core/cursorColumns", "vs/editor/common/core/indentation"], "vs/editor/common/cursor/cursorDeleteOperations": ["require", "exports", "vs/base/common/strings", "vs/editor/common/commands/replaceCommand", "vs/editor/common/cursor<PERSON><PERSON>mon", "vs/editor/common/core/cursorColumns", "vs/editor/common/cursor/cursorMoveOperations", "vs/editor/common/core/range", "vs/editor/common/core/position"], "vs/editor/common/cursor/cursorMoveCommands": ["require", "exports", "vs/base/common/types", "vs/editor/common/cursor<PERSON><PERSON>mon", "vs/editor/common/cursor/cursorMoveOperations", "vs/editor/common/cursor/cursorWordOperations", "vs/editor/common/core/position", "vs/editor/common/core/range"], "vs/editor/common/cursor/cursorTypeOperations": ["require", "exports", "vs/editor/common/commands/shiftCommand", "vs/editor/common/commands/surroundSelectionCommand", "vs/editor/common/cursor<PERSON><PERSON>mon", "vs/editor/common/cursor/cursorTypeEditOperations"], "vs/platform/keybinding/common/keybindingsRegistry": ["require", "exports", "vs/base/common/keybindings", "vs/base/common/platform", "vs/platform/commands/common/commands", "vs/platform/registry/common/platform", "vs/base/common/lifecycle", "vs/base/common/linkedList"], "vs/editor/common/cursor/cursorTypeEditOperations": ["require", "exports", "vs/base/common/errors", "vs/base/common/strings", "vs/editor/common/commands/replaceCommand", "vs/editor/common/commands/shiftCommand", "vs/editor/common/commands/surroundSelectionCommand", "vs/editor/common/cursor<PERSON><PERSON>mon", "vs/editor/common/core/wordCharacterClassifier", "vs/editor/common/core/range", "vs/editor/common/core/position", "vs/editor/common/languages/languageConfiguration", "vs/editor/common/languages/languageConfigurationRegistry", "vs/editor/common/languages/supports", "vs/editor/common/languages/autoIndent", "vs/editor/common/languages/enterAction"], "vs/base/common/codicons": ["require", "exports", "vs/base/common/codiconsUtil", "vs/base/common/codiconsLibrary"], "vs/editor/browser/widget/diffEditor/commands": ["require", "exports", "vs/base/browser/dom", "vs/base/common/codicons", "vs/editor/browser/editorExtensions", "vs/editor/browser/services/codeEditorService", "vs/editor/browser/widget/diffEditor/diffEditorWidget", "vs/editor/common/editorContext<PERSON>eys", "vs/nls", "vs/platform/actions/common/actions", "vs/platform/configuration/common/configuration", "vs/platform/contextkey/common/contextkey", "vs/editor/browser/widget/diffEditor/registrations.contribution"], "vs/editor/browser/widget/diffEditor/registrations.contribution": ["require", "exports", "vs/base/common/codicons", "vs/base/common/themables", "vs/editor/common/model/textModel", "vs/nls", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/iconRegistry"], "vs/base/common/htmlContent": ["require", "exports", "vs/base/common/errors", "vs/base/common/iconLabels", "vs/base/common/resources", "vs/base/common/strings", "vs/base/common/uri"], "vs/css!vs/editor/contrib/anchorSelect/browser/anchorSelect": [], "vs/editor/contrib/caretOperations/browser/moveCaretCommand": ["require", "exports", "vs/editor/common/core/range", "vs/editor/common/core/selection"], "vs/editor/common/model": ["require", "exports", "vs/base/common/objects"], "vs/css!vs/editor/contrib/bracketMatching/browser/bracketMatching": [], "vs/editor/common/commands/replaceCommand": ["require", "exports", "vs/editor/common/core/selection"], "vs/editor/common/cursor/cursorMoveOperations": ["require", "exports", "vs/base/common/strings", "vs/editor/common/core/cursorColumns", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/cursor/cursorAtomicMoveOperations", "vs/editor/common/cursor<PERSON><PERSON>mon"], "vs/editor/browser/controller/textAreaInput": ["require", "exports", "vs/base/browser/browser", "vs/base/browser/dom", "vs/base/browser/event", "vs/base/browser/keyboardEvent", "vs/base/browser/performance", "vs/base/common/async", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/mime", "vs/base/common/strings", "vs/editor/browser/controller/textAreaState", "vs/editor/common/core/selection", "vs/platform/accessibility/common/accessibility", "vs/platform/log/common/log"], "vs/editor/contrib/dropOrPasteInto/browser/copyPasteController": ["require", "exports", "vs/base/browser/dom", "vs/base/common/arrays", "vs/base/common/async", "vs/base/common/cancellation", "vs/base/common/dataTransfer", "vs/base/common/hierarchicalKind", "vs/base/common/lifecycle", "vs/base/common/mime", "vs/base/common/platform", "vs/base/common/uuid", "vs/editor/browser/controller/textAreaInput", "vs/editor/browser/dnd", "vs/editor/browser/services/bulkEditService", "vs/editor/common/core/range", "vs/editor/common/languages", "vs/editor/common/services/languageFeatures", "vs/editor/contrib/dropOrPasteInto/browser/defaultProviders", "vs/editor/contrib/dropOrPasteInto/browser/edit", "vs/editor/contrib/editorState/browser/editorState", "vs/editor/contrib/inlineProgress/browser/inlineProgress", "vs/editor/contrib/message/browser/messageController", "vs/nls", "vs/platform/clipboard/common/clipboardService", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation", "vs/platform/progress/common/progress", "vs/platform/quickinput/common/quickInput", "vs/editor/contrib/dropOrPasteInto/browser/postEditWidget", "vs/base/common/errors"], "vs/platform/clipboard/common/clipboardService": ["require", "exports", "vs/platform/instantiation/common/instantiation"], "vs/editor/common/config/editorConfigurationSchema": ["require", "exports", "vs/editor/common/config/diffEditor", "vs/editor/common/config/editorOptions", "vs/editor/common/core/textModelDefaults", "vs/nls", "vs/platform/configuration/common/configurationRegistry", "vs/platform/registry/common/platform"], "vs/editor/contrib/codeAction/browser/codeActionCommands": ["require", "exports", "vs/base/common/hierarchicalKind", "vs/base/common/strings", "vs/editor/browser/editorExtensions", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/contrib/codeAction/browser/codeAction", "vs/nls", "vs/platform/contextkey/common/contextkey", "vs/editor/contrib/codeAction/common/types", "vs/editor/contrib/codeAction/browser/codeActionController", "vs/editor/contrib/codeAction/browser/codeActionModel"], "vs/editor/contrib/codeAction/browser/codeActionController": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/aria/aria", "vs/base/common/errors", "vs/base/common/lazy", "vs/base/common/lifecycle", "vs/editor/common/core/position", "vs/editor/common/model/textModel", "vs/editor/common/services/languageFeatures", "vs/editor/contrib/codeAction/browser/codeAction", "vs/editor/contrib/codeAction/browser/codeActionKeybindingResolver", "vs/editor/contrib/codeAction/browser/codeActionMenu", "vs/editor/contrib/codeAction/browser/lightBulbWidget", "vs/editor/contrib/message/browser/messageController", "vs/nls", "vs/platform/actionWidget/browser/actionWidget", "vs/platform/commands/common/commands", "vs/platform/configuration/common/configuration", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation", "vs/platform/markers/common/markers", "vs/platform/progress/common/progress", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/theme", "vs/platform/theme/common/themeService", "vs/editor/contrib/codeAction/common/types", "vs/editor/contrib/codeAction/browser/codeActionModel", "vs/base/common/hierarchicalKind", "vs/platform/telemetry/common/telemetry"], "vs/editor/contrib/codeAction/browser/lightBulbWidget": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/touch", "vs/base/common/codicons", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/themables", "vs/editor/common/model", "vs/editor/common/model/textModel", "vs/editor/common/model/utils", "vs/editor/contrib/codeAction/browser/codeAction", "vs/nls", "vs/platform/keybinding/common/keybinding", "vs/platform/theme/common/iconRegistry", "vs/editor/common/core/range", "vs/css!vs/editor/contrib/codeAction/browser/lightBulbWidget"], "vs/platform/configuration/common/configurationRegistry": ["require", "exports", "vs/base/common/arrays", "vs/base/common/event", "vs/base/common/types", "vs/nls", "vs/platform/configuration/common/configuration", "vs/platform/jsonschemas/common/jsonContributionRegistry", "vs/platform/registry/common/platform"], "vs/editor/contrib/colorPicker/browser/colorDetector": ["require", "exports", "vs/base/common/async", "vs/base/common/color", "vs/base/common/errors", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/stopwatch", "vs/base/common/strings", "vs/editor/browser/editorDom", "vs/editor/browser/editorExtensions", "vs/editor/common/core/range", "vs/editor/common/model/textModel", "vs/editor/common/services/languageFeatureDebounce", "vs/editor/common/services/languageFeatures", "vs/editor/contrib/colorPicker/browser/color", "vs/platform/configuration/common/configuration"], "vs/editor/contrib/colorPicker/browser/colorHoverParticipant": ["require", "exports", "vs/base/common/async", "vs/base/common/cancellation", "vs/base/common/color", "vs/base/common/lifecycle", "vs/editor/common/core/range", "vs/editor/contrib/colorPicker/browser/color", "vs/editor/contrib/colorPicker/browser/colorDetector", "vs/editor/contrib/colorPicker/browser/colorPickerModel", "vs/editor/contrib/colorPicker/browser/colorPickerWidget", "vs/editor/contrib/hover/browser/hoverTypes", "vs/platform/theme/common/themeService", "vs/base/browser/dom"], "vs/editor/contrib/hover/browser/contentHoverController2": ["require", "exports", "vs/editor/contrib/hover/browser/hoverActionIds", "vs/base/common/lifecycle", "vs/platform/instantiation/common/instantiation", "vs/editor/contrib/inlineCompletions/browser/hintsWidget/inlineCompletionsHintsWidget", "vs/platform/keybinding/common/keybinding", "vs/base/common/async", "vs/editor/contrib/hover/browser/hoverUtils", "vs/editor/contrib/hover/browser/contentHoverWidgetWrapper", "vs/base/common/event", "vs/css!vs/editor/contrib/hover/browser/hover"], "vs/editor/contrib/hover/browser/hoverTypes": ["require", "exports"], "vs/editor/browser/stableEditorScroll": ["require", "exports"], "vs/editor/contrib/codelens/browser/codelens": ["require", "exports", "vs/base/common/cancellation", "vs/base/common/errors", "vs/base/common/lifecycle", "vs/base/common/types", "vs/base/common/uri", "vs/editor/common/services/model", "vs/platform/commands/common/commands", "vs/editor/common/services/languageFeatures"], "vs/editor/contrib/codelens/browser/codeLensCache": ["require", "exports", "vs/base/common/event", "vs/base/common/map", "vs/editor/common/core/range", "vs/editor/contrib/codelens/browser/codelens", "vs/platform/instantiation/common/extensions", "vs/platform/instantiation/common/instantiation", "vs/platform/storage/common/storage", "vs/base/browser/window", "vs/base/browser/dom"], "vs/editor/contrib/codelens/browser/codelensWidget": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/iconLabel/iconLabels", "vs/editor/common/core/range", "vs/editor/common/model/textModel", "vs/css!vs/editor/contrib/codelens/browser/codelensWidget"], "vs/editor/common/services/languageFeatureDebounce": ["require", "exports", "vs/base/common/hash", "vs/base/common/map", "vs/base/common/numbers", "vs/platform/environment/common/environment", "vs/platform/instantiation/common/extensions", "vs/platform/instantiation/common/instantiation", "vs/platform/log/common/log", "vs/base/common/network"], "vs/editor/contrib/colorPicker/browser/standaloneColorPickerWidget": ["require", "exports", "vs/base/common/lifecycle", "vs/editor/contrib/colorPicker/browser/colorHoverParticipant", "vs/platform/instantiation/common/instantiation", "vs/editor/contrib/hover/browser/contentHoverStatusBar", "vs/platform/keybinding/common/keybinding", "vs/base/common/event", "vs/editor/common/services/languageFeatures", "vs/editor/browser/editorExtensions", "vs/editor/common/editorContext<PERSON>eys", "vs/platform/contextkey/common/contextkey", "vs/editor/contrib/colorPicker/browser/defaultDocumentColorProvider", "vs/base/browser/dom", "vs/editor/common/services/editorW<PERSON>ker", "vs/css!vs/editor/contrib/colorPicker/browser/colorPicker"], "vs/css!vs/editor/contrib/colorPicker/browser/colorPicker": [], "vs/editor/contrib/comment/browser/blockCommentCommand": ["require", "exports", "vs/editor/common/core/editOperation", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/core/selection"], "vs/editor/contrib/comment/browser/lineCommentCommand": ["require", "exports", "vs/base/common/strings", "vs/editor/common/core/editOperation", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/editor/contrib/comment/browser/blockCommentCommand"], "vs/base/browser/ui/actionbar/actionViewItems": ["require", "exports", "vs/base/browser/browser", "vs/base/browser/dnd", "vs/base/browser/dom", "vs/base/browser/touch", "vs/base/browser/ui/hover/hoverDelegateFactory", "vs/base/browser/ui/selectBox/selectBox", "vs/base/common/actions", "vs/base/common/lifecycle", "vs/base/common/platform", "vs/base/common/types", "vs/nls", "vs/base/browser/ui/hover/hoverDelegate2", "vs/css!vs/base/browser/ui/actionbar/actionbar"], "vs/base/common/actions": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle", "vs/nls"], "vs/platform/contextview/browser/contextView": ["require", "exports", "vs/platform/instantiation/common/instantiation"], "vs/platform/workspace/common/workspace": ["require", "exports", "vs/nls", "vs/base/common/path", "vs/base/common/ternarySearchTree", "vs/base/common/uri", "vs/platform/instantiation/common/instantiation"], "vs/base/common/hierarchicalKind": ["require", "exports"], "vs/editor/common/editorFeatures": ["require", "exports"], "vs/editor/contrib/dropOrPasteInto/browser/defaultProviders": ["require", "exports", "vs/base/common/arrays", "vs/base/common/dataTransfer", "vs/base/common/hierarchicalKind", "vs/base/common/lifecycle", "vs/base/common/mime", "vs/base/common/network", "vs/base/common/resources", "vs/base/common/uri", "vs/editor/common/languages", "vs/editor/common/services/languageFeatures", "vs/nls", "vs/platform/workspace/common/workspace"], "vs/editor/contrib/dnd/browser/dragAndDropCommand": ["require", "exports", "vs/editor/common/core/range", "vs/editor/common/core/selection"], "vs/css!vs/editor/contrib/dnd/browser/dnd": [], "vs/editor/contrib/dropOrPasteInto/browser/dropIntoEditorController": ["require", "exports", "vs/base/common/arrays", "vs/base/common/async", "vs/base/common/dataTransfer", "vs/base/common/hierarchicalKind", "vs/base/common/lifecycle", "vs/editor/browser/dnd", "vs/editor/common/core/range", "vs/editor/common/services/languageFeatures", "vs/editor/common/services/treeViewsDnd", "vs/editor/common/services/treeViewsDndService", "vs/editor/contrib/editorState/browser/editorState", "vs/editor/contrib/inlineProgress/browser/inlineProgress", "vs/nls", "vs/platform/configuration/common/configuration", "vs/platform/contextkey/common/contextkey", "vs/platform/dnd/browser/dnd", "vs/platform/instantiation/common/instantiation", "vs/editor/contrib/dropOrPasteInto/browser/edit", "vs/editor/contrib/dropOrPasteInto/browser/postEditWidget"], "vs/editor/contrib/folding/browser/foldingModel": ["require", "exports", "vs/base/common/event", "vs/editor/contrib/folding/browser/foldingRanges", "vs/base/common/hash"], "vs/editor/contrib/folding/browser/hiddenRangeModel": ["require", "exports", "vs/base/common/arraysFind", "vs/base/common/event", "vs/editor/common/core/range", "vs/editor/common/core/eolCounter"], "vs/editor/contrib/folding/browser/indentRangeProvider": ["require", "exports", "vs/editor/common/model/utils", "vs/editor/contrib/folding/browser/foldingRanges"], "vs/editor/contrib/folding/browser/foldingDecorations": ["require", "exports", "vs/base/common/codicons", "vs/editor/common/model/textModel", "vs/nls", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/iconRegistry", "vs/platform/theme/common/themeService", "vs/base/common/themables"], "vs/editor/contrib/folding/browser/foldingRanges": ["require", "exports"], "vs/editor/contrib/folding/browser/syntaxRangeProvider": ["require", "exports", "vs/base/common/errors", "vs/base/common/lifecycle", "vs/editor/contrib/folding/browser/foldingRanges"], "vs/editor/common/services/model": ["require", "exports", "vs/platform/instantiation/common/instantiation"], "vs/css!vs/editor/contrib/folding/browser/folding": [], "vs/editor/contrib/find/browser/findModel": ["require", "exports", "vs/base/common/arraysFind", "vs/base/common/async", "vs/base/common/lifecycle", "vs/editor/common/commands/replaceCommand", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/editor/common/model/textModelSearch", "vs/editor/contrib/find/browser/findDecorations", "vs/editor/contrib/find/browser/replaceAllCommand", "vs/editor/contrib/find/browser/replacePattern", "vs/platform/contextkey/common/contextkey"], "vs/editor/contrib/find/browser/findOptionsWidget": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/findinput/findInputToggles", "vs/base/browser/ui/widget", "vs/base/common/async", "vs/editor/contrib/find/browser/findModel", "vs/platform/theme/common/colorRegistry", "vs/base/browser/ui/hover/hoverDelegateFactory", "vs/css!vs/editor/contrib/find/browser/findOptionsWidget"], "vs/editor/contrib/find/browser/findState": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle", "vs/editor/common/core/range", "vs/editor/contrib/find/browser/findModel"], "vs/editor/contrib/find/browser/findWidget": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/aria/aria", "vs/base/browser/ui/toggle/toggle", "vs/base/browser/ui/sash/sash", "vs/base/browser/ui/widget", "vs/base/common/async", "vs/base/common/codicons", "vs/base/common/errors", "vs/base/common/lifecycle", "vs/base/common/platform", "vs/base/common/strings", "vs/editor/common/core/range", "vs/editor/contrib/find/browser/findModel", "vs/nls", "vs/platform/history/browser/contextScopedHistoryWidget", "vs/platform/history/browser/historyWidgetKeybindingHint", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/iconRegistry", "vs/platform/theme/common/themeService", "vs/base/common/themables", "vs/platform/theme/common/theme", "vs/base/common/types", "vs/platform/theme/browser/defaultStyles", "vs/base/browser/ui/hover/hoverDelegateFactory", "vs/css!vs/editor/contrib/find/browser/findWidget"], "vs/platform/hover/browser/hover": ["require", "exports", "vs/platform/instantiation/common/instantiation", "vs/base/common/lifecycle", "vs/platform/configuration/common/configuration", "vs/base/browser/dom"], "vs/editor/common/config/editorZoom": ["require", "exports", "vs/base/common/event"], "vs/editor/common/services/resolverService": ["require", "exports", "vs/platform/instantiation/common/instantiation"], "vs/editor/contrib/inlineCompletions/browser/controller/commands": ["require", "exports", "vs/base/common/observable", "vs/base/common/observableInternal/base", "vs/editor/browser/editorExtensions", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/contrib/inlineCompletions/browser/controller/commandIds", "vs/editor/contrib/inlineCompletions/browser/controller/inlineCompletionContextKeys", "vs/editor/contrib/inlineCompletions/browser/controller/inlineCompletionsController", "vs/editor/contrib/suggest/browser/suggest", "vs/nls", "vs/platform/actions/common/actions", "vs/platform/configuration/common/configuration", "vs/platform/contextkey/common/contextkey"], "vs/editor/contrib/inlineCompletions/browser/hintsWidget/hoverParticipant": ["require", "exports", "vs/base/browser/dom", "vs/base/common/htmlContent", "vs/base/common/lifecycle", "vs/base/common/observable", "vs/editor/common/core/range", "vs/editor/common/languages/language", "vs/editor/contrib/hover/browser/hoverTypes", "vs/editor/contrib/inlineCompletions/browser/controller/inlineCompletionsController", "vs/editor/contrib/inlineCompletions/browser/hintsWidget/inlineCompletionsHintsWidget", "vs/editor/browser/widget/markdownRenderer/browser/markdownRenderer", "vs/nls", "vs/platform/accessibility/common/accessibility", "vs/platform/instantiation/common/instantiation", "vs/platform/opener/common/opener", "vs/platform/telemetry/common/telemetry"], "vs/editor/contrib/inlineCompletions/browser/inlineCompletionsAccessibleView": ["require", "exports"], "vs/editor/contrib/inlineCompletions/browser/controller/inlineCompletionsController": ["require", "exports", "vs/base/browser/domObservable", "vs/base/browser/ui/aria/aria", "vs/base/common/async", "vs/base/common/cancellation", "vs/base/common/lifecycle", "vs/base/common/observable", "vs/base/common/observableInternal/derived", "vs/base/common/observableInternal/utils", "vs/base/common/types", "vs/editor/browser/coreCommands", "vs/editor/browser/observableCodeEditor", "vs/editor/common/core/position", "vs/editor/common/services/languageFeatureDebounce", "vs/editor/common/services/languageFeatures", "vs/editor/contrib/inlineCompletions/browser/controller/commandIds", "vs/editor/contrib/inlineCompletions/browser/view/ghostTextView", "vs/editor/contrib/inlineCompletions/browser/controller/inlineCompletionContextKeys", "vs/editor/contrib/inlineCompletions/browser/hintsWidget/inlineCompletionsHintsWidget", "vs/editor/contrib/inlineCompletions/browser/model/inlineCompletionsModel", "vs/editor/contrib/inlineCompletions/browser/model/suggestWidgetAdaptor", "vs/nls", "vs/platform/accessibility/common/accessibility", "vs/platform/accessibilitySignal/browser/accessibilitySignalService", "vs/platform/commands/common/commands", "vs/platform/configuration/common/configuration", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation", "vs/platform/keybinding/common/keybinding"], "vs/platform/accessibility/browser/accessibleViewRegistry": ["require", "exports"], "vs/base/common/arrays": ["require", "exports"], "vs/editor/common/services/editorWorker": ["require", "exports", "vs/platform/instantiation/common/instantiation"], "vs/editor/contrib/format/browser/formattingEdit": ["require", "exports", "vs/editor/common/core/editOperation", "vs/editor/common/core/range", "vs/editor/browser/stableEditorScroll"], "vs/platform/accessibilitySignal/browser/accessibilitySignalService": ["require", "exports", "vs/nls", "vs/platform/instantiation/common/instantiation"], "vs/platform/progress/common/progress": ["require", "exports", "vs/platform/instantiation/common/instantiation"], "vs/base/common/themables": ["require", "exports", "vs/base/common/codicons"], "vs/css!vs/editor/contrib/inlineProgress/browser/inlineProgressWidget": [], "vs/editor/contrib/editorState/browser/editorState": ["require", "exports", "vs/base/common/strings", "vs/editor/common/core/range", "vs/base/common/cancellation", "vs/base/common/lifecycle", "vs/editor/contrib/editorState/browser/keybindingCancellation"], "vs/editor/browser/editorBrowser": ["require", "exports", "vs/editor/common/editor<PERSON><PERSON><PERSON>"], "vs/editor/browser/widget/codeEditor/embeddedCodeEditorWidget": ["require", "exports", "vs/base/common/objects", "vs/editor/browser/services/codeEditorService", "vs/editor/browser/widget/codeEditor/codeEditorWidget", "vs/editor/common/languages/languageConfigurationRegistry", "vs/editor/common/services/languageFeatures", "vs/platform/accessibility/common/accessibility", "vs/platform/commands/common/commands", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation", "vs/platform/notification/common/notification", "vs/platform/theme/common/themeService"], "vs/editor/contrib/gotoSymbol/browser/referencesModel": ["require", "exports", "vs/base/common/errors", "vs/base/common/event", "vs/base/common/idGenerator", "vs/base/common/lifecycle", "vs/base/common/map", "vs/base/common/resources", "vs/base/common/strings", "vs/editor/common/core/range", "vs/nls"], "vs/editor/contrib/gotoSymbol/browser/symbolNavigation": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/resources", "vs/editor/browser/editorExtensions", "vs/editor/browser/services/codeEditorService", "vs/editor/common/core/range", "vs/nls", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/extensions", "vs/platform/instantiation/common/instantiation", "vs/platform/keybinding/common/keybinding", "vs/platform/keybinding/common/keybindingsRegistry", "vs/platform/notification/common/notification"], "vs/editor/contrib/message/browser/messageController": ["require", "exports", "vs/base/browser/markdownRenderer", "vs/base/browser/ui/aria/aria", "vs/base/common/event", "vs/base/common/htmlContent", "vs/base/common/lifecycle", "vs/editor/browser/editorExtensions", "vs/editor/common/core/range", "vs/editor/browser/widget/markdownRenderer/browser/markdownRenderer", "vs/nls", "vs/platform/contextkey/common/contextkey", "vs/platform/opener/common/opener", "vs/base/browser/dom", "vs/css!vs/editor/contrib/message/browser/messageController"], "vs/editor/contrib/peekView/browser/peekView": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/actionbar/actionbar", "vs/base/common/actions", "vs/base/common/codicons", "vs/base/common/themables", "vs/base/common/color", "vs/base/common/event", "vs/base/common/objects", "vs/editor/browser/editorExtensions", "vs/editor/browser/services/codeEditorService", "vs/editor/browser/widget/codeEditor/embeddedCodeEditorWidget", "vs/editor/contrib/zoneWidget/browser/zoneWidget", "vs/nls", "vs/platform/actions/browser/menuEntryActionViewItem", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/extensions", "vs/platform/instantiation/common/instantiation", "vs/platform/theme/common/colorRegistry", "vs/css!vs/editor/contrib/peekView/browser/media/peekViewWidget"], "vs/editor/contrib/gotoSymbol/browser/goToSymbol": ["require", "exports", "vs/base/common/arrays", "vs/base/common/cancellation", "vs/base/common/errors", "vs/base/common/network", "vs/editor/browser/editorExtensions", "vs/editor/common/services/languageFeatures", "vs/editor/contrib/gotoSymbol/browser/referencesModel"], "vs/platform/contextkey/common/contextkeys": ["require", "exports", "vs/base/common/platform", "vs/nls", "vs/platform/contextkey/common/contextkey"], "vs/editor/contrib/gotoError/browser/markerNavigationService": ["require", "exports", "vs/base/common/arrays", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/linkedList", "vs/base/common/strings", "vs/base/common/uri", "vs/editor/common/core/range", "vs/platform/instantiation/common/extensions", "vs/platform/instantiation/common/instantiation", "vs/platform/markers/common/markers", "vs/platform/configuration/common/configuration"], "vs/platform/theme/common/iconRegistry": ["require", "exports", "vs/base/common/async", "vs/base/common/codicons", "vs/base/common/codiconsUtil", "vs/base/common/themables", "vs/base/common/event", "vs/base/common/types", "vs/base/common/uri", "vs/nls", "vs/platform/jsonschemas/common/jsonContributionRegistry", "vs/platform/registry/common/platform"], "vs/editor/contrib/gotoError/browser/gotoErrorWidget": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/scrollbar/scrollableElement", "vs/base/common/arrays", "vs/base/common/color", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/resources", "vs/base/common/strings", "vs/editor/common/core/range", "vs/editor/contrib/peekView/browser/peekView", "vs/nls", "vs/platform/actions/browser/menuEntryActionViewItem", "vs/platform/actions/common/actions", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation", "vs/platform/label/common/label", "vs/platform/markers/common/markers", "vs/platform/opener/common/opener", "vs/platform/severityIcon/browser/severityIcon", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/themeService", "vs/css!vs/editor/contrib/gotoError/browser/media/gotoErrorWidget"], "vs/editor/contrib/gotoSymbol/browser/link/clickLinkGesture": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/platform"], "vs/css!vs/editor/contrib/gotoSymbol/browser/link/goToDefinitionAtPosition": [], "vs/editor/contrib/hover/browser/hoverActions": ["require", "exports", "vs/editor/contrib/hover/browser/hoverActionIds", "vs/base/common/keyCodes", "vs/editor/browser/editorExtensions", "vs/editor/common/core/range", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/contrib/gotoSymbol/browser/link/goToDefinitionAtPosition", "vs/editor/contrib/hover/browser/contentHoverController2", "vs/editor/common/languages", "vs/nls", "vs/css!vs/editor/contrib/hover/browser/hover"], "vs/editor/contrib/hover/browser/markdownHoverParticipant": ["require", "exports", "vs/base/browser/dom", "vs/base/common/arrays", "vs/base/common/cancellation", "vs/base/common/htmlContent", "vs/base/common/lifecycle", "vs/editor/browser/widget/markdownRenderer/browser/markdownRenderer", "vs/editor/contrib/hover/browser/hoverActionIds", "vs/editor/common/core/range", "vs/editor/common/languages/language", "vs/editor/contrib/hover/browser/hoverTypes", "vs/nls", "vs/platform/configuration/common/configuration", "vs/platform/opener/common/opener", "vs/editor/common/services/languageFeatures", "vs/editor/common/languages", "vs/platform/theme/common/iconRegistry", "vs/base/common/codicons", "vs/base/common/themables", "vs/base/common/errors", "vs/platform/keybinding/common/keybinding", "vs/base/browser/ui/hover/hoverWidget", "vs/platform/hover/browser/hover", "vs/base/common/async", "vs/editor/contrib/hover/browser/getHover", "vs/platform/commands/common/commands"], "vs/editor/contrib/hover/browser/markerHoverParticipant": ["require", "exports", "vs/base/browser/dom", "vs/base/common/arrays", "vs/base/common/async", "vs/base/common/errors", "vs/base/common/lifecycle", "vs/base/common/resources", "vs/editor/common/core/range", "vs/editor/common/services/languageFeatures", "vs/editor/common/services/markerDecorations", "vs/editor/contrib/codeAction/browser/codeAction", "vs/editor/contrib/codeAction/browser/codeActionController", "vs/editor/contrib/codeAction/common/types", "vs/editor/contrib/gotoError/browser/gotoError", "vs/editor/contrib/hover/browser/hoverTypes", "vs/nls", "vs/platform/markers/common/markers", "vs/platform/opener/common/opener", "vs/platform/progress/common/progress"], "vs/editor/contrib/hover/browser/marginHoverController": ["require", "exports", "vs/base/common/lifecycle", "vs/platform/instantiation/common/instantiation", "vs/base/common/async", "vs/editor/contrib/hover/browser/hoverUtils", "vs/editor/contrib/hover/browser/marginHoverWidget", "vs/css!vs/editor/contrib/hover/browser/hover"], "vs/editor/contrib/hover/browser/hoverAccessibleViews": ["require", "exports"], "vs/css!vs/editor/contrib/hover/browser/hover": [], "vs/editor/contrib/inPlaceReplace/browser/inPlaceReplaceCommand": ["require", "exports", "vs/editor/common/core/selection"], "vs/css!vs/editor/contrib/inPlaceReplace/browser/inPlaceReplace": [], "vs/editor/common/commands/shiftCommand": ["require", "exports", "vs/base/common/strings", "vs/editor/common/core/cursorColumns", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/editor/common/languages/enterAction", "vs/editor/common/languages/languageConfigurationRegistry"], "vs/editor/contrib/indentation/common/indentUtils": ["require", "exports"], "vs/editor/common/languages/autoIndent": ["require", "exports", "vs/base/common/strings", "vs/editor/common/languages/languageConfiguration", "vs/editor/common/languages/supports/indentationLineProcessor"], "vs/editor/contrib/indentation/common/indentation": ["require", "exports", "vs/base/common/strings", "vs/editor/common/commands/shiftCommand", "vs/editor/common/core/editOperation", "vs/editor/common/core/indentation", "vs/editor/common/core/selection", "vs/editor/common/languages/supports/indentationLineProcessor"], "vs/editor/common/tokens/lineTokens": ["require", "exports", "vs/editor/common/encodedTokenAttributes"], "vs/editor/contrib/inlayHints/browser/inlayHintsController": ["require", "exports", "vs/base/browser/dom", "vs/base/common/arrays", "vs/base/common/async", "vs/base/common/cancellation", "vs/base/common/errors", "vs/base/common/lifecycle", "vs/base/common/map", "vs/base/common/types", "vs/base/common/uri", "vs/editor/browser/editorDom", "vs/editor/browser/stableEditorScroll", "vs/editor/common/config/editorOptions", "vs/editor/common/core/editOperation", "vs/editor/common/core/range", "vs/editor/common/languages", "vs/editor/common/model", "vs/editor/common/model/textModel", "vs/editor/common/services/languageFeatureDebounce", "vs/editor/common/services/languageFeatures", "vs/editor/common/services/resolverService", "vs/editor/contrib/gotoSymbol/browser/link/clickLinkGesture", "vs/editor/contrib/inlayHints/browser/inlayHints", "vs/editor/contrib/inlayHints/browser/inlayHintsLocations", "vs/platform/commands/common/commands", "vs/platform/instantiation/common/extensions", "vs/platform/instantiation/common/instantiation", "vs/platform/notification/common/notification", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/themeService"], "vs/editor/contrib/inlayHints/browser/inlayHintsHover": ["require", "exports", "vs/base/common/async", "vs/base/common/htmlContent", "vs/editor/common/core/position", "vs/editor/common/model/textModel", "vs/editor/contrib/hover/browser/hoverTypes", "vs/editor/common/languages/language", "vs/editor/common/services/resolverService", "vs/editor/contrib/hover/browser/getHover", "vs/editor/contrib/hover/browser/markdownHoverParticipant", "vs/editor/contrib/inlayHints/browser/inlayHintsController", "vs/platform/configuration/common/configuration", "vs/platform/opener/common/opener", "vs/editor/common/services/languageFeatures", "vs/nls", "vs/base/common/platform", "vs/editor/contrib/inlayHints/browser/inlayHints", "vs/base/common/arrays", "vs/platform/keybinding/common/keybinding", "vs/platform/hover/browser/hover", "vs/platform/commands/common/commands"], "vs/editor/common/commands/trimTrailingWhitespaceCommand": ["require", "exports", "vs/base/common/strings", "vs/editor/common/core/editOperation", "vs/editor/common/core/range"], "vs/editor/common/core/editOperation": ["require", "exports", "vs/editor/common/core/range"], "vs/editor/contrib/linesOperations/browser/copyLinesCommand": ["require", "exports", "vs/editor/common/core/range", "vs/editor/common/core/selection"], "vs/editor/contrib/linesOperations/browser/moveLinesCommand": ["require", "exports", "vs/base/common/strings", "vs/editor/common/commands/shiftCommand", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/editor/common/languages/languageConfiguration", "vs/editor/common/languages/languageConfigurationRegistry", "vs/editor/contrib/indentation/common/indentUtils", "vs/editor/common/languages/autoIndent", "vs/editor/common/languages/enterAction"], "vs/editor/contrib/linesOperations/browser/sortLinesCommand": ["require", "exports", "vs/editor/common/core/editOperation", "vs/editor/common/core/range"], "vs/css!vs/editor/contrib/linkedEditing/browser/linkedEditing": [], "vs/base/common/resources": ["require", "exports", "vs/base/common/extpath", "vs/base/common/network", "vs/base/common/path", "vs/base/common/platform", "vs/base/common/strings", "vs/base/common/uri"], "vs/editor/contrib/links/browser/getLinks": ["require", "exports", "vs/base/common/arrays", "vs/base/common/cancellation", "vs/base/common/errors", "vs/base/common/lifecycle", "vs/base/common/types", "vs/base/common/uri", "vs/editor/common/core/range", "vs/editor/common/services/model", "vs/platform/commands/common/commands", "vs/editor/common/services/languageFeatures"], "vs/platform/opener/common/opener": ["require", "exports", "vs/platform/instantiation/common/instantiation"], "vs/css!vs/editor/contrib/links/browser/links": [], "vs/editor/contrib/wordHighlighter/browser/highlightDecorations": ["require", "exports", "vs/editor/common/model", "vs/editor/common/model/textModel", "vs/editor/common/languages", "vs/nls", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/themeService", "vs/css!vs/editor/contrib/wordHighlighter/browser/highlightDecorations"], "vs/editor/contrib/inlineEdit/browser/commands": ["require", "exports", "vs/editor/browser/editorExtensions", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/contrib/inlineEdit/browser/commandIds", "vs/editor/contrib/inlineEdit/browser/inlineEditController", "vs/platform/actions/common/actions", "vs/platform/contextkey/common/contextkey"], "vs/editor/contrib/inlineEdit/browser/inlineEditController": ["require", "exports", "vs/base/common/lifecycle", "vs/base/common/observable", "vs/editor/common/core/editOperation", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/contrib/inlineEdit/browser/ghostTextWidget", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation", "vs/editor/common/languages", "vs/editor/common/services/languageFeatures", "vs/base/common/cancellation", "vs/editor/contrib/inlineCompletions/browser/model/ghostText", "vs/platform/commands/common/commands", "vs/editor/contrib/inlineEdit/browser/inlineEditHintsWidget", "vs/base/browser/dom", "vs/platform/configuration/common/configuration", "vs/base/common/errors", "vs/base/common/observableInternal/derived", "vs/editor/contrib/inlineEdit/browser/inlineEditSideBySideWidget", "vs/editor/browser/widget/diffEditor/diffProviderFactoryService", "vs/editor/common/services/model"], "vs/platform/theme/common/colorUtils": ["require", "exports", "vs/base/common/assert", "vs/base/common/async", "vs/base/common/color", "vs/base/common/event", "vs/platform/jsonschemas/common/jsonContributionRegistry", "vs/platform/registry/common/platform", "vs/nls"], "vs/editor/contrib/placeholderText/browser/placeholderTextContribution": ["require", "exports", "vs/base/browser/dom", "vs/base/common/equals", "vs/base/common/lifecycle", "vs/base/common/observable", "vs/base/common/observableInternal/derived", "vs/editor/browser/observableCodeEditor"], "vs/platform/observable/common/wrapInReloadableClass": ["require", "exports", "vs/base/common/hotReload", "vs/base/common/hotReloadHelpers", "vs/base/common/observable", "vs/platform/instantiation/common/instantiation"], "vs/css!vs/editor/contrib/placeholderText/browser/placeholderText": [], "vs/editor/contrib/inlineEdits/browser/commands": ["require", "exports", "vs/base/common/codicons", "vs/base/common/observable", "vs/base/common/observableInternal/base", "vs/editor/browser/editorExtensions", "vs/editor/browser/widget/codeEditor/embeddedCodeEditorWidget", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/contrib/inlineEdits/browser/consts", "vs/editor/contrib/inlineEdits/browser/inlineEditsController", "vs/nls", "vs/platform/actions/common/actions", "vs/platform/contextkey/common/contextkey"], "vs/editor/contrib/inlineEdits/browser/inlineEditsController": ["require", "exports", "vs/base/common/lifecycle", "vs/base/common/observable", "vs/base/common/observableInternal/derived", "vs/editor/browser/observableCodeEditor", "vs/base/common/hotReloadHelpers", "vs/editor/common/core/selection", "vs/editor/common/services/languageFeatureDebounce", "vs/editor/common/services/languageFeatures", "vs/editor/contrib/inlineEdits/browser/consts", "vs/editor/contrib/inlineEdits/browser/inlineEditsModel", "vs/editor/contrib/inlineEdits/browser/inlineEditsWidget", "vs/platform/configuration/common/configuration", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation", "vs/platform/observable/common/platformObservableUtils"], "vs/editor/contrib/parameterHints/browser/parameterHintsModel": ["require", "exports", "vs/base/common/async", "vs/base/common/errors", "vs/base/common/event", "vs/base/common/lifecycle", "vs/editor/common/core/characterClassifier", "vs/editor/common/languages", "vs/editor/contrib/parameterHints/browser/provideSignatureHelp"], "vs/editor/contrib/parameterHints/browser/provideSignatureHelp": ["require", "exports", "vs/base/common/cancellation", "vs/base/common/errors", "vs/base/common/types", "vs/base/common/uri", "vs/editor/common/core/position", "vs/editor/common/languages", "vs/editor/common/services/languageFeatures", "vs/editor/common/services/resolverService", "vs/platform/commands/common/commands", "vs/platform/contextkey/common/contextkey"], "vs/editor/contrib/parameterHints/browser/parameterHintsWidget": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/aria/aria", "vs/base/browser/ui/scrollbar/scrollableElement", "vs/base/common/codicons", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/strings", "vs/base/common/types", "vs/editor/common/config/editorOptions", "vs/editor/common/languages/language", "vs/editor/browser/widget/markdownRenderer/browser/markdownRenderer", "vs/editor/contrib/parameterHints/browser/provideSignatureHelp", "vs/nls", "vs/platform/contextkey/common/contextkey", "vs/platform/opener/common/opener", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/iconRegistry", "vs/base/common/themables", "vs/base/common/stopwatch", "vs/platform/telemetry/common/telemetry", "vs/css!vs/editor/contrib/parameterHints/browser/parameterHints"], "vs/editor/browser/services/bulkEditService": ["require", "exports", "vs/platform/instantiation/common/instantiation", "vs/base/common/uri", "vs/base/common/types"], "vs/editor/common/services/textResourceConfiguration": ["require", "exports", "vs/platform/instantiation/common/instantiation"], "vs/platform/log/common/log": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation"], "vs/editor/contrib/rename/browser/renameWidget": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/keyboardEvent", "vs/base/browser/ui/aria/aria", "vs/base/browser/ui/hover/hoverDelegate2", "vs/base/browser/ui/hover/hoverDelegateFactory", "vs/base/browser/ui/iconLabel/iconLabels", "vs/base/browser/ui/list/listWidget", "vs/base/common/arrays", "vs/base/common/async", "vs/base/common/cancellation", "vs/base/common/codicons", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/stopwatch", "vs/base/common/types", "vs/editor/browser/config/domFontInfo", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/languages", "vs/nls", "vs/platform/contextkey/common/contextkey", "vs/platform/keybinding/common/keybinding", "vs/platform/log/common/log", "vs/platform/theme/browser/defaultStyles", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/themeService", "vs/css!vs/editor/contrib/rename/browser/renameWidget"], "vs/editor/contrib/smartSelect/browser/bracketSelections": ["require", "exports", "vs/base/common/linkedList", "vs/editor/common/core/position", "vs/editor/common/core/range"], "vs/editor/contrib/smartSelect/browser/wordSelections": ["require", "exports", "vs/base/common/strings", "vs/editor/common/core/range"], "vs/editor/contrib/semanticTokens/common/getSemanticTokens": ["require", "exports", "vs/base/common/cancellation", "vs/base/common/errors", "vs/base/common/uri", "vs/editor/common/services/model", "vs/platform/commands/common/commands", "vs/base/common/types", "vs/editor/common/services/semanticTokensDto", "vs/editor/common/core/range", "vs/editor/common/services/languageFeatures"], "vs/editor/contrib/semanticTokens/common/semanticTokensConfig": ["require", "exports"], "vs/editor/common/services/semanticTokensProviderStyling": ["require", "exports", "vs/editor/common/encodedTokenAttributes", "vs/platform/theme/common/themeService", "vs/platform/log/common/log", "vs/editor/common/tokens/sparseMultilineTokens", "vs/editor/common/languages/language"], "vs/editor/common/services/semanticTokensStyling": ["require", "exports", "vs/platform/instantiation/common/instantiation"], "vs/editor/contrib/suggest/browser/suggest": ["require", "exports", "vs/base/common/cancellation", "vs/base/common/errors", "vs/base/common/filters", "vs/base/common/lifecycle", "vs/base/common/stopwatch", "vs/base/common/types", "vs/base/common/uri", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/services/resolverService", "vs/editor/contrib/snippet/browser/snippetParser", "vs/nls", "vs/platform/actions/common/actions", "vs/platform/commands/common/commands", "vs/platform/contextkey/common/contextkey", "vs/editor/common/services/languageFeatures", "vs/platform/history/browser/contextScopedHistoryWidget"], "vs/editor/contrib/snippet/browser/snippetSession": ["require", "exports", "vs/base/common/arrays", "vs/base/common/lifecycle", "vs/base/common/strings", "vs/editor/common/core/editOperation", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/editor/common/languages/languageConfigurationRegistry", "vs/editor/common/model/textModel", "vs/platform/label/common/label", "vs/platform/workspace/common/workspace", "vs/editor/contrib/snippet/browser/snippetParser", "vs/editor/contrib/snippet/browser/snippetVariables", "vs/css!vs/editor/contrib/snippet/browser/snippetSession"], "vs/editor/contrib/stickyScroll/browser/stickyScrollActions": ["require", "exports", "vs/editor/browser/editorExtensions", "vs/nls", "vs/platform/action/common/actionCommonCategories", "vs/platform/actions/common/actions", "vs/platform/configuration/common/configuration", "vs/platform/contextkey/common/contextkey", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/contrib/stickyScroll/browser/stickyScrollController"], "vs/editor/contrib/stickyScroll/browser/stickyScrollController": ["require", "exports", "vs/base/common/lifecycle", "vs/editor/common/services/languageFeatures", "vs/editor/contrib/stickyScroll/browser/stickyScrollWidget", "vs/editor/contrib/stickyScroll/browser/stickyScrollProvider", "vs/platform/instantiation/common/instantiation", "vs/platform/contextview/browser/contextView", "vs/platform/actions/common/actions", "vs/platform/contextkey/common/contextkey", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/contrib/gotoSymbol/browser/link/clickLinkGesture", "vs/editor/common/core/range", "vs/editor/contrib/gotoSymbol/browser/goToSymbol", "vs/editor/contrib/inlayHints/browser/inlayHintsLocations", "vs/editor/common/core/position", "vs/base/common/cancellation", "vs/editor/common/languages/languageConfigurationRegistry", "vs/editor/common/services/languageFeatureDebounce", "vs/base/browser/dom", "vs/editor/contrib/stickyScroll/browser/stickyScrollElement", "vs/base/browser/mouseEvent", "vs/editor/contrib/folding/browser/folding", "vs/editor/contrib/folding/browser/foldingModel"], "vs/base/common/filters": ["require", "exports", "vs/base/common/map", "vs/base/common/naturalLanguage/korean", "vs/base/common/strings"], "vs/editor/contrib/suggest/browser/completionModel": ["require", "exports", "vs/base/common/arrays", "vs/base/common/filters", "vs/base/common/strings"], "vs/editor/contrib/suggest/browser/suggestMemory": ["require", "exports", "vs/base/common/async", "vs/base/common/lifecycle", "vs/base/common/map", "vs/base/common/ternarySearchTree", "vs/editor/common/languages", "vs/platform/configuration/common/configuration", "vs/platform/instantiation/common/extensions", "vs/platform/instantiation/common/instantiation", "vs/platform/storage/common/storage"], "vs/editor/contrib/suggest/browser/suggestModel": ["require", "exports", "vs/base/common/async", "vs/base/common/cancellation", "vs/base/common/errors", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/strings", "vs/editor/common/core/selection", "vs/editor/common/services/editorW<PERSON>ker", "vs/editor/contrib/suggest/browser/wordDistance", "vs/platform/clipboard/common/clipboardService", "vs/platform/configuration/common/configuration", "vs/platform/contextkey/common/contextkey", "vs/platform/log/common/log", "vs/platform/telemetry/common/telemetry", "vs/editor/contrib/suggest/browser/completionModel", "vs/editor/contrib/suggest/browser/suggest", "vs/editor/common/services/languageFeatures", "vs/base/common/filters", "vs/base/common/types", "vs/editor/contrib/inlineCompletions/browser/controller/inlineCompletionContextKeys", "vs/editor/contrib/snippet/browser/snippetController2", "vs/platform/environment/common/environment"], "vs/editor/contrib/suggest/browser/wordDistance": ["require", "exports", "vs/base/common/arrays", "vs/editor/common/core/range", "vs/editor/contrib/smartSelect/browser/bracketSelections"], "vs/base/common/keybindings": ["require", "exports", "vs/base/common/errors"], "vs/editor/contrib/snippet/browser/snippetParser": ["require", "exports"], "vs/editor/contrib/suggest/browser/wordContextKey": ["require", "exports", "vs/platform/contextkey/common/contextkey"], "vs/editor/contrib/suggest/browser/suggestAlternatives": ["require", "exports", "vs/platform/contextkey/common/contextkey"], "vs/editor/contrib/suggest/browser/suggestCommitCharacters": ["require", "exports", "vs/base/common/arrays", "vs/base/common/lifecycle", "vs/editor/common/core/characterClassifier"], "vs/editor/contrib/suggest/browser/suggestOvertypingCapturer": ["require", "exports", "vs/base/common/lifecycle"], "vs/editor/contrib/suggest/browser/suggestWidget": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/list/listWidget", "vs/base/common/async", "vs/base/common/errors", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/numbers", "vs/base/common/strings", "vs/editor/browser/widget/codeEditor/embeddedCodeEditorWidget", "vs/editor/contrib/suggest/browser/suggestWidgetStatus", "vs/nls", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation", "vs/platform/storage/common/storage", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/theme", "vs/platform/theme/common/themeService", "vs/base/browser/ui/resizable/resizable", "vs/editor/contrib/suggest/browser/suggest", "vs/editor/contrib/suggest/browser/suggestWidgetDetails", "vs/editor/contrib/suggest/browser/suggestWidgetRenderer", "vs/platform/theme/browser/defaultStyles", "vs/base/browser/ui/aria/aria", "vs/base/browser/ui/codicons/codiconStyles", "vs/css!vs/editor/contrib/suggest/browser/media/suggest", "vs/editor/contrib/symbolIcons/browser/symbolIcons"], "vs/base/common/map": ["require", "exports"], "vs/editor/common/languageSelector": ["require", "exports", "vs/base/common/glob", "vs/base/common/path"], "vs/editor/contrib/wordHighlighter/browser/textualHighlightProvider": ["require", "exports", "vs/editor/common/core/wordHelper", "vs/editor/common/services/languageFeatures", "vs/editor/common/languages", "vs/base/common/lifecycle", "vs/base/common/map"], "vs/editor/common/viewModel/viewModelDecorations": ["require", "exports", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/viewModel", "vs/editor/common/config/editorOptions"], "vs/editor/contrib/unicodeHighlighter/browser/bannerController": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/actionbar/actionbar", "vs/base/common/actions", "vs/base/common/lifecycle", "vs/editor/browser/widget/markdownRenderer/browser/markdownRenderer", "vs/platform/instantiation/common/instantiation", "vs/platform/opener/browser/link", "vs/platform/theme/common/iconRegistry", "vs/base/common/themables", "vs/css!vs/editor/contrib/unicodeHighlighter/browser/bannerController"], "vs/platform/workspace/common/workspaceTrust": ["require", "exports", "vs/platform/instantiation/common/instantiation"], "vs/css!vs/editor/contrib/unicodeHighlighter/browser/unicodeHighlighter": [], "vs/editor/common/core/wordCharacterClassifier": ["require", "exports", "vs/base/common/map", "vs/editor/common/core/characterClassifier"], "vs/base/common/observable": ["require", "exports", "vs/base/common/observableInternal/base", "vs/base/common/observableInternal/derived", "vs/base/common/observableInternal/autorun", "vs/base/common/observableInternal/utils", "vs/base/common/observableInternal/promise", "vs/base/common/observableInternal/api", "vs/base/common/observableInternal/logging"], "vs/editor/browser/widget/diffEditor/features/hideUnchangedRegionsFeature": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/iconLabel/iconLabels", "vs/base/common/codicons", "vs/base/common/htmlContent", "vs/base/common/lifecycle", "vs/base/common/observable", "vs/base/common/observableInternal/derived", "vs/base/common/themables", "vs/base/common/types", "vs/editor/browser/observableCodeEditor", "vs/editor/browser/widget/diffEditor/utils", "vs/editor/common/core/lineRange", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/languages", "vs/nls", "vs/platform/instantiation/common/instantiation"], "vs/editor/browser/widget/diffEditor/utils": ["require", "exports", "vs/base/common/arraysFind", "vs/base/common/cancellation", "vs/base/common/lifecycle", "vs/base/common/observable", "vs/editor/browser/config/elementSizeObserver", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/core/textLength"], "vs/css!vs/base/browser/ui/codicons/codicon/codicon": [], "vs/css!vs/base/browser/ui/codicons/codicon/codicon-modifiers": [], "vs/platform/extensions/common/extensions": ["require", "exports"], "vs/editor/common/core/textModelDefaults": ["require", "exports"], "vs/base/browser/window": ["require", "exports"], "vs/editor/browser/config/fontMeasurements": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/pixelRatio", "vs/base/common/event", "vs/base/common/lifecycle", "vs/editor/browser/config/charWidthReader", "vs/editor/common/config/editorOptions", "vs/editor/common/config/fontInfo"], "vs/editor/standalone/browser/standaloneWebWorker": ["require", "exports", "vs/base/common/objects", "vs/editor/browser/services/editorWorkerService", "vs/editor/standalone/browser/standaloneServices"], "vs/editor/common/config/fontInfo": ["require", "exports", "vs/base/common/platform", "vs/editor/common/config/editorOptions", "vs/editor/common/config/editorZoom"], "vs/editor/common/languages/modesRegistry": ["require", "exports", "vs/nls", "vs/base/common/event", "vs/platform/registry/common/platform", "vs/base/common/mime", "vs/platform/configuration/common/configurationRegistry"], "vs/editor/standalone/browser/colorizer": ["require", "exports", "vs/base/browser/trustedTypes", "vs/base/common/strings", "vs/editor/common/languages", "vs/editor/common/tokens/lineTokens", "vs/editor/common/viewLayout/viewLineRenderer", "vs/editor/common/viewModel", "vs/editor/standalone/common/monarch/monarchLexer"], "vs/editor/standalone/browser/standaloneCodeEditor": ["require", "exports", "vs/base/browser/ui/aria/aria", "vs/base/common/lifecycle", "vs/editor/browser/services/codeEditorService", "vs/editor/browser/widget/codeEditor/codeEditorWidget", "vs/editor/common/editorAction", "vs/editor/standalone/browser/standaloneServices", "vs/editor/standalone/common/standaloneTheme", "vs/platform/actions/common/actions", "vs/platform/commands/common/commands", "vs/platform/configuration/common/configuration", "vs/platform/contextkey/common/contextkey", "vs/platform/contextview/browser/contextView", "vs/platform/instantiation/common/instantiation", "vs/platform/keybinding/common/keybinding", "vs/platform/notification/common/notification", "vs/platform/theme/common/themeService", "vs/platform/accessibility/common/accessibility", "vs/editor/common/standaloneStrings", "vs/platform/clipboard/common/clipboardService", "vs/platform/progress/common/progress", "vs/editor/common/services/model", "vs/editor/common/languages/language", "vs/editor/standalone/browser/standaloneCodeEditorService", "vs/editor/common/languages/modesRegistry", "vs/editor/common/languages/languageConfigurationRegistry", "vs/editor/common/services/languageFeatures", "vs/editor/browser/widget/diffEditor/diffEditorWidget", "vs/platform/accessibilitySignal/browser/accessibilitySignalService", "vs/base/browser/window", "vs/base/browser/ui/hover/hoverDelegateFactory", "vs/platform/hover/browser/hover", "vs/base/browser/ui/hover/hoverDelegate2"], "vs/editor/standalone/browser/standaloneServices": ["require", "exports", "vs/base/common/strings", "vs/base/browser/dom", "vs/base/browser/keyboardEvent", "vs/base/common/event", "vs/base/common/keybindings", "vs/base/common/lifecycle", "vs/base/common/platform", "vs/base/common/severity", "vs/base/common/uri", "vs/editor/browser/services/bulkEditService", "vs/editor/common/config/editorConfigurationSchema", "vs/editor/common/core/editOperation", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/services/model", "vs/editor/common/services/resolverService", "vs/editor/common/services/textResourceConfiguration", "vs/platform/commands/common/commands", "vs/platform/configuration/common/configuration", "vs/platform/configuration/common/configurationModels", "vs/platform/contextkey/common/contextkey", "vs/platform/dialogs/common/dialogs", "vs/platform/instantiation/common/instantiation", "vs/platform/keybinding/common/abstractKeybindingService", "vs/platform/keybinding/common/keybinding", "vs/platform/keybinding/common/keybindingResolver", "vs/platform/keybinding/common/keybindingsRegistry", "vs/platform/keybinding/common/resolvedKeybindingItem", "vs/platform/keybinding/common/usLayoutResolvedKeybinding", "vs/platform/label/common/label", "vs/platform/notification/common/notification", "vs/platform/progress/common/progress", "vs/platform/telemetry/common/telemetry", "vs/platform/workspace/common/workspace", "vs/platform/layout/browser/layoutService", "vs/editor/common/standaloneStrings", "vs/base/common/resources", "vs/editor/browser/services/codeEditorService", "vs/platform/log/common/log", "vs/platform/workspace/common/workspaceTrust", "vs/platform/contextview/browser/contextView", "vs/platform/contextview/browser/contextViewService", "vs/editor/common/services/languageService", "vs/platform/contextview/browser/contextMenuService", "vs/platform/instantiation/common/extensions", "vs/editor/browser/services/openerService", "vs/editor/common/services/editorW<PERSON>ker", "vs/editor/browser/services/editorWorkerService", "vs/editor/common/languages/language", "vs/editor/common/services/markerDecorationsService", "vs/editor/common/services/markerDecorations", "vs/editor/common/services/modelService", "vs/editor/standalone/browser/quickInput/standaloneQuickInputService", "vs/editor/standalone/browser/standaloneThemeService", "vs/editor/standalone/common/standaloneTheme", "vs/platform/accessibility/browser/accessibilityService", "vs/platform/accessibility/common/accessibility", "vs/platform/actions/common/actions", "vs/platform/actions/common/menuService", "vs/platform/clipboard/browser/clipboardService", "vs/platform/clipboard/common/clipboardService", "vs/platform/contextkey/browser/contextKeyService", "vs/platform/instantiation/common/descriptors", "vs/platform/instantiation/common/instantiationService", "vs/platform/instantiation/common/serviceCollection", "vs/platform/list/browser/listService", "vs/platform/markers/common/markers", "vs/platform/markers/common/markerService", "vs/platform/opener/common/opener", "vs/platform/quickinput/common/quickInput", "vs/platform/storage/common/storage", "vs/platform/configuration/common/configurations", "vs/platform/accessibilitySignal/browser/accessibilitySignalService", "vs/editor/common/services/languageFeatures", "vs/editor/common/languages/languageConfigurationRegistry", "vs/platform/log/common/logService", "vs/editor/common/editorFeatures", "vs/base/common/errors", "vs/platform/environment/common/environment", "vs/base/browser/window", "vs/base/common/map", "vs/editor/common/services/treeSitterParserService", "vs/editor/standalone/browser/standaloneTreeSitterService", "vs/editor/standalone/browser/standaloneCodeEditorService", "vs/editor/standalone/browser/standaloneLayoutService", "vs/platform/undoRedo/common/undoRedoService", "vs/editor/common/services/languageFeatureDebounce", "vs/editor/common/services/semanticTokensStylingService", "vs/editor/common/services/languageFeaturesService", "vs/editor/browser/services/hoverService/hoverService"], "vs/platform/markers/common/markers": ["require", "exports", "vs/base/common/severity", "vs/nls", "vs/platform/instantiation/common/instantiation"], "vs/editor/browser/widget/multiDiffEditor/multiDiffEditorWidget": ["require", "exports", "vs/base/common/lifecycle", "vs/base/common/observable", "vs/base/common/hotReloadHelpers", "vs/editor/browser/widget/multiDiffEditor/multiDiffEditorWidgetImpl", "vs/platform/instantiation/common/instantiation", "vs/editor/browser/widget/multiDiffEditor/diffEditorItemTemplate", "vs/editor/browser/widget/multiDiffEditor/colors"], "vs/css!vs/editor/standalone/browser/standalone-tokens": [], "vs/editor/standalone/common/monarch/monarchCompile": ["require", "exports", "vs/editor/standalone/common/monarch/monarch<PERSON><PERSON>mon"], "vs/editor/standalone/common/monarch/monarchLexer": ["require", "exports", "vs/base/common/lifecycle", "vs/editor/common/languages", "vs/editor/common/languages/nullTokenize", "vs/editor/standalone/common/monarch/monarch<PERSON><PERSON>mon", "vs/platform/configuration/common/configuration"], "vs/base/browser/canIUse": ["require", "exports", "vs/base/browser/browser", "vs/base/browser/window", "vs/base/common/platform"], "vs/base/browser/keyboardEvent": ["require", "exports", "vs/base/browser/browser", "vs/base/common/keyCodes", "vs/base/common/keybindings", "vs/base/common/platform"], "vs/base/browser/mouseEvent": ["require", "exports", "vs/base/browser/browser", "vs/base/browser/iframe", "vs/base/common/platform"], "vs/base/browser/dompurify/dompurify": ["require", "exports", "module"], "vs/editor/common/tokenizationRegistry": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle"], "vs/base/common/fuzzyScorer": ["require", "exports", "vs/base/common/filters", "vs/base/common/path", "vs/base/common/platform", "vs/base/common/strings"], "vs/editor/contrib/quickAccess/browser/editorNavigationQuickAccess": ["require", "exports", "vs/base/common/functional", "vs/base/common/lifecycle", "vs/editor/browser/editorBrowser", "vs/editor/common/model", "vs/editor/common/core/editorColorRegistry", "vs/platform/theme/common/themeService", "vs/base/browser/ui/aria/aria"], "vs/base/common/arraysFind": ["require", "exports"], "vs/platform/instantiation/common/extensions": ["require", "exports", "vs/platform/instantiation/common/descriptors"], "vs/css!vs/editor/contrib/symbolIcons/browser/symbolIcons": [], "vs/base/common/iconLabels": ["require", "exports", "vs/base/common/filters", "vs/base/common/strings", "vs/base/common/themables"], "vs/platform/action/common/action": ["require", "exports"], "vs/platform/quickinput/browser/commandsQuickAccess": ["require", "exports", "vs/base/common/errorMessage", "vs/base/common/errors", "vs/base/common/filters", "vs/base/common/functional", "vs/base/common/lifecycle", "vs/base/common/map", "vs/base/common/tfIdf", "vs/nls", "vs/platform/commands/common/commands", "vs/platform/configuration/common/configuration", "vs/platform/dialogs/common/dialogs", "vs/platform/instantiation/common/instantiation", "vs/platform/keybinding/common/keybinding", "vs/platform/log/common/log", "vs/platform/quickinput/browser/pickerQuickAccess", "vs/platform/storage/common/storage", "vs/platform/telemetry/common/telemetry"], "vs/base/common/severity": ["require", "exports", "vs/base/common/strings"], "vs/platform/contextkey/common/scanner": ["require", "exports", "vs/base/common/errors", "vs/nls"], "vs/platform/list/browser/listService": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/list/listPaging", "vs/base/browser/ui/list/listWidget", "vs/base/browser/ui/table/tableWidget", "vs/base/browser/ui/tree/abstractTree", "vs/base/browser/ui/tree/asyncDataTree", "vs/base/browser/ui/tree/dataTree", "vs/base/browser/ui/tree/objectTree", "vs/base/common/event", "vs/base/common/lifecycle", "vs/nls", "vs/platform/configuration/common/configuration", "vs/platform/configuration/common/configurationRegistry", "vs/platform/contextkey/common/contextkey", "vs/platform/contextkey/common/contextkeys", "vs/platform/contextview/browser/contextView", "vs/platform/instantiation/common/instantiation", "vs/platform/keybinding/common/keybinding", "vs/platform/registry/common/platform", "vs/platform/theme/browser/defaultStyles"], "vs/editor/contrib/gotoSymbol/browser/peek/referencesWidget": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/splitview/splitview", "vs/base/common/color", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/network", "vs/base/common/resources", "vs/editor/browser/widget/codeEditor/embeddedCodeEditorWidget", "vs/editor/common/core/range", "vs/editor/common/model/textModel", "vs/editor/common/languages/modesRegistry", "vs/editor/common/services/resolverService", "vs/editor/contrib/gotoSymbol/browser/peek/referencesTree", "vs/editor/contrib/peekView/browser/peekView", "vs/nls", "vs/platform/instantiation/common/instantiation", "vs/platform/keybinding/common/keybinding", "vs/platform/label/common/label", "vs/platform/list/browser/listService", "vs/platform/theme/common/themeService", "vs/editor/contrib/gotoSymbol/browser/referencesModel", "vs/css!vs/editor/contrib/gotoSymbol/browser/peek/referencesWidget"], "vs/base/parts/storage/common/storage": ["require", "exports", "vs/base/common/async", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/marshalling", "vs/base/common/types"], "vs/editor/common/languages/supports/tokenization": ["require", "exports", "vs/base/common/color"], "vs/editor/standalone/common/themes": ["require", "exports", "vs/editor/common/core/editorColorRegistry", "vs/platform/theme/common/colorRegistry"], "vs/platform/theme/browser/iconsStyleSheet": ["require", "exports", "vs/base/browser/dom", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/themables", "vs/platform/theme/common/iconRegistry"], "vs/nls.messages": ["require", "exports"], "vs/base/common/process": ["require", "exports", "vs/base/common/platform"], "vs/base/common/uint": ["require", "exports"], "vs/editor/common/core/lineRange": ["require", "exports", "vs/base/common/errors", "vs/editor/common/core/offsetRange", "vs/editor/common/core/range", "vs/base/common/arraysFind"], "vs/editor/common/core/offsetRange": ["require", "exports", "vs/base/common/errors"], "vs/editor/common/diff/defaultLinesDiffComputer/algorithms/diffAlgorithm": ["require", "exports", "vs/base/common/arrays", "vs/base/common/errors", "vs/editor/common/core/offsetRange"], "vs/editor/common/diff/defaultLinesDiffComputer/algorithms/dynamicProgrammingDiffing": ["require", "exports", "vs/editor/common/core/offsetRange", "vs/editor/common/diff/defaultLinesDiffComputer/algorithms/diffAlgorithm", "vs/editor/common/diff/defaultLinesDiffComputer/utils"], "vs/editor/common/diff/defaultLinesDiffComputer/algorithms/myersDiffAlgorithm": ["require", "exports", "vs/editor/common/core/offsetRange", "vs/editor/common/diff/defaultLinesDiffComputer/algorithms/diffAlgorithm"], "vs/editor/common/diff/defaultLinesDiffComputer/computeMovedLines": ["require", "exports", "vs/editor/common/diff/defaultLinesDiffComputer/algorithms/diffAlgorithm", "vs/editor/common/diff/rangeMapping", "vs/base/common/arrays", "vs/base/common/arraysFind", "vs/base/common/map", "vs/editor/common/core/lineRange", "vs/editor/common/diff/defaultLinesDiffComputer/linesSliceCharSequence", "vs/editor/common/diff/defaultLinesDiffComputer/utils", "vs/editor/common/diff/defaultLinesDiffComputer/algorithms/myersDiffAlgorithm", "vs/editor/common/core/range"], "vs/editor/common/diff/defaultLinesDiffComputer/heuristicSequenceOptimizations": ["require", "exports", "vs/base/common/arrays", "vs/editor/common/core/offsetRange", "vs/editor/common/diff/defaultLinesDiffComputer/algorithms/diffAlgorithm"], "vs/editor/common/diff/defaultLinesDiffComputer/lineSequence": ["require", "exports"], "vs/editor/common/diff/defaultLinesDiffComputer/linesSliceCharSequence": ["require", "exports", "vs/base/common/arraysFind", "vs/editor/common/core/offsetRange", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/diff/defaultLinesDiffComputer/utils"], "vs/editor/common/diff/linesDiffComputer": ["require", "exports"], "vs/editor/common/diff/rangeMapping": ["require", "exports", "vs/base/common/errors", "vs/editor/common/core/lineRange", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/core/textEdit"], "vs/editor/common/model/prefixSumComputer": ["require", "exports", "vs/base/common/arrays", "vs/base/common/uint"], "vs/base/common/symbols": ["require", "exports"], "vs/base/browser/fastDomNode": ["require", "exports"], "vs/editor/browser/config/elementSizeObserver": ["require", "exports", "vs/base/common/lifecycle", "vs/base/common/event", "vs/base/browser/dom"], "vs/editor/browser/config/migrateOptions": ["require", "exports"], "vs/base/browser/pixelRatio": ["require", "exports", "vs/base/browser/dom", "vs/base/common/event", "vs/base/common/lifecycle"], "vs/base/browser/performance": ["require", "exports"], "vs/editor/browser/controller/mouseTarget": ["require", "exports", "vs/editor/browser/editorDom", "vs/editor/browser/view/viewPart", "vs/editor/browser/viewParts/lines/viewLine", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/core/cursorColumns", "vs/base/browser/dom", "vs/editor/common/cursor/cursorAtomicMoveOperations", "vs/base/common/lazy"], "vs/editor/browser/controller/pointerHandler": ["require", "exports", "vs/base/browser/canIUse", "vs/base/browser/dom", "vs/base/browser/touch", "vs/base/browser/window", "vs/base/common/lifecycle", "vs/base/common/platform", "vs/editor/browser/controller/mouseHandler", "vs/editor/browser/controller/textAreaInput", "vs/editor/browser/editorDom"], "vs/editor/browser/controller/textAreaHandler": ["require", "exports", "vs/nls", "vs/base/browser/browser", "vs/base/browser/fastDomNode", "vs/base/common/platform", "vs/base/common/strings", "vs/editor/browser/config/domFontInfo", "vs/editor/browser/controller/textAreaInput", "vs/editor/browser/controller/textAreaState", "vs/editor/browser/view/viewPart", "vs/editor/browser/viewParts/lineNumbers/lineNumbers", "vs/editor/browser/viewParts/margin/margin", "vs/editor/common/config/editorOptions", "vs/editor/common/core/wordCharacterClassifier", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/base/browser/ui/mouseCursor/mouseCursor", "vs/editor/common/languages", "vs/base/common/color", "vs/base/common/ime", "vs/platform/keybinding/common/keybinding", "vs/platform/instantiation/common/instantiation", "vs/css!vs/editor/browser/controller/textAreaHandler"], "vs/editor/browser/view/renderingContext": ["require", "exports"], "vs/editor/browser/view/viewController": ["require", "exports", "vs/editor/browser/coreCommands", "vs/editor/common/core/position", "vs/base/common/platform"], "vs/editor/browser/view/viewOverlays": ["require", "exports", "vs/base/browser/fastDomNode", "vs/editor/browser/config/domFontInfo", "vs/editor/browser/view/viewLayer", "vs/editor/browser/view/viewPart"], "vs/editor/browser/view/viewPart": ["require", "exports", "vs/editor/common/viewEventHandler"], "vs/editor/browser/viewParts/blockDecorations/blockDecorations": ["require", "exports", "vs/base/browser/fastDomNode", "vs/editor/browser/view/viewPart", "vs/css!vs/editor/browser/viewParts/blockDecorations/blockDecorations"], "vs/editor/browser/viewParts/contentWidgets/contentWidgets": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/fastDomNode", "vs/editor/browser/view/viewPart"], "vs/editor/browser/viewParts/currentLineHighlight/currentLineHighlight": ["require", "exports", "vs/editor/browser/view/dynamicViewOverlay", "vs/editor/common/core/editorColorRegistry", "vs/base/common/arrays", "vs/platform/theme/common/themeService", "vs/editor/common/core/selection", "vs/platform/theme/common/theme", "vs/editor/common/core/position", "vs/css!vs/editor/browser/viewParts/currentLineHighlight/currentLineHighlight"], "vs/editor/browser/viewParts/decorations/decorations": ["require", "exports", "vs/editor/browser/view/dynamicViewOverlay", "vs/editor/browser/view/renderingContext", "vs/editor/common/core/range", "vs/css!vs/editor/browser/viewParts/decorations/decorations"], "vs/editor/browser/viewParts/editorScrollbar/editorScrollbar": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/fastDomNode", "vs/base/browser/ui/scrollbar/scrollableElement", "vs/editor/browser/view/viewPart", "vs/platform/theme/common/themeService"], "vs/editor/browser/viewParts/glyphMargin/glyphMargin": ["require", "exports", "vs/base/browser/fastDomNode", "vs/base/common/arrays", "vs/editor/browser/view/dynamicViewOverlay", "vs/editor/browser/view/viewPart", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/model", "vs/css!vs/editor/browser/viewParts/glyphMargin/glyphMargin"], "vs/editor/browser/viewParts/indentGuides/indentGuides": ["require", "exports", "vs/editor/browser/view/dynamicViewOverlay", "vs/editor/common/core/editorColorRegistry", "vs/platform/theme/common/themeService", "vs/editor/common/core/position", "vs/base/common/arrays", "vs/base/common/types", "vs/editor/common/model/guidesTextModelPart", "vs/editor/common/textModelGuides", "vs/css!vs/editor/browser/viewParts/indentGuides/indentGuides"], "vs/editor/browser/viewParts/lineNumbers/lineNumbers": ["require", "exports", "vs/base/common/platform", "vs/editor/browser/view/dynamicViewOverlay", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/platform/theme/common/themeService", "vs/editor/common/core/editorColorRegistry", "vs/css!vs/editor/browser/viewParts/lineNumbers/lineNumbers"], "vs/editor/browser/viewParts/lines/viewLines": ["require", "exports", "vs/base/browser/ui/mouseCursor/mouseCursor", "vs/base/common/async", "vs/base/common/platform", "vs/editor/browser/config/domFontInfo", "vs/editor/browser/view/renderingContext", "vs/editor/browser/view/viewLayer", "vs/editor/browser/view/viewPart", "vs/editor/browser/viewParts/lines/domReadingContext", "vs/editor/browser/viewParts/lines/viewLine", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/css!vs/editor/browser/viewParts/lines/viewLines"], "vs/editor/browser/viewParts/linesDecorations/linesDecorations": ["require", "exports", "vs/editor/browser/viewParts/glyphMargin/glyphMargin", "vs/css!vs/editor/browser/viewParts/linesDecorations/linesDecorations"], "vs/editor/browser/viewParts/margin/margin": ["require", "exports", "vs/base/browser/fastDomNode", "vs/editor/browser/view/viewPart", "vs/css!vs/editor/browser/viewParts/margin/margin"], "vs/editor/browser/viewParts/marginDecorations/marginDecorations": ["require", "exports", "vs/editor/browser/viewParts/glyphMargin/glyphMargin", "vs/css!vs/editor/browser/viewParts/marginDecorations/marginDecorations"], "vs/editor/browser/viewParts/minimap/minimap": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/fastDomNode", "vs/base/browser/globalPointerMoveMonitor", "vs/base/common/lifecycle", "vs/base/common/platform", "vs/base/common/strings", "vs/editor/browser/view/viewLayer", "vs/editor/browser/view/viewPart", "vs/editor/common/config/editorOptions", "vs/editor/common/core/range", "vs/editor/common/core/rgba", "vs/editor/common/viewModel/minimapTokensColorTracker", "vs/editor/common/viewModel", "vs/platform/theme/common/colorRegistry", "vs/editor/common/core/selection", "vs/base/browser/touch", "vs/editor/browser/viewParts/minimap/minimapCharRendererFactory", "vs/base/common/functional", "vs/base/common/map", "vs/base/browser/fonts", "vs/css!vs/editor/browser/viewParts/minimap/minimap"], "vs/editor/browser/viewParts/overlayWidgets/overlayWidgets": ["require", "exports", "vs/base/browser/fastDomNode", "vs/editor/browser/view/viewPart", "vs/base/browser/dom", "vs/css!vs/editor/browser/viewParts/overlayWidgets/overlayWidgets"], "vs/editor/browser/viewParts/overviewRuler/decorationsOverviewRuler": ["require", "exports", "vs/base/browser/fastDomNode", "vs/base/common/color", "vs/editor/browser/view/viewPart", "vs/editor/common/core/position", "vs/editor/common/languages", "vs/editor/common/core/editorColorRegistry", "vs/editor/common/viewModel", "vs/base/common/arrays"], "vs/editor/browser/viewParts/overviewRuler/overviewRuler": ["require", "exports", "vs/base/browser/fastDomNode", "vs/editor/common/viewModel/overviewZoneManager", "vs/editor/common/viewEventHandler"], "vs/editor/browser/viewParts/rulers/rulers": ["require", "exports", "vs/base/browser/fastDomNode", "vs/editor/browser/view/viewPart", "vs/css!vs/editor/browser/viewParts/rulers/rulers"], "vs/editor/browser/viewParts/scrollDecoration/scrollDecoration": ["require", "exports", "vs/base/browser/fastDomNode", "vs/editor/browser/view/viewPart", "vs/css!vs/editor/browser/viewParts/scrollDecoration/scrollDecoration"], "vs/editor/browser/viewParts/selections/selections": ["require", "exports", "vs/editor/browser/view/dynamicViewOverlay", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/themeService", "vs/css!vs/editor/browser/viewParts/selections/selections"], "vs/editor/browser/viewParts/viewCursors/viewCursors": ["require", "exports", "vs/base/browser/fastDomNode", "vs/base/common/async", "vs/editor/browser/view/viewPart", "vs/editor/browser/viewParts/viewCursors/viewCursor", "vs/editor/common/config/editorOptions", "vs/editor/common/core/editorColorRegistry", "vs/platform/theme/common/themeService", "vs/platform/theme/common/theme", "vs/base/browser/dom", "vs/css!vs/editor/browser/viewParts/viewCursors/viewCursors"], "vs/editor/browser/viewParts/viewZones/viewZones": ["require", "exports", "vs/base/browser/fastDomNode", "vs/base/common/errors", "vs/editor/browser/view/viewPart", "vs/editor/common/core/position"], "vs/editor/browser/viewParts/whitespace/whitespace": ["require", "exports", "vs/editor/browser/view/dynamicViewOverlay", "vs/base/common/strings", "vs/editor/common/viewLayout/viewLineRenderer", "vs/editor/common/core/position", "vs/editor/common/core/editorColorRegistry", "vs/css!vs/editor/browser/viewParts/whitespace/whitespace"], "vs/editor/common/viewEventHandler": ["require", "exports", "vs/base/common/lifecycle"], "vs/editor/common/viewLayout/viewLinesViewportData": ["require", "exports", "vs/editor/common/core/range"], "vs/editor/common/viewModel/viewContext": ["require", "exports", "vs/editor/common/editorTheme"], "vs/base/browser/trustedTypes": ["require", "exports", "vs/base/common/errors"], "vs/editor/common/core/stringBuilder": ["require", "exports", "vs/base/common/strings", "vs/base/common/platform", "vs/base/common/buffer"], "vs/editor/common/modelLineProjectionData": ["require", "exports", "vs/base/common/assert", "vs/editor/common/core/position", "vs/editor/common/model"], "vs/editor/common/textModelEvents": ["require", "exports"], "vs/editor/common/languages/languageConfiguration": ["require", "exports"], "vs/editor/common/languages/supports/characterPair": ["require", "exports", "vs/editor/common/languages/languageConfiguration"], "vs/editor/common/languages/supports/electricCharacter": ["require", "exports", "vs/base/common/arrays", "vs/editor/common/languages/supports", "vs/editor/common/languages/supports/richEditBrackets"], "vs/editor/common/languages/supports/indentRules": ["require", "exports"], "vs/editor/common/languages/supports/onEnter": ["require", "exports", "vs/base/common/errors", "vs/base/common/strings", "vs/editor/common/languages/languageConfiguration"], "vs/editor/common/languages/supports/richEditBrackets": ["require", "exports", "vs/base/common/strings", "vs/editor/common/core/stringBuilder", "vs/editor/common/core/range"], "vs/editor/common/languages/supports/languageBracketsConfiguration": ["require", "exports", "vs/base/common/cache", "vs/editor/common/languages/supports/richEditBrackets"], "vs/editor/common/core/eolCounter": ["require", "exports"], "vs/editor/common/core/indentation": ["require", "exports", "vs/base/common/strings", "vs/editor/common/core/cursorColumns"], "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsImpl": ["require", "exports", "vs/base/common/arrays", "vs/base/common/event", "vs/base/common/lifecycle", "vs/editor/common/core/range", "vs/editor/common/languages/supports", "vs/editor/common/languages/supports/richEditBrackets", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/bracketPairsTree"], "vs/editor/common/model/bracketPairsTextModelPart/colorizedBracketPairsDecorationProvider": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle", "vs/editor/common/core/range", "vs/editor/common/core/editorColorRegistry", "vs/platform/theme/common/themeService"], "vs/editor/common/model/editStack": ["require", "exports", "vs/nls", "vs/base/common/errors", "vs/editor/common/core/selection", "vs/base/common/uri", "vs/editor/common/core/textChange", "vs/base/common/buffer", "vs/base/common/resources"], "vs/editor/common/model/guidesTextModelPart": ["require", "exports", "vs/base/common/arraysFind", "vs/base/common/strings", "vs/editor/common/core/cursorColumns", "vs/editor/common/core/range", "vs/editor/common/model/textModelPart", "vs/editor/common/model/utils", "vs/editor/common/textModelGuides", "vs/base/common/errors"], "vs/editor/common/model/indentationGuesser": ["require", "exports"], "vs/editor/common/model/intervalTree": ["require", "exports"], "vs/editor/common/model/pieceTreeTextBuffer/pieceTreeTextBuffer": ["require", "exports", "vs/base/common/event", "vs/base/common/strings", "vs/editor/common/core/range", "vs/editor/common/model", "vs/editor/common/model/pieceTreeTextBuffer/pieceTreeBase", "vs/editor/common/core/eolCounter", "vs/editor/common/core/textChange", "vs/base/common/lifecycle"], "vs/editor/common/model/pieceTreeTextBuffer/pieceTreeTextBufferBuilder": ["require", "exports", "vs/base/common/strings", "vs/editor/common/model/pieceTreeTextBuffer/pieceTreeBase", "vs/editor/common/model/pieceTreeTextBuffer/pieceTreeTextBuffer"], "vs/editor/common/model/tokenizationTextModelPart": ["require", "exports", "vs/base/common/errors", "vs/base/common/event", "vs/base/common/lifecycle", "vs/editor/common/core/eolCounter", "vs/editor/common/core/lineRange", "vs/editor/common/core/position", "vs/editor/common/core/wordHelper", "vs/editor/common/languages", "vs/editor/common/languages/language", "vs/editor/common/languages/languageConfigurationRegistry", "vs/editor/common/model/textModelPart", "vs/editor/common/model/textModelTokens", "vs/editor/common/model/tokens", "vs/editor/common/model/treeSitterTokens", "vs/editor/common/services/treeSitterParserService", "vs/editor/common/tokens/contiguousMultilineTokensBuilder", "vs/editor/common/tokens/contiguousTokensStore", "vs/editor/common/tokens/sparseTokensStore"], "vs/editor/common/model/tokens": ["require", "exports", "vs/base/common/arrays", "vs/base/common/async", "vs/base/common/event", "vs/base/common/lifecycle", "vs/editor/common/core/lineRange"], "vs/platform/undoRedo/common/undoRedo": ["require", "exports", "vs/platform/instantiation/common/instantiation"], "vs/editor/common/cursor/cursor": ["require", "exports", "vs/base/common/errors", "vs/base/common/strings", "vs/editor/common/cursor/cursorCollection", "vs/editor/common/cursor<PERSON><PERSON>mon", "vs/editor/common/cursor/cursorContext", "vs/editor/common/cursor/cursorDeleteOperations", "vs/editor/common/cursor/cursorTypeOperations", "vs/editor/common/cursor/cursorTypeEditOperations", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/editor/common/textModelEvents", "vs/editor/common/viewEvents", "vs/base/common/lifecycle", "vs/editor/common/viewModelEventDispatcher"], "vs/editor/common/languages/textToHtmlTokenizer": ["require", "exports", "vs/base/common/strings", "vs/editor/common/tokens/lineTokens", "vs/editor/common/languages", "vs/editor/common/languages/nullTokenize"], "vs/editor/common/viewEvents": ["require", "exports"], "vs/editor/common/viewLayout/viewLayout": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/scrollable", "vs/editor/common/viewLayout/linesLayout", "vs/editor/common/viewModel", "vs/editor/common/viewModelEventDispatcher"], "vs/editor/common/viewModel/minimapTokensColorTracker": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle", "vs/editor/common/core/rgba", "vs/editor/common/languages"], "vs/editor/common/viewModel": ["require", "exports", "vs/base/common/arrays", "vs/base/common/strings", "vs/editor/common/core/range"], "vs/editor/common/viewModelEventDispatcher": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle"], "vs/editor/common/viewModel/viewModelLines": ["require", "exports", "vs/base/common/arrays", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/textModelGuides", "vs/editor/common/model/textModel", "vs/editor/common/textModelEvents", "vs/editor/common/viewEvents", "vs/editor/common/viewModel/modelLineProjection", "vs/editor/common/model/prefixSumComputer", "vs/editor/common/viewModel"], "vs/editor/common/viewModel/glyphLanesModel": ["require", "exports", "vs/editor/common/model"], "vs/platform/theme/common/colors/baseColors": ["require", "exports", "vs/nls", "vs/base/common/color", "vs/platform/theme/common/colorUtils"], "vs/platform/theme/common/colors/chartsColors": ["require", "exports", "vs/nls", "vs/platform/theme/common/colorUtils", "vs/platform/theme/common/colors/baseColors", "vs/platform/theme/common/colors/editorColors", "vs/platform/theme/common/colors/minimapColors"], "vs/platform/theme/common/colors/editorColors": ["require", "exports", "vs/nls", "vs/base/common/color", "vs/platform/theme/common/colorUtils", "vs/platform/theme/common/colors/baseColors", "vs/platform/theme/common/colors/miscColors"], "vs/platform/theme/common/colors/inputColors": ["require", "exports", "vs/nls", "vs/base/common/color", "vs/platform/theme/common/colorUtils", "vs/platform/theme/common/colors/baseColors", "vs/platform/theme/common/colors/editorColors"], "vs/platform/theme/common/colors/listColors": ["require", "exports", "vs/nls", "vs/base/common/color", "vs/platform/theme/common/colorUtils", "vs/platform/theme/common/colors/baseColors", "vs/platform/theme/common/colors/editorColors"], "vs/platform/theme/common/colors/menuColors": ["require", "exports", "vs/nls", "vs/platform/theme/common/colorUtils", "vs/platform/theme/common/colors/baseColors", "vs/platform/theme/common/colors/inputColors", "vs/platform/theme/common/colors/listColors"], "vs/platform/theme/common/colors/minimapColors": ["require", "exports", "vs/nls", "vs/base/common/color", "vs/platform/theme/common/colorUtils", "vs/platform/theme/common/colors/editorColors", "vs/platform/theme/common/colors/miscColors"], "vs/platform/theme/common/colors/miscColors": ["require", "exports", "vs/nls", "vs/base/common/color", "vs/platform/theme/common/colorUtils", "vs/platform/theme/common/colors/baseColors"], "vs/platform/theme/common/colors/quickpickColors": ["require", "exports", "vs/nls", "vs/base/common/color", "vs/platform/theme/common/colorUtils", "vs/platform/theme/common/colors/editorColors", "vs/platform/theme/common/colors/listColors"], "vs/platform/theme/common/colors/searchColors": ["require", "exports", "vs/nls", "vs/platform/theme/common/colorUtils", "vs/platform/theme/common/colors/baseColors", "vs/platform/theme/common/colors/editorColors"], "vs/editor/common/services/markerDecorations": ["require", "exports", "vs/platform/instantiation/common/instantiation"], "vs/css!vs/base/browser/ui/aria/aria": [], "vs/editor/common/languages/supports": ["require", "exports"], "vs/editor/common/commands/surroundSelectionCommand": ["require", "exports", "vs/editor/common/core/range", "vs/editor/common/core/selection"], "vs/base/common/codiconsUtil": ["require", "exports", "vs/base/common/types"], "vs/base/common/codiconsLibrary": ["require", "exports", "vs/base/common/codiconsUtil"], "vs/editor/common/languages/enterAction": ["require", "exports", "vs/editor/common/languages/languageConfiguration", "vs/editor/common/languages/languageConfigurationRegistry", "vs/editor/common/languages/supports/indentationLineProcessor"], "vs/editor/browser/widget/diffEditor/diffEditorWidget": ["require", "exports", "vs/base/browser/dom", "vs/base/common/arraysFind", "vs/base/common/errors", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/observable", "vs/base/common/observableInternal/derived", "vs/editor/browser/editorExtensions", "vs/editor/browser/services/codeEditorService", "vs/editor/browser/stableEditorScroll", "vs/editor/browser/widget/codeEditor/codeEditorWidget", "vs/editor/browser/widget/diffEditor/components/accessibleDiffViewer", "vs/editor/browser/widget/diffEditor/components/diffEditorDecorations", "vs/editor/browser/widget/diffEditor/components/diffEditorSash", "vs/editor/browser/widget/diffEditor/components/diffEditorViewZones/diffEditorViewZones", "vs/editor/browser/widget/diffEditor/features/gutterFeature", "vs/editor/browser/widget/diffEditor/features/hideUnchangedRegionsFeature", "vs/editor/browser/widget/diffEditor/features/movedBlocksLinesFeature", "vs/editor/browser/widget/diffEditor/features/overviewRulerFeature", "vs/editor/browser/widget/diffEditor/features/revertButtonsFeature", "vs/editor/browser/widget/diffEditor/utils", "vs/base/common/hotReloadHelpers", "vs/platform/observable/common/platformObservableUtils", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/editor<PERSON><PERSON><PERSON>", "vs/editor/common/editorContext<PERSON>eys", "vs/platform/accessibilitySignal/browser/accessibilitySignalService", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation", "vs/platform/instantiation/common/serviceCollection", "vs/platform/progress/common/progress", "vs/editor/browser/widget/diffEditor/components/diffEditorEditors", "vs/editor/browser/widget/diffEditor/delegatingEditorImpl", "vs/editor/browser/widget/diffEditor/diffEditorOptions", "vs/editor/browser/widget/diffEditor/diffEditorViewModel", "vs/css!vs/editor/browser/widget/diffEditor/style"], "vs/editor/common/cursor/cursorAtomicMoveOperations": ["require", "exports", "vs/editor/common/core/cursorColumns"], "vs/base/browser/event": ["require", "exports", "vs/base/common/event"], "vs/base/common/mime": ["require", "exports"], "vs/editor/browser/controller/textAreaState": ["require", "exports", "vs/base/common/strings", "vs/editor/common/core/range"], "vs/base/common/dataTransfer": ["require", "exports", "vs/base/common/arrays", "vs/base/common/iterator", "vs/base/common/uuid"], "vs/base/common/uuid": ["require", "exports"], "vs/editor/browser/dnd": ["require", "exports", "vs/base/browser/dnd", "vs/base/common/dataTransfer", "vs/base/common/mime", "vs/base/common/uri", "vs/platform/dnd/browser/dnd"], "vs/editor/contrib/dropOrPasteInto/browser/edit": ["require", "exports", "vs/editor/browser/services/bulkEditService", "vs/editor/contrib/snippet/browser/snippetParser"], "vs/editor/contrib/dropOrPasteInto/browser/postEditWidget": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/button/button", "vs/base/common/actions", "vs/base/common/errorMessage", "vs/base/common/errors", "vs/base/common/event", "vs/base/common/lifecycle", "vs/editor/browser/services/bulkEditService", "vs/editor/contrib/dropOrPasteInto/browser/edit", "vs/nls", "vs/platform/contextkey/common/contextkey", "vs/platform/contextview/browser/contextView", "vs/platform/instantiation/common/instantiation", "vs/platform/keybinding/common/keybinding", "vs/platform/notification/common/notification", "vs/css!vs/editor/contrib/dropOrPasteInto/browser/postEditWidget"], "vs/editor/common/config/diffEditor": ["require", "exports"], "vs/editor/contrib/codeAction/browser/codeAction": ["require", "exports", "vs/base/common/arrays", "vs/base/common/cancellation", "vs/base/common/errors", "vs/base/common/lifecycle", "vs/base/common/uri", "vs/editor/browser/services/bulkEditService", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/editor/common/services/languageFeatures", "vs/editor/common/services/model", "vs/editor/contrib/editorState/browser/editorState", "vs/nls", "vs/platform/commands/common/commands", "vs/platform/notification/common/notification", "vs/platform/progress/common/progress", "vs/platform/telemetry/common/telemetry", "vs/editor/contrib/codeAction/common/types", "vs/base/common/hierarchicalKind"], "vs/editor/contrib/codeAction/browser/codeActionKeybindingResolver": ["require", "exports", "vs/base/common/hierarchicalKind", "vs/base/common/lazy", "vs/editor/contrib/codeAction/browser/codeAction", "vs/editor/contrib/codeAction/common/types", "vs/platform/keybinding/common/keybinding"], "vs/editor/contrib/codeAction/browser/codeActionMenu": ["require", "exports", "vs/base/common/codicons", "vs/editor/contrib/codeAction/common/types", "vs/nls", "vs/base/common/hierarchicalKind", "vs/base/browser/ui/codicons/codiconStyles", "vs/editor/contrib/symbolIcons/browser/symbolIcons"], "vs/platform/actionWidget/browser/actionWidget": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/actionbar/actionbar", "vs/base/common/lifecycle", "vs/nls", "vs/platform/actionWidget/browser/actionList", "vs/platform/actions/common/actions", "vs/platform/contextkey/common/contextkey", "vs/platform/contextview/browser/contextView", "vs/platform/instantiation/common/extensions", "vs/platform/instantiation/common/instantiation", "vs/platform/theme/common/colorRegistry", "vs/css!vs/platform/actionWidget/browser/actionWidget"], "vs/editor/contrib/codeAction/common/types": ["require", "exports", "vs/base/common/errors", "vs/base/common/hierarchicalKind"], "vs/editor/contrib/codeAction/browser/codeActionModel": ["require", "exports", "vs/base/common/async", "vs/base/common/errors", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/resources", "vs/editor/common/config/editorOptions", "vs/editor/common/core/position", "vs/editor/common/core/selection", "vs/platform/contextkey/common/contextkey", "vs/platform/progress/common/progress", "vs/editor/contrib/codeAction/common/types", "vs/editor/contrib/codeAction/browser/codeAction", "vs/base/common/hierarchicalKind", "vs/base/common/stopwatch"], "vs/base/browser/touch": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/window", "vs/base/common/arrays", "vs/base/common/decorators", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/linkedList"], "vs/editor/common/model/utils": ["require", "exports"], "vs/css!vs/editor/contrib/codeAction/browser/lightBulbWidget": [], "vs/platform/jsonschemas/common/jsonContributionRegistry": ["require", "exports", "vs/base/common/event", "vs/platform/registry/common/platform"], "vs/editor/browser/editorDom": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/globalPointerMoveMonitor", "vs/base/browser/mouseEvent", "vs/base/common/async", "vs/base/common/lifecycle", "vs/platform/theme/common/colorRegistry"], "vs/editor/contrib/colorPicker/browser/color": ["require", "exports", "vs/base/common/cancellation", "vs/base/common/errors", "vs/base/common/uri", "vs/editor/common/core/range", "vs/editor/common/services/model", "vs/platform/commands/common/commands", "vs/editor/common/services/languageFeatures", "vs/editor/contrib/colorPicker/browser/defaultDocumentColorProvider", "vs/platform/configuration/common/configuration"], "vs/editor/contrib/colorPicker/browser/colorPickerModel": ["require", "exports", "vs/base/common/event"], "vs/editor/contrib/colorPicker/browser/colorPickerWidget": ["require", "exports", "vs/base/browser/pixelRatio", "vs/base/browser/dom", "vs/base/browser/globalPointerMoveMonitor", "vs/base/browser/ui/widget", "vs/base/common/codicons", "vs/base/common/color", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/themables", "vs/nls", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/iconRegistry", "vs/css!vs/editor/contrib/colorPicker/browser/colorPicker"], "vs/editor/contrib/hover/browser/hoverActionIds": ["require", "exports", "vs/nls"], "vs/editor/contrib/inlineCompletions/browser/hintsWidget/inlineCompletionsHintsWidget": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/actionbar/actionViewItems", "vs/base/browser/ui/keybindingLabel/keybindingLabel", "vs/base/common/actions", "vs/base/common/arrays", "vs/base/common/async", "vs/base/common/codicons", "vs/base/common/lifecycle", "vs/base/common/observable", "vs/base/common/observableInternal/derived", "vs/base/common/platform", "vs/base/common/themables", "vs/editor/common/core/position", "vs/editor/common/languages", "vs/editor/contrib/inlineCompletions/browser/controller/commandIds", "vs/nls", "vs/platform/actions/browser/menuEntryActionViewItem", "vs/platform/actions/browser/toolbar", "vs/platform/actions/common/actions", "vs/platform/commands/common/commands", "vs/platform/contextkey/common/contextkey", "vs/platform/contextview/browser/contextView", "vs/platform/instantiation/common/instantiation", "vs/platform/keybinding/common/keybinding", "vs/platform/telemetry/common/telemetry", "vs/platform/theme/common/iconRegistry", "vs/css!vs/editor/contrib/inlineCompletions/browser/hintsWidget/inlineCompletionsHintsWidget"], "vs/editor/contrib/hover/browser/hoverUtils": ["require", "exports", "vs/base/browser/dom"], "vs/editor/contrib/hover/browser/contentHoverWidgetWrapper": ["require", "exports", "vs/base/browser/dom", "vs/base/common/lifecycle", "vs/editor/common/languages", "vs/editor/contrib/hover/browser/hoverOperation", "vs/editor/contrib/hover/browser/hoverTypes", "vs/platform/instantiation/common/instantiation", "vs/platform/keybinding/common/keybinding", "vs/editor/contrib/hover/browser/contentHoverWidget", "vs/editor/contrib/hover/browser/contentHoverComputer", "vs/editor/contrib/hover/browser/contentHoverTypes", "vs/base/common/event", "vs/editor/contrib/hover/browser/contentHoverRendered", "vs/editor/contrib/hover/browser/hoverUtils"], "vs/base/browser/ui/iconLabel/iconLabels": ["require", "exports", "vs/base/browser/dom", "vs/base/common/themables"], "vs/css!vs/editor/contrib/codelens/browser/codelensWidget": [], "vs/base/common/numbers": ["require", "exports"], "vs/platform/environment/common/environment": ["require", "exports", "vs/platform/instantiation/common/instantiation"], "vs/editor/contrib/hover/browser/contentHoverStatusBar": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/hover/hoverWidget", "vs/base/common/lifecycle", "vs/platform/keybinding/common/keybinding"], "vs/editor/contrib/colorPicker/browser/defaultDocumentColorProvider": ["require", "exports", "vs/base/common/color", "vs/base/common/lifecycle", "vs/editor/common/services/languageFeatures", "vs/editor/common/editorFeatures", "vs/editor/common/services/editorW<PERSON>ker"], "vs/base/browser/dnd": ["require", "exports", "vs/base/common/mime"], "vs/base/browser/ui/hover/hoverDelegateFactory": ["require", "exports", "vs/base/common/lazy"], "vs/base/browser/ui/selectBox/selectBox": ["require", "exports", "vs/base/browser/ui/selectBox/selectBoxCustom", "vs/base/browser/ui/selectBox/selectBoxNative", "vs/base/browser/ui/widget", "vs/base/common/platform", "vs/css!vs/base/browser/ui/selectBox/selectBox"], "vs/base/browser/ui/hover/hoverDelegate2": ["require", "exports"], "vs/css!vs/base/browser/ui/actionbar/actionbar": [], "vs/base/common/ternarySearchTree": ["require", "exports", "vs/base/common/strings"], "vs/editor/common/services/treeViewsDnd": ["require", "exports"], "vs/editor/common/services/treeViewsDndService": ["require", "exports", "vs/platform/instantiation/common/extensions", "vs/platform/instantiation/common/instantiation", "vs/editor/common/services/treeViewsDnd"], "vs/platform/dnd/browser/dnd": ["require", "exports", "vs/platform/registry/common/platform"], "vs/editor/contrib/find/browser/findDecorations": ["require", "exports", "vs/editor/common/core/range", "vs/editor/common/model", "vs/editor/common/model/textModel", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/themeService"], "vs/editor/contrib/find/browser/replaceAllCommand": ["require", "exports", "vs/editor/common/core/range"], "vs/editor/contrib/find/browser/replacePattern": ["require", "exports", "vs/base/common/search"], "vs/base/browser/ui/findinput/findInputToggles": ["require", "exports", "vs/base/browser/ui/hover/hoverDelegateFactory", "vs/base/browser/ui/toggle/toggle", "vs/base/common/codicons", "vs/nls"], "vs/base/browser/ui/widget": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/keyboardEvent", "vs/base/browser/mouseEvent", "vs/base/browser/touch", "vs/base/common/lifecycle"], "vs/css!vs/editor/contrib/find/browser/findOptionsWidget": [], "vs/base/browser/ui/toggle/toggle": ["require", "exports", "vs/base/browser/ui/widget", "vs/base/common/themables", "vs/base/common/event", "vs/base/browser/ui/hover/hoverDelegateFactory", "vs/base/browser/ui/hover/hoverDelegate2", "vs/css!vs/base/browser/ui/toggle/toggle"], "vs/base/browser/ui/sash/sash": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/event", "vs/base/browser/touch", "vs/base/common/async", "vs/base/common/decorators", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/platform", "vs/css!vs/base/browser/ui/sash/sash"], "vs/platform/history/browser/contextScopedHistoryWidget": ["require", "exports", "vs/base/browser/ui/findinput/findInput", "vs/base/browser/ui/findinput/replaceInput", "vs/platform/contextkey/common/contextkey", "vs/platform/keybinding/common/keybindingsRegistry", "vs/nls", "vs/base/common/lifecycle", "vs/base/browser/dom"], "vs/platform/history/browser/historyWidgetKeybindingHint": ["require", "exports"], "vs/platform/theme/browser/defaultStyles": ["require", "exports", "vs/platform/theme/common/colorRegistry", "vs/base/common/color"], "vs/css!vs/editor/contrib/find/browser/findWidget": [], "vs/base/common/observableInternal/base": ["require", "exports", "vs/base/common/equals", "vs/base/common/observableInternal/debugName", "vs/base/common/observableInternal/logging"], "vs/editor/contrib/inlineCompletions/browser/controller/commandIds": ["require", "exports"], "vs/editor/contrib/inlineCompletions/browser/controller/inlineCompletionContextKeys": ["require", "exports", "vs/base/common/observable", "vs/base/common/strings", "vs/editor/common/core/cursorColumns", "vs/platform/contextkey/common/contextkey", "vs/base/common/lifecycle", "vs/nls"], "vs/editor/browser/widget/markdownRenderer/browser/markdownRenderer": ["require", "exports", "vs/base/browser/markdownRenderer", "vs/base/browser/trustedTypes", "vs/base/common/errors", "vs/base/common/event", "vs/base/common/lifecycle", "vs/editor/browser/config/domFontInfo", "vs/editor/common/languages/language", "vs/editor/common/languages/modesRegistry", "vs/editor/common/languages/textToHtmlTokenizer", "vs/platform/opener/common/opener", "vs/css!vs/editor/browser/widget/markdownRenderer/browser/renderedMarkdown"], "vs/base/browser/domObservable": ["require", "exports", "vs/base/browser/dom", "vs/base/common/lifecycle", "vs/base/common/observable"], "vs/base/common/observableInternal/derived": ["require", "exports", "vs/base/common/assert", "vs/base/common/equals", "vs/base/common/lifecycle", "vs/base/common/observableInternal/base", "vs/base/common/observableInternal/debugName", "vs/base/common/observableInternal/logging"], "vs/base/common/observableInternal/utils": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/observableInternal/base", "vs/base/common/observableInternal/debugName", "vs/base/common/observableInternal/derived", "vs/base/common/observableInternal/logging", "vs/base/common/equals"], "vs/editor/browser/observableCodeEditor": ["require", "exports", "vs/base/common/equals", "vs/base/common/lifecycle", "vs/base/common/observable", "vs/base/common/observableInternal/base", "vs/base/common/observableInternal/derived", "vs/editor/common/core/selection"], "vs/editor/contrib/inlineCompletions/browser/view/ghostTextView": ["require", "exports", "vs/base/browser/trustedTypes", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/observable", "vs/base/common/strings", "vs/editor/browser/config/domFontInfo", "vs/editor/common/config/editorOptions", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/core/stringBuilder", "vs/editor/common/languages/language", "vs/editor/common/model", "vs/editor/common/tokens/lineTokens", "vs/editor/common/viewLayout/lineDecorations", "vs/editor/common/viewLayout/viewLineRenderer", "vs/editor/contrib/inlineCompletions/browser/model/ghostText", "vs/editor/contrib/inlineCompletions/browser/utils", "vs/css!vs/editor/contrib/inlineCompletions/browser/view/ghostTextView"], "vs/editor/contrib/inlineCompletions/browser/model/inlineCompletionsModel": ["require", "exports", "vs/base/common/arrays", "vs/base/common/arraysFind", "vs/base/common/equals", "vs/base/common/errors", "vs/base/common/lifecycle", "vs/base/common/observable", "vs/base/common/strings", "vs/base/common/types", "vs/editor/common/core/editOperation", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/editor/common/core/textEdit", "vs/editor/common/core/textLength", "vs/editor/common/languages", "vs/editor/common/languages/languageConfigurationRegistry", "vs/editor/contrib/inlineCompletions/browser/model/ghostText", "vs/editor/contrib/inlineCompletions/browser/model/inlineCompletionsSource", "vs/editor/contrib/inlineCompletions/browser/model/singleTextEdit", "vs/editor/contrib/inlineCompletions/browser/utils", "vs/editor/contrib/snippet/browser/snippetController2", "vs/platform/commands/common/commands", "vs/platform/instantiation/common/instantiation"], "vs/editor/contrib/inlineCompletions/browser/model/suggestWidgetAdaptor": ["require", "exports", "vs/base/common/arrays", "vs/base/common/arraysFind", "vs/base/common/event", "vs/base/common/lifecycle", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/core/textEdit", "vs/editor/common/languages", "vs/editor/contrib/inlineCompletions/browser/model/singleTextEdit", "vs/editor/contrib/snippet/browser/snippetParser", "vs/editor/contrib/snippet/browser/snippetSession", "vs/editor/contrib/suggest/browser/suggestController"], "vs/editor/contrib/editorState/browser/keybindingCancellation": ["require", "exports", "vs/editor/browser/editorExtensions", "vs/platform/contextkey/common/contextkey", "vs/base/common/cancellation", "vs/base/common/linkedList", "vs/platform/instantiation/common/instantiation", "vs/platform/instantiation/common/extensions", "vs/nls"], "vs/base/common/idGenerator": ["require", "exports"], "vs/base/browser/markdownRenderer": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/dompurify/dompurify", "vs/base/browser/event", "vs/base/browser/formattedTextRenderer", "vs/base/browser/keyboardEvent", "vs/base/browser/mouseEvent", "vs/base/browser/ui/iconLabel/iconLabels", "vs/base/common/errors", "vs/base/common/event", "vs/base/common/htmlContent", "vs/base/common/iconLabels", "vs/base/common/idGenerator", "vs/base/common/lazy", "vs/base/common/lifecycle", "vs/base/common/marked/marked", "vs/base/common/marshalling", "vs/base/common/network", "vs/base/common/objects", "vs/base/common/resources", "vs/base/common/strings", "vs/base/common/uri"], "vs/css!vs/editor/contrib/message/browser/messageController": [], "vs/base/browser/ui/actionbar/actionbar": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/keyboardEvent", "vs/base/browser/ui/actionbar/actionViewItems", "vs/base/browser/ui/hover/hoverDelegateFactory", "vs/base/common/actions", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/types", "vs/css!vs/base/browser/ui/actionbar/actionbar"], "vs/editor/contrib/zoneWidget/browser/zoneWidget": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/sash/sash", "vs/base/common/color", "vs/base/common/idGenerator", "vs/base/common/lifecycle", "vs/base/common/objects", "vs/editor/common/core/range", "vs/editor/common/model/textModel", "vs/css!vs/editor/contrib/zoneWidget/browser/zoneWidget"], "vs/platform/actions/browser/menuEntryActionViewItem": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/keyboardEvent", "vs/base/browser/ui/actionbar/actionViewItems", "vs/base/browser/ui/dropdown/dropdownActionViewItem", "vs/base/common/actions", "vs/base/common/keybindingLabels", "vs/base/common/lifecycle", "vs/base/common/platform", "vs/nls", "vs/platform/actions/common/actions", "vs/platform/action/common/action", "vs/platform/contextkey/common/contextkey", "vs/platform/contextview/browser/contextView", "vs/platform/instantiation/common/instantiation", "vs/platform/keybinding/common/keybinding", "vs/platform/notification/common/notification", "vs/platform/storage/common/storage", "vs/platform/theme/common/themeService", "vs/base/common/themables", "vs/platform/theme/common/theme", "vs/base/common/types", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/browser/defaultStyles", "vs/platform/accessibility/common/accessibility", "vs/css!vs/platform/actions/browser/menuEntryActionViewItem"], "vs/css!vs/editor/contrib/peekView/browser/media/peekViewWidget": [], "vs/base/browser/ui/scrollbar/scrollableElement": ["require", "exports", "vs/base/browser/browser", "vs/base/browser/dom", "vs/base/browser/fastDomNode", "vs/base/browser/mouseEvent", "vs/base/browser/ui/scrollbar/horizontalScrollbar", "vs/base/browser/ui/scrollbar/verticalScrollbar", "vs/base/browser/ui/widget", "vs/base/common/async", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/platform", "vs/base/common/scrollable", "vs/css!vs/base/browser/ui/scrollbar/media/scrollbars"], "vs/platform/label/common/label": ["require", "exports", "vs/platform/instantiation/common/instantiation"], "vs/platform/severityIcon/browser/severityIcon": ["require", "exports", "vs/base/common/codicons", "vs/base/common/themables", "vs/base/common/severity", "vs/css!vs/platform/severityIcon/browser/media/severityIcon"], "vs/css!vs/editor/contrib/gotoError/browser/media/gotoErrorWidget": [], "vs/base/browser/ui/hover/hoverWidget": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/keyboardEvent", "vs/base/browser/ui/scrollbar/scrollableElement", "vs/base/common/lifecycle", "vs/nls", "vs/css!vs/base/browser/ui/hover/hoverWidget"], "vs/editor/contrib/hover/browser/getHover": ["require", "exports", "vs/base/common/async", "vs/base/common/cancellation", "vs/base/common/errors", "vs/editor/browser/editorExtensions", "vs/editor/common/services/languageFeatures"], "vs/editor/contrib/hover/browser/marginHoverWidget": ["require", "exports", "vs/base/browser/dom", "vs/base/common/lifecycle", "vs/editor/browser/widget/markdownRenderer/browser/markdownRenderer", "vs/editor/common/languages/language", "vs/editor/contrib/hover/browser/hoverOperation", "vs/platform/opener/common/opener", "vs/base/browser/ui/hover/hoverWidget", "vs/editor/contrib/hover/browser/marginHoverComputer", "vs/editor/contrib/hover/browser/hoverUtils"], "vs/editor/common/languages/supports/indentationLineProcessor": ["require", "exports", "vs/base/common/strings", "vs/editor/common/languages/supports", "vs/editor/common/tokens/lineTokens"], "vs/editor/contrib/inlayHints/browser/inlayHints": ["require", "exports", "vs/base/common/errors", "vs/base/common/lifecycle", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/base/common/network", "vs/base/common/uri"], "vs/editor/contrib/inlayHints/browser/inlayHintsLocations": ["require", "exports", "vs/base/browser/dom", "vs/base/common/actions", "vs/base/common/cancellation", "vs/base/common/uuid", "vs/editor/common/core/range", "vs/editor/common/services/resolverService", "vs/editor/contrib/gotoSymbol/browser/goToCommands", "vs/editor/contrib/peekView/browser/peekView", "vs/platform/actions/common/actions", "vs/platform/commands/common/commands", "vs/platform/contextkey/common/contextkey", "vs/platform/contextview/browser/contextView", "vs/platform/instantiation/common/instantiation", "vs/platform/notification/common/notification"], "vs/base/common/extpath": ["require", "exports", "vs/base/common/path", "vs/base/common/platform", "vs/base/common/strings"], "vs/css!vs/editor/contrib/wordHighlighter/browser/highlightDecorations": [], "vs/editor/contrib/inlineEdit/browser/commandIds": ["require", "exports"], "vs/editor/contrib/inlineEdit/browser/ghostTextWidget": ["require", "exports", "vs/base/common/lifecycle", "vs/base/common/observable", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/languages/language", "vs/editor/common/model", "vs/editor/common/viewLayout/lineDecorations", "vs/editor/contrib/inlineCompletions/browser/utils", "vs/editor/browser/widget/diffEditor/registrations.contribution", "vs/css!vs/editor/contrib/inlineEdit/browser/inlineEdit"], "vs/editor/contrib/inlineCompletions/browser/model/ghostText": ["require", "exports", "vs/base/common/arrays", "vs/base/common/strings", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/core/textEdit"], "vs/editor/contrib/inlineEdit/browser/inlineEditHintsWidget": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/keybindingLabel/keybindingLabel", "vs/base/common/actions", "vs/base/common/arrays", "vs/base/common/lifecycle", "vs/base/common/observable", "vs/base/common/platform", "vs/editor/common/core/position", "vs/platform/actions/browser/menuEntryActionViewItem", "vs/platform/actions/browser/toolbar", "vs/platform/actions/common/actions", "vs/platform/commands/common/commands", "vs/platform/contextkey/common/contextkey", "vs/platform/contextview/browser/contextView", "vs/platform/instantiation/common/instantiation", "vs/platform/keybinding/common/keybinding", "vs/platform/telemetry/common/telemetry", "vs/css!vs/editor/contrib/inlineEdit/browser/inlineEditHintsWidget"], "vs/editor/contrib/inlineEdit/browser/inlineEditSideBySideWidget": ["require", "exports", "vs/base/browser/dom", "vs/base/common/cancellation", "vs/base/common/lifecycle", "vs/base/common/observable", "vs/base/common/observableInternal/derived", "vs/base/common/uri", "vs/editor/browser/observableCodeEditor", "vs/editor/browser/widget/codeEditor/embeddedCodeEditorWidget", "vs/editor/browser/widget/diffEditor/diffProviderFactoryService", "vs/editor/browser/widget/diffEditor/registrations.contribution", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/languages/modesRegistry", "vs/editor/common/model/textModel", "vs/editor/common/services/model", "vs/platform/instantiation/common/instantiation", "vs/css!vs/editor/contrib/inlineEdit/browser/inlineEditSideBySideWidget"], "vs/editor/browser/widget/diffEditor/diffProviderFactoryService": ["require", "exports", "vs/platform/instantiation/common/extensions", "vs/platform/instantiation/common/instantiation", "vs/base/common/event", "vs/base/common/stopwatch", "vs/editor/common/core/lineRange", "vs/editor/common/diff/rangeMapping", "vs/editor/common/services/editorW<PERSON>ker", "vs/platform/telemetry/common/telemetry"], "vs/base/common/equals": ["require", "exports", "vs/base/common/arrays"], "vs/base/common/hotReload": ["require", "exports", "vs/base/common/process"], "vs/base/common/hotReloadHelpers": ["require", "exports", "vs/base/common/hotReload", "vs/base/common/observable"], "vs/editor/contrib/inlineEdits/browser/consts": ["require", "exports", "vs/nls", "vs/platform/contextkey/common/contextkey"], "vs/editor/contrib/inlineEdits/browser/inlineEditsModel": ["require", "exports", "vs/base/common/async", "vs/base/common/cancellation", "vs/base/common/equals", "vs/base/common/errors", "vs/base/common/lifecycle", "vs/base/common/observable", "vs/base/common/observableInternal/derived", "vs/base/common/uri", "vs/editor/browser/widget/diffEditor/diffProviderFactoryService", "vs/editor/common/core/lineRange", "vs/editor/common/languages", "vs/editor/common/services/languageFeatures", "vs/editor/common/services/model", "vs/editor/contrib/inlineCompletions/browser/model/provideInlineCompletions", "vs/editor/contrib/inlineEdits/browser/inlineEditsWidget"], "vs/editor/contrib/inlineEdits/browser/inlineEditsWidget": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/fonts", "vs/base/common/lifecycle", "vs/base/common/observable", "vs/base/common/observableInternal/derived", "vs/editor/browser/editorExtensions", "vs/editor/browser/observableCodeEditor", "vs/editor/browser/widget/codeEditor/embeddedCodeEditorWidget", "vs/editor/browser/widget/diffEditor/registrations.contribution", "vs/editor/browser/widget/diffEditor/utils", "vs/editor/common/languages/modesRegistry", "vs/editor/common/model/textModel", "vs/editor/contrib/contextmenu/browser/contextmenu", "vs/editor/contrib/placeholderText/browser/placeholderTextContribution", "vs/editor/contrib/suggest/browser/suggestController", "vs/platform/instantiation/common/instantiation", "vs/css!vs/editor/contrib/inlineEdits/browser/inlineEditsWidget"], "vs/platform/observable/common/platformObservableUtils": ["require", "exports", "vs/base/common/observable", "vs/base/common/observableInternal/utils"], "vs/css!vs/editor/contrib/parameterHints/browser/parameterHints": [], "vs/base/browser/ui/list/listWidget": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/event", "vs/base/browser/keyboardEvent", "vs/base/browser/touch", "vs/base/browser/ui/aria/aria", "vs/base/browser/ui/list/splice", "vs/base/common/arrays", "vs/base/common/async", "vs/base/common/color", "vs/base/common/decorators", "vs/base/common/event", "vs/base/common/filters", "vs/base/common/lifecycle", "vs/base/common/numbers", "vs/base/common/platform", "vs/base/common/types", "vs/base/browser/ui/list/list", "vs/base/browser/ui/list/listView", "vs/base/browser/mouseEvent", "vs/base/common/observable", "vs/css!vs/base/browser/ui/list/list"], "vs/css!vs/editor/contrib/rename/browser/renameWidget": [], "vs/editor/common/services/semanticTokensDto": ["require", "exports", "vs/base/common/buffer", "vs/base/common/platform"], "vs/editor/common/tokens/sparseMultilineTokens": ["require", "exports", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/core/eolCounter"], "vs/editor/contrib/snippet/browser/snippetVariables": ["require", "exports", "vs/base/common/labels", "vs/base/common/path", "vs/base/common/resources", "vs/base/common/strings", "vs/base/common/uuid", "vs/editor/common/languages/languageConfigurationRegistry", "vs/editor/contrib/snippet/browser/snippetParser", "vs/nls", "vs/platform/workspace/common/workspace"], "vs/css!vs/editor/contrib/snippet/browser/snippetSession": [], "vs/platform/action/common/actionCommonCategories": ["require", "exports", "vs/nls"], "vs/editor/contrib/stickyScroll/browser/stickyScrollWidget": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/trustedTypes", "vs/base/common/arrays", "vs/base/common/lifecycle", "vs/base/common/themables", "vs/editor/browser/viewParts/lines/viewLine", "vs/editor/browser/widget/codeEditor/embeddedCodeEditorWidget", "vs/editor/common/core/position", "vs/editor/common/core/stringBuilder", "vs/editor/common/viewLayout/lineDecorations", "vs/editor/common/viewLayout/viewLineRenderer", "vs/editor/contrib/folding/browser/foldingDecorations", "vs/css!vs/editor/contrib/stickyScroll/browser/stickyScroll"], "vs/editor/contrib/stickyScroll/browser/stickyScrollProvider": ["require", "exports", "vs/base/common/lifecycle", "vs/editor/common/services/languageFeatures", "vs/base/common/cancellation", "vs/base/common/async", "vs/base/common/arrays", "vs/base/common/event", "vs/editor/common/languages/languageConfigurationRegistry", "vs/editor/contrib/stickyScroll/browser/stickyScrollModelProvider"], "vs/editor/contrib/stickyScroll/browser/stickyScrollElement": ["require", "exports"], "vs/base/common/naturalLanguage/korean": ["require", "exports"], "vs/editor/contrib/suggest/browser/suggestWidgetStatus": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/actionbar/actionbar", "vs/base/common/lifecycle", "vs/platform/actions/browser/menuEntryActionViewItem", "vs/platform/actions/common/actions", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation"], "vs/base/browser/ui/resizable/resizable": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/sash/sash", "vs/base/common/event", "vs/base/common/lifecycle"], "vs/editor/contrib/suggest/browser/suggestWidgetDetails": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/scrollbar/scrollableElement", "vs/base/common/codicons", "vs/base/common/themables", "vs/base/common/event", "vs/base/common/htmlContent", "vs/base/common/lifecycle", "vs/editor/browser/widget/markdownRenderer/browser/markdownRenderer", "vs/base/browser/ui/resizable/resizable", "vs/nls", "vs/platform/instantiation/common/instantiation"], "vs/editor/contrib/suggest/browser/suggestWidgetRenderer": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/iconLabel/iconLabel", "vs/base/common/codicons", "vs/base/common/themables", "vs/base/common/event", "vs/base/common/filters", "vs/base/common/lifecycle", "vs/base/common/uri", "vs/editor/common/languages", "vs/editor/common/services/getIconClasses", "vs/editor/common/services/model", "vs/editor/common/languages/language", "vs/nls", "vs/platform/files/common/files", "vs/platform/theme/common/iconRegistry", "vs/platform/theme/common/themeService", "vs/editor/contrib/suggest/browser/suggestWidgetDetails"], "vs/css!vs/editor/contrib/suggest/browser/media/suggest": [], "vs/base/common/glob": ["require", "exports", "vs/base/common/async", "vs/base/common/extpath", "vs/base/common/map", "vs/base/common/path", "vs/base/common/platform", "vs/base/common/strings"], "vs/platform/opener/browser/link": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/event", "vs/base/browser/keyboardEvent", "vs/base/browser/touch", "vs/base/common/event", "vs/base/common/lifecycle", "vs/platform/opener/common/opener", "vs/base/browser/ui/hover/hoverDelegateFactory", "vs/platform/hover/browser/hover", "vs/css!vs/platform/opener/browser/link"], "vs/css!vs/editor/contrib/unicodeHighlighter/browser/bannerController": [], "vs/base/common/observableInternal/autorun": ["require", "exports", "vs/base/common/assert", "vs/base/common/lifecycle", "vs/base/common/observableInternal/debugName", "vs/base/common/observableInternal/logging"], "vs/base/common/observableInternal/promise": ["require", "exports", "vs/base/common/observableInternal/autorun", "vs/base/common/observableInternal/base", "vs/base/common/errors"], "vs/base/common/observableInternal/api": ["require", "exports", "vs/base/common/equals", "vs/base/common/observableInternal/base", "vs/base/common/observableInternal/debugName", "vs/base/common/observableInternal/lazyObservableValue"], "vs/base/common/observableInternal/logging": ["require", "exports"], "vs/editor/common/core/textLength": ["require", "exports", "vs/editor/common/core/position", "vs/editor/common/core/range"], "vs/editor/browser/config/charWidthReader": ["require", "exports", "vs/editor/browser/config/domFontInfo"], "vs/editor/browser/services/editorWorkerService": ["require", "exports", "vs/base/common/async", "vs/base/common/lifecycle", "vs/base/common/worker/simpleWorker", "vs/base/browser/defaultWorkerFactory", "vs/editor/common/core/range", "vs/editor/common/languages/languageConfigurationRegistry", "vs/editor/common/services/editorSimpleWorker", "vs/editor/common/services/model", "vs/editor/common/services/textResourceConfiguration", "vs/base/common/arrays", "vs/platform/log/common/log", "vs/base/common/stopwatch", "vs/base/common/errors", "vs/editor/common/services/languageFeatures", "vs/editor/common/diff/linesDiffComputer", "vs/editor/common/diff/rangeMapping", "vs/editor/common/core/lineRange", "vs/base/browser/window", "vs/base/browser/dom", "vs/editor/common/services/textModelSync/textModelSync.impl", "vs/editor/common/services/editorWorkerHost"], "vs/editor/common/viewLayout/viewLineRenderer": ["require", "exports", "vs/nls", "vs/base/common/strings", "vs/editor/common/core/stringBuilder", "vs/editor/common/viewLayout/lineDecorations", "vs/editor/common/viewLayout/linePart"], "vs/editor/standalone/browser/standaloneCodeEditorService": ["require", "exports", "vs/base/browser/dom", "vs/base/common/network", "vs/editor/browser/services/abstractCodeEditorService", "vs/editor/browser/services/codeEditorService", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/extensions", "vs/platform/theme/common/themeService"], "vs/platform/configuration/common/configurationModels": ["require", "exports", "vs/base/common/arrays", "vs/base/common/map", "vs/base/common/objects", "vs/base/common/types", "vs/base/common/uri", "vs/platform/configuration/common/configuration", "vs/platform/configuration/common/configurationRegistry", "vs/platform/registry/common/platform"], "vs/platform/keybinding/common/abstractKeybindingService": ["require", "exports", "vs/base/common/async", "vs/base/common/errors", "vs/base/common/event", "vs/base/common/ime", "vs/base/common/lifecycle", "vs/nls", "vs/platform/keybinding/common/keybindingResolver"], "vs/platform/keybinding/common/keybindingResolver": ["require", "exports", "vs/platform/contextkey/common/contextkey"], "vs/platform/keybinding/common/resolvedKeybindingItem": ["require", "exports"], "vs/platform/keybinding/common/usLayoutResolvedKeybinding": ["require", "exports", "vs/base/common/keyCodes", "vs/base/common/keybindings", "vs/platform/keybinding/common/baseResolvedKeybinding", "vs/platform/keybinding/common/resolvedKeybindingItem"], "vs/platform/layout/browser/layoutService": ["require", "exports", "vs/platform/instantiation/common/instantiation"], "vs/platform/contextview/browser/contextViewService": ["require", "exports", "vs/base/browser/ui/contextview/contextview", "vs/base/common/lifecycle", "vs/platform/layout/browser/layoutService", "vs/base/browser/dom"], "vs/editor/common/services/languageService": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle", "vs/editor/common/services/languagesRegistry", "vs/base/common/arrays", "vs/editor/common/languages", "vs/editor/common/languages/modesRegistry", "vs/base/common/observable"], "vs/platform/contextview/browser/contextMenuService": ["require", "exports", "vs/base/browser/dom", "vs/base/common/actions", "vs/base/common/event", "vs/base/common/lifecycle", "vs/platform/actions/browser/menuEntryActionViewItem", "vs/platform/actions/common/actions", "vs/platform/contextkey/common/contextkey", "vs/platform/keybinding/common/keybinding", "vs/platform/notification/common/notification", "vs/platform/telemetry/common/telemetry", "vs/platform/contextview/browser/contextMenuHandler", "vs/platform/contextview/browser/contextView"], "vs/editor/browser/services/openerService": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/window", "vs/base/common/cancellation", "vs/base/common/linkedList", "vs/base/common/map", "vs/base/common/marshalling", "vs/base/common/network", "vs/base/common/resources", "vs/base/common/uri", "vs/editor/browser/services/codeEditorService", "vs/platform/commands/common/commands", "vs/platform/editor/common/editor", "vs/platform/opener/common/opener"], "vs/editor/common/services/markerDecorationsService": ["require", "exports", "vs/platform/markers/common/markers", "vs/base/common/lifecycle", "vs/editor/common/model", "vs/platform/theme/common/themeService", "vs/editor/common/core/editorColorRegistry", "vs/editor/common/services/model", "vs/editor/common/core/range", "vs/base/common/network", "vs/base/common/event", "vs/platform/theme/common/colorRegistry", "vs/base/common/map", "vs/base/common/collections"], "vs/editor/common/services/modelService": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/platform", "vs/editor/common/model/textModel", "vs/editor/common/core/textModelDefaults", "vs/editor/common/languages/modesRegistry", "vs/editor/common/services/textResourceConfiguration", "vs/platform/configuration/common/configuration", "vs/platform/undoRedo/common/undoRedo", "vs/base/common/hash", "vs/editor/common/model/editStack", "vs/base/common/network", "vs/base/common/objects", "vs/platform/instantiation/common/instantiation"], "vs/editor/standalone/browser/quickInput/standaloneQuickInputService": ["require", "exports", "vs/base/common/event", "vs/editor/browser/editorExtensions", "vs/platform/theme/common/themeService", "vs/base/common/cancellation", "vs/platform/instantiation/common/instantiation", "vs/platform/contextkey/common/contextkey", "vs/editor/standalone/browser/standaloneLayoutService", "vs/editor/browser/services/codeEditorService", "vs/platform/quickinput/browser/quickInputService", "vs/base/common/functional", "vs/platform/configuration/common/configuration", "vs/css!vs/editor/standalone/browser/quickInput/standaloneQuickInput"], "vs/platform/accessibility/browser/accessibilityService": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/window", "vs/base/common/event", "vs/base/common/lifecycle", "vs/platform/accessibility/common/accessibility", "vs/platform/configuration/common/configuration", "vs/platform/contextkey/common/contextkey", "vs/platform/layout/browser/layoutService"], "vs/platform/actions/common/menuService": ["require", "exports", "vs/base/common/async", "vs/base/common/event", "vs/base/common/lifecycle", "vs/platform/actions/common/actions", "vs/platform/commands/common/commands", "vs/platform/contextkey/common/contextkey", "vs/base/common/actions", "vs/platform/storage/common/storage", "vs/base/common/arrays", "vs/nls", "vs/platform/keybinding/common/keybinding"], "vs/platform/clipboard/browser/clipboardService": ["require", "exports", "vs/base/browser/browser", "vs/base/browser/dom", "vs/base/browser/window", "vs/base/common/async", "vs/base/common/event", "vs/base/common/hash", "vs/base/common/lifecycle", "vs/base/common/uri", "vs/platform/layout/browser/layoutService", "vs/platform/log/common/log"], "vs/platform/contextkey/browser/contextKeyService": ["require", "exports", "vs/base/common/event", "vs/base/common/iterator", "vs/base/common/lifecycle", "vs/base/common/objects", "vs/base/common/ternarySearchTree", "vs/base/common/uri", "vs/nls", "vs/platform/commands/common/commands", "vs/platform/configuration/common/configuration", "vs/platform/contextkey/common/contextkey"], "vs/platform/instantiation/common/descriptors": ["require", "exports"], "vs/platform/instantiation/common/instantiationService": ["require", "exports", "vs/base/common/async", "vs/base/common/errors", "vs/base/common/lifecycle", "vs/platform/instantiation/common/descriptors", "vs/platform/instantiation/common/graph", "vs/platform/instantiation/common/instantiation", "vs/platform/instantiation/common/serviceCollection", "vs/base/common/linkedList"], "vs/platform/markers/common/markerService": ["require", "exports", "vs/base/common/arrays", "vs/base/common/event", "vs/base/common/iterator", "vs/base/common/map", "vs/base/common/network", "vs/base/common/uri", "vs/platform/markers/common/markers"], "vs/platform/configuration/common/configurations": ["require", "exports", "vs/base/common/lifecycle", "vs/platform/configuration/common/configurationModels", "vs/platform/configuration/common/configurationRegistry", "vs/platform/registry/common/platform"], "vs/platform/log/common/logService": ["require", "exports", "vs/base/common/lifecycle", "vs/platform/log/common/log"], "vs/editor/common/services/treeSitterParserService": ["require", "exports", "vs/platform/instantiation/common/instantiation"], "vs/editor/standalone/browser/standaloneTreeSitterService": ["require", "exports"], "vs/editor/standalone/browser/standaloneLayoutService": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/window", "vs/base/common/arrays", "vs/base/common/event", "vs/editor/browser/services/codeEditorService", "vs/platform/instantiation/common/extensions", "vs/platform/layout/browser/layoutService"], "vs/platform/undoRedo/common/undoRedoService": ["require", "exports", "vs/base/common/errors", "vs/base/common/lifecycle", "vs/base/common/network", "vs/base/common/severity", "vs/nls", "vs/platform/dialogs/common/dialogs", "vs/platform/instantiation/common/extensions", "vs/platform/notification/common/notification", "vs/platform/undoRedo/common/undoRedo"], "vs/editor/common/services/semanticTokensStylingService": ["require", "exports", "vs/base/common/lifecycle", "vs/editor/common/languages/language", "vs/platform/theme/common/themeService", "vs/platform/log/common/log", "vs/editor/common/services/semanticTokensProviderStyling", "vs/editor/common/services/semanticTokensStyling", "vs/platform/instantiation/common/extensions"], "vs/editor/common/services/languageFeaturesService": ["require", "exports", "vs/editor/common/languageFeatureRegistry", "vs/editor/common/services/languageFeatures", "vs/platform/instantiation/common/extensions"], "vs/editor/browser/services/hoverService/hoverService": ["require", "exports", "vs/platform/instantiation/common/extensions", "vs/platform/theme/common/themeService", "vs/platform/theme/common/colorRegistry", "vs/platform/hover/browser/hover", "vs/platform/contextview/browser/contextView", "vs/platform/instantiation/common/instantiation", "vs/editor/browser/services/hoverService/hoverWidget", "vs/base/common/lifecycle", "vs/base/browser/dom", "vs/platform/keybinding/common/keybinding", "vs/base/browser/keyboardEvent", "vs/platform/accessibility/common/accessibility", "vs/platform/layout/browser/layoutService", "vs/base/browser/window", "vs/platform/contextview/browser/contextViewService", "vs/editor/browser/services/hoverService/updatableHoverWidget", "vs/base/common/async"], "vs/editor/browser/widget/multiDiffEditor/multiDiffEditorWidgetImpl": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/scrollbar/scrollableElement", "vs/base/common/arrays", "vs/base/common/arraysFind", "vs/base/common/errors", "vs/base/common/lifecycle", "vs/base/common/observable", "vs/base/common/observableInternal/base", "vs/base/common/scrollable", "vs/editor/browser/widget/diffEditor/utils", "vs/editor/common/core/offsetRange", "vs/editor/common/core/selection", "vs/editor/common/editorContext<PERSON>eys", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation", "vs/platform/instantiation/common/serviceCollection", "vs/editor/browser/widget/multiDiffEditor/diffEditorItemTemplate", "vs/editor/browser/widget/multiDiffEditor/objectPool", "vs/nls", "vs/css!vs/editor/browser/widget/multiDiffEditor/style"], "vs/editor/browser/widget/multiDiffEditor/diffEditorItemTemplate": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/button/button", "vs/base/common/codicons", "vs/base/common/lifecycle", "vs/base/common/observable", "vs/base/common/observableInternal/base", "vs/editor/browser/observableCodeEditor", "vs/editor/browser/widget/diffEditor/diffEditorWidget", "vs/platform/actions/browser/menuEntryActionViewItem", "vs/platform/actions/browser/toolbar", "vs/platform/actions/common/actions", "vs/platform/instantiation/common/instantiation", "vs/editor/browser/widget/multiDiffEditor/utils", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/serviceCollection"], "vs/editor/browser/widget/multiDiffEditor/colors": ["require", "exports", "vs/nls", "vs/platform/theme/common/colorRegistry"], "vs/editor/standalone/common/monarch/monarchCommon": ["require", "exports"], "vs/base/browser/iframe": ["require", "exports"], "vs/base/common/errorMessage": ["require", "exports", "vs/base/common/arrays", "vs/base/common/types", "vs/nls"], "vs/base/common/tfIdf": ["require", "exports"], "vs/platform/quickinput/browser/pickerQuickAccess": ["require", "exports", "vs/base/common/async", "vs/base/common/cancellation", "vs/base/common/lifecycle", "vs/base/common/types"], "vs/base/browser/ui/list/listPaging": ["require", "exports", "vs/base/common/arrays", "vs/base/common/cancellation", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/browser/ui/list/listWidget", "vs/css!vs/base/browser/ui/list/list"], "vs/base/browser/ui/table/tableWidget": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/hover/hoverDelegate2", "vs/base/browser/ui/hover/hoverDelegateFactory", "vs/base/browser/ui/list/listWidget", "vs/base/browser/ui/splitview/splitview", "vs/base/common/event", "vs/base/common/lifecycle", "vs/css!vs/base/browser/ui/table/table"], "vs/base/browser/ui/tree/abstractTree": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/event", "vs/base/browser/keyboardEvent", "vs/base/browser/ui/actionbar/actionbar", "vs/base/browser/ui/findinput/findInput", "vs/base/browser/ui/inputbox/inputBox", "vs/base/browser/ui/list/listView", "vs/base/browser/ui/list/listWidget", "vs/base/browser/ui/toggle/toggle", "vs/base/browser/ui/tree/indexTreeModel", "vs/base/browser/ui/tree/tree", "vs/base/common/actions", "vs/base/common/arrays", "vs/base/common/async", "vs/base/common/codicons", "vs/base/common/themables", "vs/base/common/map", "vs/base/common/event", "vs/base/common/filters", "vs/base/common/lifecycle", "vs/base/common/numbers", "vs/base/common/types", "vs/nls", "vs/base/browser/ui/hover/hoverDelegateFactory", "vs/base/common/observable", "vs/base/browser/ui/aria/aria", "vs/css!vs/base/browser/ui/tree/media/tree"], "vs/base/browser/ui/tree/asyncDataTree": ["require", "exports", "vs/base/browser/ui/list/listView", "vs/base/browser/ui/tree/abstractTree", "vs/base/browser/ui/tree/indexTreeModel", "vs/base/browser/ui/tree/objectTree", "vs/base/browser/ui/tree/tree", "vs/base/common/async", "vs/base/common/codicons", "vs/base/common/themables", "vs/base/common/errors", "vs/base/common/event", "vs/base/common/iterator", "vs/base/common/lifecycle", "vs/base/common/types"], "vs/base/browser/ui/tree/dataTree": ["require", "exports", "vs/base/browser/ui/tree/abstractTree", "vs/base/browser/ui/tree/objectTreeModel"], "vs/base/browser/ui/tree/objectTree": ["require", "exports", "vs/base/browser/ui/tree/abstractTree", "vs/base/browser/ui/tree/compressedObjectTreeModel", "vs/base/browser/ui/tree/objectTreeModel", "vs/base/common/decorators", "vs/base/common/iterator"], "vs/base/browser/ui/splitview/splitview": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/event", "vs/base/browser/ui/sash/sash", "vs/base/browser/ui/scrollbar/scrollableElement", "vs/base/common/arrays", "vs/base/common/color", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/numbers", "vs/base/common/scrollable", "vs/base/common/types", "vs/css!vs/base/browser/ui/splitview/splitview"], "vs/editor/contrib/gotoSymbol/browser/peek/referencesTree": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/countBadge/countBadge", "vs/base/browser/ui/highlightedlabel/highlightedLabel", "vs/base/browser/ui/iconLabel/iconLabel", "vs/base/common/filters", "vs/base/common/lifecycle", "vs/base/common/resources", "vs/editor/common/services/resolverService", "vs/nls", "vs/platform/instantiation/common/instantiation", "vs/platform/keybinding/common/keybinding", "vs/platform/label/common/label", "vs/platform/theme/browser/defaultStyles", "vs/editor/contrib/gotoSymbol/browser/referencesModel"], "vs/css!vs/editor/contrib/gotoSymbol/browser/peek/referencesWidget": [], "vs/base/common/marshalling": ["require", "exports", "vs/base/common/buffer", "vs/base/common/uri"], "vs/editor/common/diff/defaultLinesDiffComputer/utils": ["require", "exports"], "vs/editor/common/core/textEdit": ["require", "exports", "vs/base/common/assert", "vs/base/common/errors", "vs/editor/common/core/position", "vs/editor/common/core/positionToOffset", "vs/editor/common/core/range", "vs/editor/common/core/textLength"], "vs/editor/browser/viewParts/lines/viewLine": ["require", "exports", "vs/base/browser/browser", "vs/base/browser/fastDomNode", "vs/base/common/platform", "vs/editor/browser/viewParts/lines/rangeUtil", "vs/editor/browser/view/renderingContext", "vs/editor/common/viewLayout/lineDecorations", "vs/editor/common/viewLayout/viewLineRenderer", "vs/platform/theme/common/theme", "vs/editor/common/config/editorOptions"], "vs/editor/browser/controller/mouseHandler": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/mouseEvent", "vs/base/common/lifecycle", "vs/base/common/platform", "vs/editor/browser/controller/mouseTarget", "vs/editor/browser/editorDom", "vs/editor/common/config/editorZoom", "vs/editor/common/core/position", "vs/editor/common/core/selection", "vs/editor/common/viewEventHandler", "vs/base/browser/ui/scrollbar/scrollableElement"], "vs/base/browser/ui/mouseCursor/mouseCursor": ["require", "exports", "vs/css!vs/base/browser/ui/mouseCursor/mouseCursor"], "vs/base/common/ime": ["require", "exports", "vs/base/common/event"], "vs/css!vs/editor/browser/controller/textAreaHandler": [], "vs/editor/browser/view/viewLayer": ["require", "exports", "vs/base/browser/fastDomNode", "vs/base/browser/trustedTypes", "vs/base/common/errors", "vs/editor/common/core/stringBuilder"], "vs/css!vs/editor/browser/viewParts/blockDecorations/blockDecorations": [], "vs/editor/browser/view/dynamicViewOverlay": ["require", "exports", "vs/editor/common/viewEventHandler"], "vs/css!vs/editor/browser/viewParts/decorations/decorations": [], "vs/css!vs/editor/browser/viewParts/currentLineHighlight/currentLineHighlight": [], "vs/css!vs/editor/browser/viewParts/glyphMargin/glyphMargin": [], "vs/css!vs/editor/browser/viewParts/lineNumbers/lineNumbers": [], "vs/editor/common/textModelGuides": ["require", "exports"], "vs/css!vs/editor/browser/viewParts/indentGuides/indentGuides": [], "vs/css!vs/editor/browser/viewParts/margin/margin": [], "vs/css!vs/editor/browser/viewParts/linesDecorations/linesDecorations": [], "vs/editor/browser/viewParts/lines/domReadingContext": ["require", "exports"], "vs/css!vs/editor/browser/viewParts/lines/viewLines": [], "vs/css!vs/editor/browser/viewParts/marginDecorations/marginDecorations": [], "vs/base/browser/globalPointerMoveMonitor": ["require", "exports", "vs/base/browser/dom", "vs/base/common/lifecycle"], "vs/editor/common/core/rgba": ["require", "exports"], "vs/editor/browser/viewParts/minimap/minimapCharRendererFactory": ["require", "exports", "vs/editor/browser/viewParts/minimap/minimapChar<PERSON><PERSON>er", "vs/editor/browser/viewParts/minimap/minimapCharSheet", "vs/editor/browser/viewParts/minimap/minimapPreBaked", "vs/base/common/uint"], "vs/base/browser/fonts": ["require", "exports", "vs/base/common/platform"], "vs/css!vs/editor/browser/viewParts/minimap/minimap": [], "vs/css!vs/editor/browser/viewParts/overlayWidgets/overlayWidgets": [], "vs/editor/common/viewModel/overviewZoneManager": ["require", "exports"], "vs/css!vs/editor/browser/viewParts/scrollDecoration/scrollDecoration": [], "vs/css!vs/editor/browser/viewParts/rulers/rulers": [], "vs/css!vs/editor/browser/viewParts/selections/selections": [], "vs/editor/browser/viewParts/viewCursors/viewCursor": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/fastDomNode", "vs/base/common/strings", "vs/editor/browser/config/domFontInfo", "vs/editor/common/config/editorOptions", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/base/browser/ui/mouseCursor/mouseCursor"], "vs/css!vs/editor/browser/viewParts/viewCursors/viewCursors": [], "vs/css!vs/editor/browser/viewParts/whitespace/whitespace": [], "vs/editor/common/editorTheme": ["require", "exports"], "vs/base/common/buffer": ["require", "exports", "vs/base/common/lazy"], "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/bracketPairsTree": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle", "vs/editor/common/textModelBracketPairs", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/beforeEditPositionMapper", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/brackets", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/length", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/parser", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/smallImmutableSet", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/tokenizer", "vs/base/common/arrays", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/combineTextEditInfos"], "vs/editor/common/core/textChange": ["require", "exports", "vs/base/common/buffer", "vs/editor/common/core/stringBuilder"], "vs/editor/common/model/textModelPart": ["require", "exports", "vs/base/common/lifecycle"], "vs/editor/common/model/pieceTreeTextBuffer/pieceTreeBase": ["require", "exports", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/model", "vs/editor/common/model/pieceTreeTextBuffer/rbTreeBase", "vs/editor/common/model/textModelSearch"], "vs/editor/common/model/textModelTokens": ["require", "exports", "vs/base/common/async", "vs/base/common/errors", "vs/base/common/platform", "vs/base/common/stopwatch", "vs/editor/common/core/eolCounter", "vs/editor/common/core/lineRange", "vs/editor/common/core/offsetRange", "vs/editor/common/languages/nullTokenize", "vs/editor/common/model/fixedArray", "vs/editor/common/tokens/contiguousMultilineTokensBuilder", "vs/editor/common/tokens/lineTokens"], "vs/editor/common/model/treeSitterTokens": ["require", "exports", "vs/editor/common/languages", "vs/editor/common/tokens/lineTokens", "vs/editor/common/model/tokens"], "vs/editor/common/tokens/contiguousMultilineTokensBuilder": ["require", "exports", "vs/editor/common/tokens/contiguousMultilineTokens"], "vs/editor/common/tokens/contiguousTokensStore": ["require", "exports", "vs/base/common/arrays", "vs/editor/common/core/position", "vs/editor/common/tokens/contiguousTokensEditing", "vs/editor/common/tokens/lineTokens", "vs/editor/common/encodedTokenAttributes"], "vs/editor/common/tokens/sparseTokensStore": ["require", "exports", "vs/base/common/arrays", "vs/editor/common/tokens/lineTokens"], "vs/editor/common/cursor/cursorCollection": ["require", "exports", "vs/base/common/arrays", "vs/base/common/arraysFind", "vs/editor/common/cursor<PERSON><PERSON>mon", "vs/editor/common/cursor/oneCursor", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/core/selection"], "vs/editor/common/cursor/cursorContext": ["require", "exports"], "vs/base/common/scrollable": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle"], "vs/editor/common/viewLayout/linesLayout": ["require", "exports", "vs/base/common/strings"], "vs/editor/common/viewModel/modelLineProjection": ["require", "exports", "vs/editor/common/tokens/lineTokens", "vs/editor/common/core/position", "vs/editor/common/textModelEvents", "vs/editor/common/viewModel"], "vs/editor/browser/widget/diffEditor/components/accessibleDiffViewer": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/trustedTypes", "vs/base/browser/ui/actionbar/actionbar", "vs/base/browser/ui/scrollbar/scrollableElement", "vs/base/common/actions", "vs/base/common/arrays", "vs/base/common/codicons", "vs/base/common/lifecycle", "vs/base/common/observable", "vs/base/common/themables", "vs/editor/browser/config/domFontInfo", "vs/editor/browser/widget/diffEditor/utils", "vs/editor/common/config/editorOptions", "vs/editor/common/core/lineRange", "vs/editor/common/core/offsetRange", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/diff/rangeMapping", "vs/editor/common/languages/language", "vs/editor/common/tokens/lineTokens", "vs/editor/common/viewLayout/viewLineRenderer", "vs/editor/common/viewModel", "vs/nls", "vs/platform/accessibilitySignal/browser/accessibilitySignalService", "vs/platform/instantiation/common/instantiation", "vs/platform/theme/common/iconRegistry", "vs/css!vs/editor/browser/widget/diffEditor/components/accessibleDiffViewer"], "vs/editor/browser/widget/diffEditor/components/diffEditorDecorations": ["require", "exports", "vs/base/common/lifecycle", "vs/base/common/observable", "vs/editor/browser/widget/diffEditor/components/diffEditorViewZones/diffEditorViewZones", "vs/editor/browser/widget/diffEditor/features/movedBlocksLinesFeature", "vs/editor/browser/widget/diffEditor/registrations.contribution", "vs/editor/browser/widget/diffEditor/utils"], "vs/editor/browser/widget/diffEditor/components/diffEditorSash": ["require", "exports", "vs/base/browser/ui/sash/sash", "vs/base/common/lifecycle", "vs/base/common/observable", "vs/base/common/observableInternal/derived"], "vs/editor/browser/widget/diffEditor/components/diffEditorViewZones/diffEditorViewZones": ["require", "exports", "vs/base/browser/dom", "vs/base/common/arrays", "vs/base/common/async", "vs/base/common/codicons", "vs/base/common/lifecycle", "vs/base/common/observable", "vs/base/common/themables", "vs/base/common/types", "vs/editor/browser/config/domFontInfo", "vs/editor/browser/widget/diffEditor/registrations.contribution", "vs/editor/browser/widget/diffEditor/diffEditorViewModel", "vs/editor/browser/widget/diffEditor/components/diffEditorViewZones/inlineDiffDeletedCodeMargin", "vs/editor/browser/widget/diffEditor/components/diffEditorViewZones/renderLines", "vs/editor/browser/widget/diffEditor/utils", "vs/editor/common/core/lineRange", "vs/editor/common/core/position", "vs/editor/common/viewModel", "vs/platform/clipboard/common/clipboardService", "vs/platform/contextview/browser/contextView", "vs/editor/common/core/range"], "vs/editor/browser/widget/diffEditor/features/gutterFeature": ["require", "exports", "vs/base/browser/dom", "vs/base/common/lifecycle", "vs/base/common/observable", "vs/base/common/observableInternal/derived", "vs/editor/browser/widget/diffEditor/components/diffEditorSash", "vs/editor/browser/widget/diffEditor/utils", "vs/editor/browser/widget/diffEditor/utils/editorGutter", "vs/editor/browser/widget/multiDiffEditor/utils", "vs/editor/common/core/lineRange", "vs/editor/common/core/offsetRange", "vs/editor/common/core/range", "vs/editor/common/core/textEdit", "vs/editor/common/diff/rangeMapping", "vs/editor/common/model/textModelText", "vs/platform/actions/browser/toolbar", "vs/platform/actions/common/actions", "vs/platform/contextkey/common/contextkey", "vs/platform/hover/browser/hover", "vs/platform/instantiation/common/instantiation"], "vs/editor/browser/widget/diffEditor/features/movedBlocksLinesFeature": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/actionbar/actionbar", "vs/base/common/actions", "vs/base/common/arrays", "vs/base/common/arraysFind", "vs/base/common/codicons", "vs/base/common/lifecycle", "vs/base/common/observable", "vs/base/common/themables", "vs/editor/browser/widget/diffEditor/utils", "vs/editor/common/core/offsetRange", "vs/nls"], "vs/editor/browser/widget/diffEditor/features/overviewRulerFeature": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/fastDomNode", "vs/base/browser/ui/scrollbar/scrollbarState", "vs/base/common/lifecycle", "vs/base/common/observable", "vs/editor/browser/widget/diffEditor/utils", "vs/editor/common/core/position", "vs/editor/common/viewModel/overviewZoneManager", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/themeService"], "vs/editor/browser/widget/diffEditor/features/revertButtonsFeature": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/iconLabel/iconLabels", "vs/base/common/codicons", "vs/base/common/lifecycle", "vs/base/common/observable", "vs/editor/common/core/lineRange", "vs/editor/common/core/range", "vs/editor/common/diff/rangeMapping", "vs/editor/common/model", "vs/nls"], "vs/editor/browser/widget/diffEditor/components/diffEditorEditors": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/observable", "vs/editor/browser/observableCodeEditor", "vs/editor/browser/widget/diffEditor/features/overviewRulerFeature", "vs/editor/common/config/editorOptions", "vs/editor/common/core/position", "vs/nls", "vs/platform/instantiation/common/instantiation", "vs/platform/keybinding/common/keybinding"], "vs/editor/browser/widget/diffEditor/delegatingEditorImpl": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle"], "vs/editor/browser/widget/diffEditor/diffEditorOptions": ["require", "exports", "vs/base/common/observable", "vs/base/common/observableInternal/utils", "vs/editor/browser/widget/diffEditor/components/diffEditorViewZones/diffEditorViewZones", "vs/editor/common/config/diffEditor", "vs/editor/common/config/editorOptions", "vs/platform/accessibility/common/accessibility"], "vs/editor/browser/widget/diffEditor/diffEditorViewModel": ["require", "exports", "vs/base/common/async", "vs/base/common/cancellation", "vs/base/common/lifecycle", "vs/base/common/observable", "vs/editor/browser/widget/diffEditor/diffProviderFactoryService", "vs/editor/browser/widget/diffEditor/utils", "vs/base/common/hotReloadHelpers", "vs/editor/common/core/lineRange", "vs/editor/common/diff/defaultLinesDiffComputer/defaultLinesDiffComputer", "vs/editor/common/diff/rangeMapping", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/beforeEditPositionMapper", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/combineTextEditInfos", "vs/editor/common/diff/defaultLinesDiffComputer/heuristicSequenceOptimizations", "vs/base/common/types", "vs/base/common/arrays", "vs/base/common/assert"], "vs/css!vs/editor/browser/widget/diffEditor/style": [], "vs/base/browser/ui/button/button": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/dompurify/dompurify", "vs/base/browser/keyboardEvent", "vs/base/browser/markdownRenderer", "vs/base/browser/touch", "vs/base/browser/ui/hover/hoverDelegateFactory", "vs/base/browser/ui/iconLabel/iconLabels", "vs/base/common/color", "vs/base/common/event", "vs/base/common/htmlContent", "vs/base/common/lifecycle", "vs/base/common/themables", "vs/base/browser/ui/hover/hoverDelegate2", "vs/css!vs/base/browser/ui/button/button"], "vs/css!vs/editor/contrib/dropOrPasteInto/browser/postEditWidget": [], "vs/platform/actionWidget/browser/actionList": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/keybindingLabel/keybindingLabel", "vs/base/browser/ui/list/listWidget", "vs/base/common/cancellation", "vs/base/common/codicons", "vs/base/common/lifecycle", "vs/base/common/platform", "vs/base/common/themables", "vs/nls", "vs/platform/contextview/browser/contextView", "vs/platform/keybinding/common/keybinding", "vs/platform/theme/browser/defaultStyles", "vs/platform/theme/common/colorRegistry", "vs/css!vs/platform/actionWidget/browser/actionWidget"], "vs/css!vs/platform/actionWidget/browser/actionWidget": [], "vs/base/common/decorators": ["require", "exports"], "vs/base/browser/ui/keybindingLabel/keybindingLabel": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/hover/hoverDelegate2", "vs/base/browser/ui/hover/hoverDelegateFactory", "vs/base/common/keybindingLabels", "vs/base/common/lifecycle", "vs/base/common/objects", "vs/nls", "vs/css!vs/base/browser/ui/keybindingLabel/keybindingLabel"], "vs/platform/actions/browser/toolbar": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/mouseEvent", "vs/base/browser/ui/toolbar/toolbar", "vs/base/common/actions", "vs/base/common/arrays", "vs/base/common/collections", "vs/base/common/errors", "vs/base/common/event", "vs/base/common/iterator", "vs/base/common/lifecycle", "vs/nls", "vs/platform/actions/browser/menuEntryActionViewItem", "vs/platform/actions/common/actions", "vs/platform/actions/common/menuService", "vs/platform/commands/common/commands", "vs/platform/contextkey/common/contextkey", "vs/platform/contextview/browser/contextView", "vs/platform/keybinding/common/keybinding", "vs/platform/telemetry/common/telemetry"], "vs/css!vs/editor/contrib/inlineCompletions/browser/hintsWidget/inlineCompletionsHintsWidget": [], "vs/editor/contrib/hover/browser/hoverOperation": ["require", "exports", "vs/base/common/async", "vs/base/common/errors", "vs/base/common/event", "vs/base/common/lifecycle"], "vs/editor/contrib/hover/browser/contentHoverWidget": ["require", "exports", "vs/base/browser/dom", "vs/platform/keybinding/common/keybinding", "vs/editor/contrib/hover/browser/resizableContentWidget", "vs/platform/contextkey/common/contextkey", "vs/platform/configuration/common/configuration", "vs/platform/accessibility/common/accessibility", "vs/editor/common/editorContext<PERSON>eys", "vs/base/browser/ui/hover/hoverWidget", "vs/base/common/event"], "vs/editor/contrib/hover/browser/contentHoverComputer": ["require", "exports", "vs/base/common/arrays", "vs/base/common/async"], "vs/editor/contrib/hover/browser/contentHoverTypes": ["require", "exports"], "vs/editor/contrib/hover/browser/contentHoverRendered": ["require", "exports", "vs/editor/contrib/hover/browser/hoverTypes", "vs/base/common/lifecycle", "vs/editor/contrib/hover/browser/contentHoverStatusBar", "vs/editor/common/model/textModel", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/base/browser/dom", "vs/editor/contrib/hover/browser/markdownHoverParticipant", "vs/editor/contrib/colorPicker/browser/colorHoverParticipant", "vs/editor/contrib/inlayHints/browser/inlayHintsHover", "vs/base/common/errors"], "vs/base/browser/ui/selectBox/selectBoxCustom": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/event", "vs/base/browser/keyboardEvent", "vs/base/browser/markdownRenderer", "vs/base/browser/ui/hover/hoverDelegate2", "vs/base/browser/ui/hover/hoverDelegateFactory", "vs/base/browser/ui/list/listWidget", "vs/base/common/arrays", "vs/base/common/event", "vs/base/common/keyCodes", "vs/base/common/lifecycle", "vs/base/common/platform", "vs/nls", "vs/css!vs/base/browser/ui/selectBox/selectBoxCustom"], "vs/base/browser/ui/selectBox/selectBoxNative": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/touch", "vs/base/common/arrays", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/platform"], "vs/css!vs/base/browser/ui/selectBox/selectBox": [], "vs/base/common/search": ["require", "exports", "vs/base/common/strings"], "vs/css!vs/base/browser/ui/toggle/toggle": [], "vs/css!vs/base/browser/ui/sash/sash": [], "vs/base/browser/ui/findinput/findInput": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/findinput/findInputToggles", "vs/base/browser/ui/inputbox/inputBox", "vs/base/browser/ui/widget", "vs/base/common/event", "vs/nls", "vs/base/common/lifecycle", "vs/base/browser/ui/hover/hoverDelegateFactory", "vs/css!vs/base/browser/ui/findinput/findInput"], "vs/base/browser/ui/findinput/replaceInput": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/toggle/toggle", "vs/base/browser/ui/inputbox/inputBox", "vs/base/browser/ui/widget", "vs/base/common/codicons", "vs/base/common/event", "vs/nls", "vs/base/browser/ui/hover/hoverDelegateFactory", "vs/css!vs/base/browser/ui/findinput/findInput"], "vs/base/common/observableInternal/debugName": ["require", "exports"], "vs/css!vs/editor/browser/widget/markdownRenderer/browser/renderedMarkdown": [], "vs/editor/contrib/inlineCompletions/browser/model/singleTextEdit": ["require", "exports", "vs/base/common/diff/diff", "vs/base/common/strings", "vs/editor/common/core/range", "vs/editor/common/core/textLength", "vs/editor/common/core/textEdit", "vs/editor/contrib/inlineCompletions/browser/model/ghostText"], "vs/editor/contrib/inlineCompletions/browser/model/inlineCompletionsSource": ["require", "exports", "vs/base/common/cancellation", "vs/base/common/equals", "vs/base/common/filters", "vs/base/common/lifecycle", "vs/base/common/observable", "vs/editor/common/core/range", "vs/editor/common/core/textEdit", "vs/editor/common/core/textLength", "vs/editor/common/languages", "vs/editor/common/languages/languageConfigurationRegistry", "vs/editor/common/services/languageFeatures", "vs/editor/contrib/inlineCompletions/browser/model/provideInlineCompletions", "vs/editor/contrib/inlineCompletions/browser/model/singleTextEdit"], "vs/editor/contrib/inlineCompletions/browser/utils": ["require", "exports", "vs/base/common/errors", "vs/base/common/lifecycle", "vs/base/common/observable", "vs/editor/common/core/position", "vs/editor/common/core/range"], "vs/editor/common/viewLayout/lineDecorations": ["require", "exports", "vs/base/common/strings"], "vs/css!vs/editor/contrib/inlineCompletions/browser/view/ghostTextView": [], "vs/base/browser/formattedTextRenderer": ["require", "exports", "vs/base/browser/dom"], "vs/base/common/marked/marked": ["exports"], "vs/css!vs/editor/contrib/zoneWidget/browser/zoneWidget": [], "vs/base/browser/ui/dropdown/dropdownActionViewItem": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/actionbar/actionViewItems", "vs/base/browser/ui/dropdown/dropdown", "vs/base/common/event", "vs/base/browser/ui/hover/hoverDelegateFactory", "vs/base/browser/ui/hover/hoverDelegate2", "vs/css!vs/base/browser/ui/dropdown/dropdown"], "vs/base/common/keybindingLabels": ["require", "exports", "vs/nls"], "vs/css!vs/platform/actions/browser/menuEntryActionViewItem": [], "vs/base/browser/ui/scrollbar/horizontalScrollbar": ["require", "exports", "vs/base/browser/mouseEvent", "vs/base/browser/ui/scrollbar/abstractScrollbar", "vs/base/browser/ui/scrollbar/scrollbarArrow", "vs/base/browser/ui/scrollbar/scrollbarState", "vs/base/common/codicons"], "vs/base/browser/ui/scrollbar/verticalScrollbar": ["require", "exports", "vs/base/browser/mouseEvent", "vs/base/browser/ui/scrollbar/abstractScrollbar", "vs/base/browser/ui/scrollbar/scrollbarArrow", "vs/base/browser/ui/scrollbar/scrollbarState", "vs/base/common/codicons"], "vs/css!vs/base/browser/ui/scrollbar/media/scrollbars": [], "vs/css!vs/platform/severityIcon/browser/media/severityIcon": [], "vs/css!vs/base/browser/ui/hover/hoverWidget": [], "vs/editor/contrib/hover/browser/marginHoverComputer": ["require", "exports", "vs/base/common/arrays", "vs/base/common/htmlContent", "vs/editor/common/model"], "vs/css!vs/editor/contrib/inlineEdit/browser/inlineEdit": [], "vs/css!vs/editor/contrib/inlineEdit/browser/inlineEditHintsWidget": [], "vs/css!vs/editor/contrib/inlineEdit/browser/inlineEditSideBySideWidget": [], "vs/editor/contrib/inlineCompletions/browser/model/provideInlineCompletions": ["require", "exports", "vs/base/common/assert", "vs/base/common/async", "vs/base/common/cancellation", "vs/base/common/map", "vs/base/common/errors", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/model/bracketPairsTextModelPart/fixBrackets", "vs/editor/common/core/textEdit", "vs/editor/contrib/inlineCompletions/browser/utils", "vs/editor/contrib/snippet/browser/snippetParser"], "vs/css!vs/editor/contrib/inlineEdits/browser/inlineEditsWidget": [], "vs/base/browser/ui/list/splice": ["require", "exports"], "vs/base/browser/ui/list/list": ["require", "exports"], "vs/base/browser/ui/list/listView": ["require", "exports", "vs/base/browser/dnd", "vs/base/browser/dom", "vs/base/browser/event", "vs/base/browser/touch", "vs/base/browser/ui/scrollbar/scrollableElement", "vs/base/common/arrays", "vs/base/common/async", "vs/base/common/decorators", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/range", "vs/base/common/scrollable", "vs/base/browser/ui/list/rangeMap", "vs/base/browser/ui/list/rowCache", "vs/base/common/errors", "vs/base/common/numbers"], "vs/css!vs/base/browser/ui/list/list": [], "vs/base/common/labels": ["require", "exports", "vs/base/common/extpath", "vs/base/common/platform"], "vs/css!vs/editor/contrib/stickyScroll/browser/stickyScroll": [], "vs/editor/contrib/stickyScroll/browser/stickyScrollModelProvider": ["require", "exports", "vs/base/common/lifecycle", "vs/editor/common/services/languageFeatures", "vs/editor/contrib/documentSymbols/browser/outlineModel", "vs/base/common/async", "vs/editor/contrib/folding/browser/folding", "vs/editor/contrib/folding/browser/syntaxRangeProvider", "vs/editor/contrib/folding/browser/indentRangeProvider", "vs/editor/common/languages/languageConfigurationRegistry", "vs/base/common/errors", "vs/editor/contrib/stickyScroll/browser/stickyScrollElement", "vs/base/common/iterator", "vs/platform/instantiation/common/instantiation"], "vs/base/browser/ui/iconLabel/iconLabel": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/highlightedlabel/highlightedLabel", "vs/base/common/lifecycle", "vs/base/common/objects", "vs/base/common/range", "vs/base/browser/ui/hover/hoverDelegateFactory", "vs/base/browser/ui/hover/hoverDelegate2", "vs/base/common/types", "vs/base/common/iconLabels", "vs/css!vs/base/browser/ui/iconLabel/iconlabel"], "vs/editor/common/services/getIconClasses": ["require", "exports", "vs/base/common/network", "vs/base/common/resources", "vs/base/common/uri", "vs/editor/common/languages/modesRegistry", "vs/platform/files/common/files", "vs/base/common/themables"], "vs/platform/files/common/files": ["require", "exports"], "vs/css!vs/platform/opener/browser/link": [], "vs/base/common/observableInternal/lazyObservableValue": ["require", "exports", "vs/base/common/observableInternal/base"], "vs/base/browser/defaultWorkerFactory": ["require", "exports", "vs/base/browser/trustedTypes", "vs/base/common/errors", "vs/base/common/network", "vs/base/common/worker/simpleWorker", "vs/base/common/lifecycle", "vs/base/common/arrays", "vs/nls"], "vs/editor/common/viewLayout/linePart": ["require", "exports"], "vs/editor/browser/services/abstractCodeEditorService": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/linkedList", "vs/platform/theme/common/themeService"], "vs/platform/keybinding/common/baseResolvedKeybinding": ["require", "exports", "vs/base/common/errors", "vs/base/common/keybindingLabels", "vs/base/common/keybindings"], "vs/base/browser/ui/contextview/contextview": ["require", "exports", "vs/base/browser/canIUse", "vs/base/browser/dom", "vs/base/common/lifecycle", "vs/base/common/platform", "vs/base/common/range", "vs/css!vs/base/browser/ui/contextview/contextview"], "vs/editor/common/services/languagesRegistry": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/strings", "vs/editor/common/services/languagesAssociations", "vs/editor/common/languages/modesRegistry", "vs/platform/configuration/common/configurationRegistry", "vs/platform/registry/common/platform"], "vs/platform/editor/common/editor": ["require", "exports"], "vs/base/common/collections": ["require", "exports"], "vs/platform/contextview/browser/contextMenuHandler": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/mouseEvent", "vs/base/browser/ui/menu/menu", "vs/base/common/actions", "vs/base/common/errors", "vs/base/common/lifecycle", "vs/platform/theme/browser/defaultStyles"], "vs/platform/quickinput/browser/quickInputService": ["require", "exports", "vs/base/common/cancellation", "vs/base/common/event", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation", "vs/platform/layout/browser/layoutService", "vs/platform/opener/common/opener", "vs/platform/quickinput/browser/quickAccess", "vs/platform/theme/browser/defaultStyles", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/themeService", "vs/platform/quickinput/browser/quickInput", "vs/platform/quickinput/browser/quickInputController", "vs/platform/configuration/common/configuration", "vs/base/browser/dom"], "vs/css!vs/editor/standalone/browser/quickInput/standaloneQuickInput": [], "vs/platform/instantiation/common/graph": ["require", "exports"], "vs/editor/common/languageFeatureRegistry": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle", "vs/editor/common/model", "vs/editor/common/languageSelector"], "vs/editor/browser/services/hoverService/hoverWidget": ["require", "exports", "vs/base/common/lifecycle", "vs/base/common/event", "vs/base/browser/dom", "vs/platform/keybinding/common/keybinding", "vs/platform/configuration/common/configuration", "vs/editor/common/config/editorOptions", "vs/base/browser/ui/hover/hoverWidget", "vs/base/browser/ui/widget", "vs/platform/opener/common/opener", "vs/platform/instantiation/common/instantiation", "vs/editor/browser/widget/markdownRenderer/browser/markdownRenderer", "vs/base/common/htmlContent", "vs/nls", "vs/base/common/platform", "vs/platform/accessibility/common/accessibility", "vs/base/browser/ui/aria/aria", "vs/css!vs/editor/browser/services/hoverService/hover"], "vs/editor/browser/services/hoverService/updatableHoverWidget": ["require", "exports", "vs/base/browser/dom", "vs/base/common/cancellation", "vs/base/common/htmlContent", "vs/base/common/types", "vs/nls"], "vs/editor/browser/widget/multiDiffEditor/utils": ["require", "exports", "vs/base/common/actions"], "vs/editor/browser/widget/multiDiffEditor/objectPool": ["require", "exports"], "vs/css!vs/editor/browser/widget/multiDiffEditor/style": [], "vs/css!vs/base/browser/ui/table/table": [], "vs/base/browser/ui/tree/indexTreeModel": ["require", "exports", "vs/base/browser/ui/tree/tree", "vs/base/common/arrays", "vs/base/common/async", "vs/base/common/symbols", "vs/base/common/diff/diff", "vs/base/common/event", "vs/base/common/iterator"], "vs/base/browser/ui/tree/tree": ["require", "exports"], "vs/base/browser/ui/inputbox/inputBox": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/event", "vs/base/browser/formattedTextRenderer", "vs/base/browser/ui/actionbar/actionbar", "vs/base/browser/ui/aria/aria", "vs/base/browser/ui/hover/hoverDelegate2", "vs/base/browser/ui/hover/hoverDelegateFactory", "vs/base/browser/ui/scrollbar/scrollableElement", "vs/base/browser/ui/widget", "vs/base/common/event", "vs/base/common/history", "vs/base/common/objects", "vs/nls", "vs/css!vs/base/browser/ui/inputbox/inputBox"], "vs/css!vs/base/browser/ui/tree/media/tree": [], "vs/base/browser/ui/tree/compressedObjectTreeModel": ["require", "exports", "vs/base/browser/ui/tree/objectTreeModel", "vs/base/browser/ui/tree/tree", "vs/base/common/arrays", "vs/base/common/event", "vs/base/common/iterator"], "vs/base/browser/ui/tree/objectTreeModel": ["require", "exports", "vs/base/browser/ui/tree/indexTreeModel", "vs/base/browser/ui/tree/tree", "vs/base/common/iterator"], "vs/base/browser/ui/countBadge/countBadge": ["require", "exports", "vs/base/browser/dom", "vs/base/common/strings", "vs/css!vs/base/browser/ui/countBadge/countBadge"], "vs/base/browser/ui/highlightedlabel/highlightedLabel": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/hover/hoverDelegate2", "vs/base/browser/ui/hover/hoverDelegateFactory", "vs/base/browser/ui/iconLabel/iconLabels", "vs/base/common/lifecycle", "vs/base/common/objects"], "vs/css!vs/base/browser/ui/splitview/splitview": [], "vs/editor/common/core/positionToOffset": ["require", "exports", "vs/editor/common/core/offsetRange", "vs/editor/common/core/textLength"], "vs/css!vs/base/browser/ui/mouseCursor/mouseCursor": [], "vs/editor/browser/viewParts/lines/rangeUtil": ["require", "exports", "vs/editor/browser/view/renderingContext"], "vs/editor/browser/viewParts/minimap/minimapCharRenderer": ["require", "exports", "vs/editor/browser/viewParts/minimap/minimapCharSheet", "vs/base/common/uint"], "vs/editor/browser/viewParts/minimap/minimapCharSheet": ["require", "exports"], "vs/editor/browser/viewParts/minimap/minimapPreBaked": ["require", "exports", "vs/base/common/functional"], "vs/editor/common/textModelBracketPairs": ["require", "exports"], "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/beforeEditPositionMapper": ["require", "exports", "vs/editor/common/core/range", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/length"], "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/brackets": ["require", "exports", "vs/base/common/strings", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/ast", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/length", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/smallImmutableSet", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/tokenizer"], "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/length": ["require", "exports", "vs/base/common/strings", "vs/editor/common/core/range", "vs/editor/common/core/textLength"], "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/parser": ["require", "exports", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/ast", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/beforeEditPositionMapper", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/smallImmutableSet", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/length", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/concat23Trees", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/nodeReader"], "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/smallImmutableSet": ["require", "exports"], "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/tokenizer": ["require", "exports", "vs/base/common/errors", "vs/editor/common/encodedTokenAttributes", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/ast", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/length", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/smallImmutableSet"], "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/combineTextEditInfos": ["require", "exports", "vs/base/common/arrays", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/beforeEditPositionMapper", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/length"], "vs/editor/common/model/pieceTreeTextBuffer/rbTreeBase": ["require", "exports"], "vs/editor/common/model/fixedArray": ["require", "exports", "vs/base/common/arrays"], "vs/editor/common/tokens/contiguousMultilineTokens": ["require", "exports"], "vs/editor/common/tokens/contiguousTokensEditing": ["require", "exports", "vs/editor/common/tokens/lineTokens"], "vs/editor/common/cursor/oneCursor": ["require", "exports", "vs/editor/common/cursor<PERSON><PERSON>mon", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/core/selection"], "vs/css!vs/editor/browser/widget/diffEditor/components/accessibleDiffViewer": [], "vs/editor/browser/widget/diffEditor/components/diffEditorViewZones/inlineDiffDeletedCodeMargin": ["require", "exports", "vs/base/browser/dom", "vs/base/common/actions", "vs/base/common/codicons", "vs/base/common/lifecycle", "vs/base/common/platform", "vs/base/common/themables", "vs/nls"], "vs/editor/browser/widget/diffEditor/components/diffEditorViewZones/renderLines": ["require", "exports", "vs/base/browser/trustedTypes", "vs/editor/browser/config/domFontInfo", "vs/editor/common/config/editorOptions", "vs/editor/common/core/stringBuilder", "vs/editor/common/viewLayout/lineDecorations", "vs/editor/common/viewLayout/viewLineRenderer", "vs/editor/common/viewModel"], "vs/editor/browser/widget/diffEditor/utils/editorGutter": ["require", "exports", "vs/base/browser/dom", "vs/base/common/lifecycle", "vs/base/common/observable", "vs/editor/common/core/lineRange", "vs/editor/common/core/offsetRange"], "vs/editor/common/model/textModelText": ["require", "exports", "vs/editor/common/core/textEdit", "vs/editor/common/core/textLength"], "vs/base/browser/ui/scrollbar/scrollbarState": ["require", "exports"], "vs/css!vs/base/browser/ui/button/button": [], "vs/css!vs/base/browser/ui/keybindingLabel/keybindingLabel": [], "vs/editor/contrib/hover/browser/resizableContentWidget": ["require", "exports", "vs/base/browser/ui/resizable/resizable", "vs/base/common/lifecycle", "vs/editor/common/core/position", "vs/base/browser/dom"], "vs/base/browser/ui/toolbar/toolbar": ["require", "exports", "vs/base/browser/ui/actionbar/actionbar", "vs/base/browser/ui/dropdown/dropdownActionViewItem", "vs/base/common/actions", "vs/base/common/codicons", "vs/base/common/themables", "vs/base/common/event", "vs/base/common/lifecycle", "vs/nls", "vs/base/browser/ui/hover/hoverDelegateFactory", "vs/css!vs/base/browser/ui/toolbar/toolbar"], "vs/css!vs/base/browser/ui/selectBox/selectBoxCustom": [], "vs/css!vs/base/browser/ui/findinput/findInput": [], "vs/base/browser/ui/dropdown/dropdown": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/keyboardEvent", "vs/base/browser/touch", "vs/base/common/actions", "vs/base/common/event", "vs/css!vs/base/browser/ui/dropdown/dropdown"], "vs/css!vs/base/browser/ui/dropdown/dropdown": [], "vs/base/browser/ui/scrollbar/abstractScrollbar": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/fastDomNode", "vs/base/browser/globalPointerMoveMonitor", "vs/base/browser/ui/scrollbar/scrollbarArrow", "vs/base/browser/ui/scrollbar/scrollbarVisibilityController", "vs/base/browser/ui/widget", "vs/base/common/platform"], "vs/base/browser/ui/scrollbar/scrollbarArrow": ["require", "exports", "vs/base/browser/globalPointerMoveMonitor", "vs/base/browser/ui/widget", "vs/base/common/async", "vs/base/common/themables", "vs/base/browser/dom"], "vs/editor/common/model/bracketPairsTextModelPart/fixBrackets": ["require", "exports", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/brackets", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/length", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/parser", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/smallImmutableSet", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/tokenizer"], "vs/base/common/range": ["require", "exports"], "vs/base/browser/ui/list/rangeMap": ["require", "exports", "vs/base/common/range"], "vs/base/browser/ui/list/rowCache": ["require", "exports", "vs/base/browser/dom"], "vs/css!vs/base/browser/ui/iconLabel/iconlabel": [], "vs/css!vs/base/browser/ui/contextview/contextview": [], "vs/editor/common/services/languagesAssociations": ["require", "exports", "vs/base/common/glob", "vs/base/common/mime", "vs/base/common/network", "vs/base/common/path", "vs/base/common/resources", "vs/base/common/strings", "vs/editor/common/languages/modesRegistry"], "vs/platform/quickinput/browser/quickAccess": ["require", "exports", "vs/base/common/async", "vs/base/common/cancellation", "vs/base/common/event", "vs/base/common/lifecycle", "vs/platform/instantiation/common/instantiation", "vs/platform/quickinput/common/quickAccess", "vs/platform/quickinput/common/quickInput", "vs/platform/registry/common/platform"], "vs/platform/quickinput/browser/quickInput": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/keyboardEvent", "vs/base/browser/ui/toggle/toggle", "vs/base/common/arrays", "vs/base/common/async", "vs/base/common/codicons", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/platform", "vs/base/common/severity", "vs/base/common/themables", "vs/nls", "vs/platform/quickinput/common/quickInput", "vs/platform/quickinput/browser/quickInputUtils", "vs/platform/configuration/common/configuration", "vs/platform/hover/browser/hover", "vs/platform/contextkey/common/contextkey", "vs/css!vs/platform/quickinput/browser/media/quickInput"], "vs/platform/quickinput/browser/quickInputController": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/actionbar/actionbar", "vs/base/browser/ui/button/button", "vs/base/browser/ui/countBadge/countBadge", "vs/base/browser/ui/progressbar/progressbar", "vs/base/common/cancellation", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/severity", "vs/nls", "vs/platform/quickinput/common/quickInput", "vs/platform/quickinput/browser/quickInputBox", "vs/platform/quickinput/browser/quickInput", "vs/platform/layout/browser/layoutService", "vs/base/browser/window", "vs/platform/instantiation/common/instantiation", "vs/platform/quickinput/browser/quickInputTree", "vs/platform/contextkey/common/contextkey", "vs/platform/quickinput/browser/quickInputActions"], "vs/base/browser/ui/menu/menu": ["require", "exports", "vs/base/browser/browser", "vs/base/browser/touch", "vs/base/browser/dom", "vs/base/browser/keyboardEvent", "vs/base/browser/mouseEvent", "vs/base/browser/ui/actionbar/actionbar", "vs/base/browser/ui/actionbar/actionViewItems", "vs/base/browser/ui/contextview/contextview", "vs/base/browser/ui/scrollbar/scrollableElement", "vs/base/common/actions", "vs/base/common/async", "vs/base/common/codicons", "vs/base/common/codiconsUtil", "vs/base/common/themables", "vs/base/common/iconLabels", "vs/base/common/lifecycle", "vs/base/common/platform", "vs/base/common/strings"], "vs/css!vs/editor/browser/services/hoverService/hover": [], "vs/base/common/history": ["require", "exports", "vs/base/common/navigator"], "vs/css!vs/base/browser/ui/inputbox/inputBox": [], "vs/css!vs/base/browser/ui/countBadge/countBadge": [], "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/ast": ["require", "exports", "vs/base/common/errors", "vs/editor/common/core/cursorColumns", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/length", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/smallImmutableSet"], "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/concat23Trees": ["require", "exports", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/ast"], "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/nodeReader": ["require", "exports", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/length"], "vs/css!vs/base/browser/ui/toolbar/toolbar": [], "vs/base/browser/ui/scrollbar/scrollbarVisibilityController": ["require", "exports", "vs/base/common/async", "vs/base/common/lifecycle"], "vs/platform/quickinput/browser/quickInputUtils": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/event", "vs/base/common/event", "vs/base/browser/keyboardEvent", "vs/base/browser/touch", "vs/base/browser/ui/iconLabel/iconLabels", "vs/base/common/idGenerator", "vs/base/common/linkedText", "vs/nls", "vs/css!vs/platform/quickinput/browser/media/quickInput"], "vs/css!vs/platform/quickinput/browser/media/quickInput": [], "vs/base/browser/ui/progressbar/progressbar": ["require", "exports", "vs/base/browser/dom", "vs/base/common/async", "vs/base/common/lifecycle", "vs/css!vs/base/browser/ui/progressbar/progressbar"], "vs/platform/quickinput/browser/quickInputBox": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/findinput/findInput", "vs/base/common/lifecycle", "vs/base/common/severity", "vs/css!vs/platform/quickinput/browser/media/quickInput"], "vs/platform/quickinput/browser/quickInputTree": ["require", "exports", "vs/base/browser/dom", "vs/base/common/event", "vs/nls", "vs/platform/instantiation/common/instantiation", "vs/platform/list/browser/listService", "vs/platform/theme/common/themeService", "vs/base/common/lifecycle", "vs/platform/quickinput/common/quickInput", "vs/base/browser/keyboardEvent", "vs/base/common/platform", "vs/base/common/decorators", "vs/base/browser/ui/iconLabel/iconLabel", "vs/base/browser/ui/keybindingLabel/keybindingLabel", "vs/base/browser/ui/actionbar/actionbar", "vs/platform/theme/common/theme", "vs/base/common/uri", "vs/platform/quickinput/browser/quickInputUtils", "vs/base/common/lazy", "vs/base/common/iconLabels", "vs/base/common/comparers", "vs/base/common/strings", "vs/base/browser/ui/tree/abstractTree", "vs/base/common/async", "vs/base/common/errors", "vs/platform/accessibility/common/accessibility", "vs/base/common/observable", "vs/base/common/arrays"], "vs/platform/quickinput/browser/quickInputActions": ["require", "exports", "vs/base/common/platform", "vs/nls", "vs/platform/contextkey/common/contextkey", "vs/platform/contextkey/common/contextkeys", "vs/platform/keybinding/common/keybindingsRegistry", "vs/platform/quickinput/browser/quickInput", "vs/platform/quickinput/common/quickInput"], "vs/base/common/navigator": ["require", "exports"], "vs/base/common/linkedText": ["require", "exports", "vs/base/common/decorators"], "vs/css!vs/base/browser/ui/progressbar/progressbar": [], "vs/base/common/comparers": ["require", "exports", "vs/base/common/lazy"]}, "bundles": {"vs/editor/editor.main": ["vs/base/browser/dompurify/dompurify", "vs/base/browser/fastDomNode", "vs/base/browser/iframe", "vs/base/browser/performance", "vs/base/browser/ui/hover/hoverDelegate2", "vs/base/browser/ui/list/list", "vs/base/browser/ui/list/splice", "vs/base/browser/ui/scrollbar/scrollbarState", "vs/base/browser/ui/tree/tree", "vs/base/browser/window", "vs/base/browser/browser", "vs/base/common/arrays", "vs/base/common/arraysFind", "vs/base/common/cache", "vs/base/common/collections", "vs/base/common/color", "vs/base/common/decorators", "vs/base/common/diff/diffChange", "vs/base/common/equals", "vs/base/common/errors", "vs/base/browser/trustedTypes", "vs/base/common/assert", "vs/base/common/functional", "vs/base/common/hierarchicalKind", "vs/base/common/idGenerator", "vs/base/common/iterator", "vs/base/common/keyCodes", "vs/base/common/keybindings", "vs/base/common/lazy", "vs/base/browser/ui/hover/hoverDelegateFactory", "vs/base/common/buffer", "vs/base/common/comparers", "vs/base/common/lifecycle", "vs/base/common/linkedList", "vs/base/common/linkedText", "vs/base/common/map", "vs/base/common/marked/marked", "vs/base/common/mime", "vs/base/browser/dnd", "vs/base/common/naturalLanguage/korean", "vs/base/common/navigator", "vs/base/common/history", "vs/base/common/numbers", "vs/base/common/observableInternal/debugName", "vs/base/common/observableInternal/logging", "vs/base/common/observableInternal/autorun", "vs/base/common/observableInternal/base", "vs/base/common/observableInternal/derived", "vs/base/common/observableInternal/lazyObservableValue", "vs/base/common/observableInternal/api", "vs/base/common/observableInternal/promise", "vs/base/common/range", "vs/base/browser/ui/list/rangeMap", "vs/base/common/stopwatch", "vs/base/common/event", "vs/base/browser/event", "vs/base/common/cancellation", "vs/base/common/ime", "vs/base/common/observableInternal/utils", "vs/base/common/observable", "vs/base/common/scrollable", "vs/base/common/strings", "vs/base/common/filters", "vs/base/common/hash", "vs/base/common/diff/diff", "vs/base/common/search", "vs/base/common/severity", "vs/base/common/symbols", "vs/base/common/ternarySearchTree", "vs/base/common/tfIdf", "vs/base/common/types", "vs/base/common/codiconsUtil", "vs/base/common/codiconsLibrary", "vs/base/common/codicons", "vs/base/common/objects", "vs/base/common/themables", "vs/base/common/iconLabels", "vs/base/common/uint", "vs/base/common/uuid", "vs/base/common/dataTransfer", "vs/css!vs/base/browser/ui/actionbar/actionbar", "vs/css!vs/base/browser/ui/aria/aria", "vs/css!vs/base/browser/ui/button/button", "vs/css!vs/base/browser/ui/codicons/codicon/codicon", "vs/css!vs/base/browser/ui/codicons/codicon/codicon-modifiers", "vs/base/browser/ui/codicons/codiconStyles", "vs/css!vs/base/browser/ui/contextview/contextview", "vs/css!vs/base/browser/ui/countBadge/countBadge", "vs/css!vs/base/browser/ui/dropdown/dropdown", "vs/css!vs/base/browser/ui/findinput/findInput", "vs/css!vs/base/browser/ui/hover/hoverWidget", "vs/css!vs/base/browser/ui/iconLabel/iconlabel", "vs/css!vs/base/browser/ui/inputbox/inputBox", "vs/css!vs/base/browser/ui/keybindingLabel/keybindingLabel", "vs/css!vs/base/browser/ui/list/list", "vs/css!vs/base/browser/ui/mouseCursor/mouseCursor", "vs/base/browser/ui/mouseCursor/mouseCursor", "vs/css!vs/base/browser/ui/progressbar/progressbar", "vs/css!vs/base/browser/ui/sash/sash", "vs/css!vs/base/browser/ui/scrollbar/media/scrollbars", "vs/css!vs/base/browser/ui/selectBox/selectBox", "vs/css!vs/base/browser/ui/selectBox/selectBoxCustom", "vs/css!vs/base/browser/ui/splitview/splitview", "vs/css!vs/base/browser/ui/table/table", "vs/css!vs/base/browser/ui/toggle/toggle", "vs/css!vs/base/browser/ui/toolbar/toolbar", "vs/css!vs/base/browser/ui/tree/media/tree", "vs/css!vs/editor/browser/controller/textAreaHandler", "vs/css!vs/editor/browser/services/hoverService/hover", "vs/css!vs/editor/browser/viewParts/blockDecorations/blockDecorations", "vs/css!vs/editor/browser/viewParts/currentLineHighlight/currentLineHighlight", "vs/css!vs/editor/browser/viewParts/decorations/decorations", "vs/css!vs/editor/browser/viewParts/glyphMargin/glyphMargin", "vs/css!vs/editor/browser/viewParts/indentGuides/indentGuides", "vs/css!vs/editor/browser/viewParts/lineNumbers/lineNumbers", "vs/css!vs/editor/browser/viewParts/lines/viewLines", "vs/css!vs/editor/browser/viewParts/linesDecorations/linesDecorations", "vs/css!vs/editor/browser/viewParts/margin/margin", "vs/css!vs/editor/browser/viewParts/marginDecorations/marginDecorations", "vs/css!vs/editor/browser/viewParts/minimap/minimap", "vs/css!vs/editor/browser/viewParts/overlayWidgets/overlayWidgets", "vs/css!vs/editor/browser/viewParts/rulers/rulers", "vs/css!vs/editor/browser/viewParts/scrollDecoration/scrollDecoration", "vs/css!vs/editor/browser/viewParts/selections/selections", "vs/css!vs/editor/browser/viewParts/viewCursors/viewCursors", "vs/css!vs/editor/browser/viewParts/whitespace/whitespace", "vs/css!vs/editor/browser/widget/codeEditor/editor", "vs/css!vs/editor/browser/widget/diffEditor/components/accessibleDiffViewer", "vs/css!vs/editor/browser/widget/diffEditor/style", "vs/css!vs/editor/browser/widget/markdownRenderer/browser/renderedMarkdown", "vs/css!vs/editor/browser/widget/multiDiffEditor/style", "vs/css!vs/editor/contrib/anchorSelect/browser/anchorSelect", "vs/css!vs/editor/contrib/bracketMatching/browser/bracketMatching", "vs/css!vs/editor/contrib/codeAction/browser/lightBulbWidget", "vs/css!vs/editor/contrib/codelens/browser/codelensWidget", "vs/css!vs/editor/contrib/colorPicker/browser/colorPicker", "vs/css!vs/editor/contrib/dnd/browser/dnd", "vs/css!vs/editor/contrib/dropOrPasteInto/browser/postEditWidget", "vs/css!vs/editor/contrib/find/browser/findOptionsWidget", "vs/css!vs/editor/contrib/find/browser/findWidget", "vs/css!vs/editor/contrib/folding/browser/folding", "vs/css!vs/editor/contrib/gotoError/browser/media/gotoErrorWidget", "vs/css!vs/editor/contrib/gotoSymbol/browser/link/goToDefinitionAtPosition", "vs/css!vs/editor/contrib/gotoSymbol/browser/peek/referencesWidget", "vs/css!vs/editor/contrib/hover/browser/hover", "vs/css!vs/editor/contrib/inPlaceReplace/browser/inPlaceReplace", "vs/css!vs/editor/contrib/inlineCompletions/browser/hintsWidget/inlineCompletionsHintsWidget", "vs/css!vs/editor/contrib/inlineCompletions/browser/view/ghostTextView", "vs/css!vs/editor/contrib/inlineEdit/browser/inlineEdit", "vs/css!vs/editor/contrib/inlineEdit/browser/inlineEditHintsWidget", "vs/css!vs/editor/contrib/inlineEdit/browser/inlineEditSideBySideWidget", "vs/css!vs/editor/contrib/inlineEdits/browser/inlineEditsWidget", "vs/css!vs/editor/contrib/inlineProgress/browser/inlineProgressWidget", "vs/css!vs/editor/contrib/linkedEditing/browser/linkedEditing", "vs/css!vs/editor/contrib/links/browser/links", "vs/css!vs/editor/contrib/message/browser/messageController", "vs/css!vs/editor/contrib/parameterHints/browser/parameterHints", "vs/css!vs/editor/contrib/peekView/browser/media/peekViewWidget", "vs/css!vs/editor/contrib/placeholderText/browser/placeholderText", "vs/css!vs/editor/contrib/rename/browser/renameWidget", "vs/css!vs/editor/contrib/snippet/browser/snippetSession", "vs/css!vs/editor/contrib/stickyScroll/browser/stickyScroll", "vs/css!vs/editor/contrib/suggest/browser/media/suggest", "vs/css!vs/editor/contrib/symbolIcons/browser/symbolIcons", "vs/css!vs/editor/contrib/unicodeHighlighter/browser/bannerController", "vs/css!vs/editor/contrib/unicodeHighlighter/browser/unicodeHighlighter", "vs/css!vs/editor/contrib/wordHighlighter/browser/highlightDecorations", "vs/css!vs/editor/contrib/zoneWidget/browser/zoneWidget", "vs/css!vs/editor/standalone/browser/iPadShowKeyboard/iPadShowKeyboard", "vs/css!vs/editor/standalone/browser/inspectTokens/inspectTokens", "vs/css!vs/editor/standalone/browser/quickInput/standaloneQuickInput", "vs/css!vs/editor/standalone/browser/standalone-tokens", "vs/css!vs/platform/actionWidget/browser/actionWidget", "vs/css!vs/platform/actions/browser/menuEntryActionViewItem", "vs/css!vs/platform/opener/browser/link", "vs/css!vs/platform/quickinput/browser/media/quickInput", "vs/css!vs/platform/severityIcon/browser/media/severityIcon", "vs/editor/browser/config/domFontInfo", "vs/editor/browser/config/charWidthReader", "vs/editor/browser/config/migrateOptions", "vs/editor/browser/config/tabFocus", "vs/editor/browser/stableEditorScroll", "vs/editor/browser/view/renderingContext", "vs/editor/browser/viewParts/lines/domReadingContext", "vs/editor/browser/viewParts/lines/rangeUtil", "vs/editor/browser/viewParts/minimap/minimapCharSheet", "vs/editor/browser/viewParts/minimap/minimapChar<PERSON><PERSON>er", "vs/editor/browser/viewParts/minimap/minimapPreBaked", "vs/editor/browser/viewParts/minimap/minimapCharRendererFactory", "vs/editor/browser/widget/diffEditor/delegatingEditorImpl", "vs/editor/browser/widget/multiDiffEditor/objectPool", "vs/editor/common/config/diffEditor", "vs/editor/common/config/editorZoom", "vs/editor/common/core/characterClassifier", "vs/editor/common/core/cursorColumns", "vs/editor/common/core/eolCounter", "vs/editor/common/core/indentation", "vs/editor/common/core/offsetRange", "vs/editor/common/core/position", "vs/editor/browser/view/viewUserInputEvents", "vs/editor/common/core/range", "vs/editor/browser/controller/textAreaState", "vs/editor/common/core/editOperation", "vs/editor/common/commands/trimTrailingWhitespaceCommand", "vs/editor/common/core/lineRange", "vs/editor/common/core/rgba", "vs/editor/common/core/selection", "vs/editor/browser/observableCodeEditor", "vs/editor/common/commands/replaceCommand", "vs/editor/common/commands/surroundSelectionCommand", "vs/editor/common/core/textLength", "vs/editor/common/core/positionToOffset", "vs/editor/common/core/textEdit", "vs/editor/common/core/textModelDefaults", "vs/editor/common/core/wordCharacterClassifier", "vs/editor/common/core/wordHelper", "vs/editor/common/cursor/cursorAtomicMoveOperations", "vs/editor/common/cursor/cursorContext", "vs/editor/common/diff/defaultLinesDiffComputer/algorithms/diffAlgorithm", "vs/editor/common/diff/defaultLinesDiffComputer/algorithms/myersDiffAlgorithm", "vs/editor/common/diff/defaultLinesDiffComputer/heuristicSequenceOptimizations", "vs/editor/common/diff/defaultLinesDiffComputer/lineSequence", "vs/editor/common/diff/defaultLinesDiffComputer/utils", "vs/editor/common/diff/defaultLinesDiffComputer/algorithms/dynamicProgrammingDiffing", "vs/editor/common/diff/defaultLinesDiffComputer/linesSliceCharSequence", "vs/editor/common/diff/linesDiffComputer", "vs/editor/common/diff/rangeMapping", "vs/editor/common/diff/defaultLinesDiffComputer/computeMovedLines", "vs/editor/common/diff/defaultLinesDiffComputer/defaultLinesDiffComputer", "vs/editor/common/diff/legacyLinesDiffComputer", "vs/editor/common/diff/linesDiffComputers", "vs/editor/common/editorAction", "vs/editor/common/editor<PERSON><PERSON><PERSON>", "vs/editor/browser/editorBrowser", "vs/editor/common/editorFeatures", "vs/editor/common/editorTheme", "vs/editor/common/encodedTokenAttributes", "vs/editor/common/languages/defaultDocumentColorsComputer", "vs/editor/common/languages/languageConfiguration", "vs/editor/common/languages/linkComputer", "vs/editor/common/languages/supports", "vs/editor/common/cursor<PERSON><PERSON>mon", "vs/editor/common/cursor/cursorColumnSelection", "vs/editor/common/cursor/cursorMoveOperations", "vs/editor/common/cursor/cursorDeleteOperations", "vs/editor/common/cursor/cursorWordOperations", "vs/editor/common/cursor/cursorMoveCommands", "vs/editor/common/cursor/oneCursor", "vs/editor/common/cursor/cursorCollection", "vs/editor/common/languages/supports/characterPair", "vs/editor/common/languages/supports/indentRules", "vs/editor/common/languages/supports/inplaceReplaceSupport", "vs/editor/common/languages/supports/onEnter", "vs/editor/common/languages/supports/tokenization", "vs/editor/common/model", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/length", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/beforeEditPositionMapper", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/combineTextEditInfos", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/nodeReader", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/smallImmutableSet", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/ast", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/concat23Trees", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/parser", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/tokenizer", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/brackets", "vs/editor/common/model/bracketPairsTextModelPart/fixBrackets", "vs/editor/common/model/fixedArray", "vs/editor/common/model/indentationG<PERSON>ser", "vs/editor/common/model/intervalTree", "vs/editor/common/model/pieceTreeTextBuffer/rbTreeBase", "vs/editor/common/model/prefixSumComputer", "vs/editor/common/model/mirrorTextModel", "vs/editor/common/model/textModelPart", "vs/editor/common/model/textModelSearch", "vs/editor/common/model/pieceTreeTextBuffer/pieceTreeBase", "vs/editor/common/model/textModelText", "vs/editor/common/model/utils", "vs/editor/common/modelLineProjectionData", "vs/editor/common/services/editorWorkerHost", "vs/editor/common/services/findSectionHeaders", "vs/editor/common/services/treeViewsDnd", "vs/editor/common/services/unicodeTextModelHighlighter", "vs/editor/common/standalone/standaloneEnums", "vs/editor/common/textModelBracketPairs", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/bracketPairsTree", "vs/editor/common/textModelEvents", "vs/editor/common/textModelGuides", "vs/editor/common/model/guidesTextModelPart", "vs/editor/common/tokenizationRegistry", "vs/editor/common/tokens/contiguousMultilineTokens", "vs/editor/common/tokens/contiguousMultilineTokensBuilder", "vs/editor/common/tokens/lineTokens", "vs/editor/common/languages/supports/indentationLineProcessor", "vs/editor/common/languages/autoIndent", "vs/editor/common/tokens/contiguousTokensEditing", "vs/editor/common/tokens/contiguousTokensStore", "vs/editor/common/tokens/sparseMultilineTokens", "vs/editor/common/tokens/sparseTokensStore", "vs/editor/common/viewEventHandler", "vs/editor/browser/view/dynamicViewOverlay", "vs/editor/browser/view/viewPart", "vs/editor/browser/viewParts/blockDecorations/blockDecorations", "vs/editor/browser/viewParts/decorations/decorations", "vs/editor/browser/viewParts/glyphMargin/glyphMargin", "vs/editor/browser/viewParts/linesDecorations/linesDecorations", "vs/editor/browser/viewParts/margin/margin", "vs/editor/browser/viewParts/marginDecorations/marginDecorations", "vs/editor/browser/viewParts/rulers/rulers", "vs/editor/browser/viewParts/scrollDecoration/scrollDecoration", "vs/editor/browser/viewParts/viewZones/viewZones", "vs/editor/common/viewEvents", "vs/editor/common/viewLayout/lineDecorations", "vs/editor/common/viewLayout/linePart", "vs/editor/common/viewLayout/linesLayout", "vs/editor/common/viewLayout/viewLinesViewportData", "vs/editor/common/viewModel", "vs/editor/common/viewModel/glyphLanesModel", "vs/editor/common/viewModel/modelLineProjection", "vs/editor/common/viewModel/monospaceLineBreaksComputer", "vs/editor/common/viewModel/overviewZoneManager", "vs/editor/browser/viewParts/overviewRuler/overviewRuler", "vs/editor/common/viewModel/viewContext", "vs/editor/common/viewModelEventDispatcher", "vs/editor/common/viewLayout/viewLayout", "vs/editor/contrib/caretOperations/browser/moveCaretCommand", "vs/editor/contrib/codeAction/common/types", "vs/editor/contrib/colorPicker/browser/colorPickerModel", "vs/editor/contrib/comment/browser/blockCommentCommand", "vs/editor/contrib/comment/browser/lineCommentCommand", "vs/editor/contrib/dnd/browser/dragAndDropCommand", "vs/editor/contrib/find/browser/replaceAllCommand", "vs/editor/contrib/find/browser/replacePattern", "vs/editor/contrib/folding/browser/foldingRanges", "vs/editor/contrib/folding/browser/foldingModel", "vs/editor/contrib/folding/browser/hiddenRangeModel", "vs/editor/contrib/folding/browser/indentRangeProvider", "vs/editor/contrib/folding/browser/syntaxRangeProvider", "vs/editor/contrib/format/browser/formattingEdit", "vs/editor/contrib/hover/browser/contentHoverTypes", "vs/editor/contrib/hover/browser/hoverAccessibleViews", "vs/editor/contrib/hover/browser/hoverTypes", "vs/editor/contrib/inPlaceReplace/browser/inPlaceReplaceCommand", "vs/editor/contrib/indentation/common/indentUtils", "vs/editor/contrib/inlineCompletions/browser/controller/commandIds", "vs/editor/contrib/inlineCompletions/browser/inlineCompletionsAccessibleView", "vs/editor/contrib/inlineCompletions/browser/model/ghostText", "vs/editor/contrib/inlineCompletions/browser/model/singleTextEdit", "vs/editor/contrib/inlineCompletions/browser/utils", "vs/editor/contrib/inlineEdit/browser/commandIds", "vs/editor/contrib/linesOperations/browser/copyLinesCommand", "vs/editor/contrib/linesOperations/browser/sortLinesCommand", "vs/editor/contrib/semanticTokens/common/semanticTokensConfig", "vs/editor/contrib/smartSelect/browser/bracketSelections", "vs/editor/contrib/smartSelect/browser/wordSelections", "vs/editor/contrib/snippet/browser/snippetParser", "vs/editor/contrib/stickyScroll/browser/stickyScrollElement", "vs/editor/contrib/suggest/browser/completionModel", "vs/editor/contrib/suggest/browser/suggestCommitCharacters", "vs/editor/contrib/suggest/browser/suggestOvertypingCapturer", "vs/editor/contrib/suggest/browser/wordDistance", "vs/editor/standalone/browser/standaloneTreeSitterService", "vs/editor/standalone/common/monarch/monarch<PERSON><PERSON>mon", "vs/editor/standalone/common/monarch/monarchCompile", "vs/nls.messages", "vs/nls", "vs/base/common/actions", "vs/base/common/errorMessage", "vs/base/common/keybindingLabels", "vs/base/common/platform", "vs/base/browser/canIUse", "vs/base/browser/fonts", "vs/base/browser/keyboardEvent", "vs/base/browser/mouseEvent", "vs/base/common/async", "vs/base/browser/ui/scrollbar/scrollbarVisibilityController", "vs/base/browser/ui/tree/indexTreeModel", "vs/base/browser/ui/tree/objectTreeModel", "vs/base/browser/ui/tree/compressedObjectTreeModel", "vs/base/common/process", "vs/base/common/hotReload", "vs/base/common/hotReloadHelpers", "vs/base/common/path", "vs/base/common/extpath", "vs/base/common/fuzzyScorer", "vs/base/common/glob", "vs/base/common/labels", "vs/base/common/uri", "vs/base/common/marshalling", "vs/base/common/network", "vs/base/browser/dom", "vs/base/browser/domObservable", "vs/base/browser/formattedTextRenderer", "vs/base/browser/globalPointerMoveMonitor", "vs/base/browser/pixelRatio", "vs/base/browser/touch", "vs/base/browser/ui/aria/aria", "vs/base/browser/ui/contextview/contextview", "vs/base/browser/ui/countBadge/countBadge", "vs/base/browser/ui/dropdown/dropdown", "vs/base/browser/ui/iconLabel/iconLabels", "vs/base/browser/ui/highlightedlabel/highlightedLabel", "vs/base/browser/ui/iconLabel/iconLabel", "vs/base/browser/ui/keybindingLabel/keybindingLabel", "vs/base/browser/ui/list/rowCache", "vs/base/browser/ui/progressbar/progressbar", "vs/base/browser/ui/sash/sash", "vs/base/browser/ui/resizable/resizable", "vs/base/browser/ui/selectBox/selectBoxNative", "vs/base/browser/ui/widget", "vs/base/browser/ui/scrollbar/scrollbarArrow", "vs/base/browser/ui/scrollbar/abstractScrollbar", "vs/base/browser/ui/scrollbar/horizontalScrollbar", "vs/base/browser/ui/scrollbar/verticalScrollbar", "vs/base/browser/ui/scrollbar/scrollableElement", "vs/base/browser/ui/hover/hoverWidget", "vs/base/browser/ui/list/listView", "vs/base/browser/ui/list/listWidget", "vs/base/browser/ui/list/listPaging", "vs/base/browser/ui/splitview/splitview", "vs/base/browser/ui/table/tableWidget", "vs/base/browser/ui/toggle/toggle", "vs/base/browser/ui/findinput/findInputToggles", "vs/base/common/resources", "vs/base/common/htmlContent", "vs/base/browser/markdownRenderer", "vs/base/browser/ui/button/button", "vs/base/browser/ui/selectBox/selectBoxCustom", "vs/base/browser/ui/selectBox/selectBox", "vs/base/browser/ui/actionbar/actionViewItems", "vs/base/browser/ui/actionbar/actionbar", "vs/base/browser/ui/dropdown/dropdownActionViewItem", "vs/base/browser/ui/inputbox/inputBox", "vs/base/browser/ui/findinput/findInput", "vs/base/browser/ui/findinput/replaceInput", "vs/base/browser/ui/menu/menu", "vs/base/browser/ui/toolbar/toolbar", "vs/base/browser/ui/tree/abstractTree", "vs/base/browser/ui/tree/dataTree", "vs/base/browser/ui/tree/objectTree", "vs/base/browser/ui/tree/asyncDataTree", "vs/base/common/worker/simpleWorker", "vs/base/browser/defaultWorkerFactory", "vs/base/parts/storage/common/storage", "vs/editor/browser/config/elementSizeObserver", "vs/editor/browser/services/hoverService/updatableHoverWidget", "vs/editor/browser/viewParts/contentWidgets/contentWidgets", "vs/editor/browser/viewParts/overlayWidgets/overlayWidgets", "vs/editor/browser/widget/codeEditor/codeEditorContributions", "vs/editor/browser/widget/diffEditor/components/diffEditorSash", "vs/editor/browser/widget/diffEditor/components/diffEditorViewZones/inlineDiffDeletedCodeMargin", "vs/editor/browser/widget/diffEditor/features/revertButtonsFeature", "vs/editor/browser/widget/diffEditor/utils", "vs/editor/browser/widget/diffEditor/features/movedBlocksLinesFeature", "vs/editor/browser/widget/diffEditor/utils/editorGutter", "vs/editor/browser/widget/multiDiffEditor/utils", "vs/editor/common/config/editorOptions", "vs/editor/browser/viewParts/viewCursors/viewCursor", "vs/editor/common/config/fontInfo", "vs/editor/browser/config/fontMeasurements", "vs/editor/common/core/stringBuilder", "vs/editor/browser/view/domLineBreaksComputer", "vs/editor/browser/view/viewLayer", "vs/editor/browser/view/viewOverlays", "vs/editor/common/core/textChange", "vs/editor/common/languageSelector", "vs/editor/common/languageFeatureRegistry", "vs/editor/common/languages", "vs/editor/common/languages/nullTokenize", "vs/editor/common/languages/supports/richEditBrackets", "vs/editor/common/languages/supports/electricCharacter", "vs/editor/common/languages/supports/languageBracketsConfiguration", "vs/editor/common/languages/textToHtmlTokenizer", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsImpl", "vs/editor/common/model/editStack", "vs/editor/common/model/pieceTreeTextBuffer/pieceTreeTextBuffer", "vs/editor/common/model/pieceTreeTextBuffer/pieceTreeTextBufferBuilder", "vs/editor/common/model/textModelTokens", "vs/editor/common/model/tokens", "vs/editor/common/model/treeSitterTokens", "vs/editor/common/services/editorBaseApi", "vs/editor/common/services/semanticTokensDto", "vs/editor/common/services/textModelSync/textModelSync.impl", "vs/editor/common/services/editorSimpleWorker", "vs/editor/common/standaloneStrings", "vs/editor/common/viewLayout/viewLineRenderer", "vs/editor/browser/widget/diffEditor/components/diffEditorViewZones/renderLines", "vs/editor/common/viewModel/minimapTokensColorTracker", "vs/editor/common/viewModel/viewModelDecorations", "vs/editor/contrib/gotoSymbol/browser/link/clickLinkGesture", "vs/editor/contrib/gotoSymbol/browser/referencesModel", "vs/editor/contrib/hover/browser/contentHoverComputer", "vs/editor/contrib/hover/browser/hoverActionIds", "vs/editor/contrib/hover/browser/hoverOperation", "vs/editor/contrib/hover/browser/hoverUtils", "vs/editor/contrib/hover/browser/marginHoverComputer", "vs/editor/contrib/hover/browser/resizableContentWidget", "vs/editor/contrib/inlayHints/browser/inlayHints", "vs/editor/contrib/inlineCompletions/browser/model/provideInlineCompletions", "vs/editor/contrib/placeholderText/browser/placeholderTextContribution", "vs/platform/accessibility/browser/accessibleViewRegistry", "vs/platform/action/common/action", "vs/platform/action/common/actionCommonCategories", "vs/platform/contextkey/common/scanner", "vs/platform/editor/common/editor", "vs/platform/extensions/common/extensions", "vs/platform/files/common/files", "vs/platform/history/browser/historyWidgetKeybindingHint", "vs/platform/instantiation/common/descriptors", "vs/platform/instantiation/common/extensions", "vs/platform/instantiation/common/graph", "vs/platform/instantiation/common/instantiation", "vs/editor/browser/services/bulkEditService", "vs/editor/browser/services/codeEditorService", "vs/editor/browser/widget/diffEditor/features/hideUnchangedRegionsFeature", "vs/editor/common/languages/language", "vs/editor/common/services/editorW<PERSON>ker", "vs/editor/common/services/languageFeatures", "vs/editor/common/services/languageFeaturesService", "vs/editor/common/services/markerDecorations", "vs/editor/common/services/model", "vs/editor/common/services/resolverService", "vs/editor/common/services/semanticTokensStyling", "vs/editor/common/services/textResourceConfiguration", "vs/editor/common/services/treeSitterParserService", "vs/editor/common/services/treeViewsDndService", "vs/editor/contrib/colorPicker/browser/defaultDocumentColorProvider", "vs/editor/contrib/dropOrPasteInto/browser/edit", "vs/editor/contrib/inlineCompletions/browser/view/ghostTextView", "vs/editor/contrib/wordHighlighter/browser/textualHighlightProvider", "vs/editor/standalone/common/standaloneTheme", "vs/platform/accessibilitySignal/browser/accessibilitySignalService", "vs/platform/clipboard/common/clipboardService", "vs/platform/commands/common/commands", "vs/editor/contrib/codelens/browser/codelens", "vs/editor/contrib/links/browser/getLinks", "vs/editor/contrib/semanticTokens/common/getSemanticTokens", "vs/platform/configuration/common/configuration", "vs/editor/contrib/colorPicker/browser/color", "vs/editor/standalone/common/monarch/monarchLexer", "vs/editor/standalone/browser/colorizer", "vs/platform/contextkey/common/contextkey", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/contrib/inlineCompletions/browser/controller/inlineCompletionContextKeys", "vs/editor/contrib/inlineEdits/browser/consts", "vs/editor/contrib/parameterHints/browser/provideSignatureHelp", "vs/editor/contrib/parameterHints/browser/parameterHintsModel", "vs/editor/contrib/suggest/browser/suggestAlternatives", "vs/editor/contrib/suggest/browser/wordContextKey", "vs/platform/accessibility/common/accessibility", "vs/editor/browser/config/editorConfiguration", "vs/platform/contextkey/browser/contextKeyService", "vs/platform/contextkey/common/contextkeys", "vs/platform/contextview/browser/contextView", "vs/platform/dialogs/common/dialogs", "vs/platform/environment/common/environment", "vs/platform/hover/browser/hover", "vs/platform/instantiation/common/serviceCollection", "vs/platform/instantiation/common/instantiationService", "vs/platform/keybinding/common/baseResolvedKeybinding", "vs/platform/keybinding/common/keybinding", "vs/editor/contrib/hover/browser/contentHoverStatusBar", "vs/editor/contrib/hover/browser/contentHoverWidget", "vs/platform/keybinding/common/keybindingResolver", "vs/platform/keybinding/common/abstractKeybindingService", "vs/platform/keybinding/common/resolvedKeybindingItem", "vs/platform/keybinding/common/usLayoutResolvedKeybinding", "vs/platform/label/common/label", "vs/platform/layout/browser/layoutService", "vs/editor/standalone/browser/standaloneLayoutService", "vs/platform/accessibility/browser/accessibilityService", "vs/platform/contextview/browser/contextViewService", "vs/platform/log/common/log", "vs/editor/browser/controller/textAreaInput", "vs/editor/common/services/languageFeatureDebounce", "vs/editor/contrib/documentSymbols/browser/outlineModel", "vs/editor/contrib/diffEditorBreadcrumbs/browser/contribution", "vs/editor/contrib/documentSymbols/browser/documentSymbols", "vs/platform/clipboard/browser/clipboardService", "vs/platform/log/common/logService", "vs/platform/markers/common/markers", "vs/editor/contrib/gotoError/browser/markerNavigationService", "vs/platform/markers/common/markerService", "vs/platform/notification/common/notification", "vs/editor/contrib/dropOrPasteInto/browser/postEditWidget", "vs/platform/observable/common/platformObservableUtils", "vs/platform/observable/common/wrapInReloadableClass", "vs/platform/opener/common/opener", "vs/editor/browser/services/openerService", "vs/platform/opener/browser/link", "vs/platform/progress/common/progress", "vs/platform/quickinput/browser/pickerQuickAccess", "vs/platform/quickinput/browser/quickInputBox", "vs/platform/quickinput/browser/quickInputUtils", "vs/platform/quickinput/common/quickInput", "vs/platform/quickinput/browser/quickInput", "vs/platform/registry/common/platform", "vs/platform/dnd/browser/dnd", "vs/editor/browser/dnd", "vs/platform/jsonschemas/common/jsonContributionRegistry", "vs/platform/configuration/common/configurationRegistry", "vs/editor/common/config/editorConfigurationSchema", "vs/editor/common/languages/modesRegistry", "vs/editor/browser/widget/markdownRenderer/browser/markdownRenderer", "vs/editor/browser/services/hoverService/hoverWidget", "vs/editor/common/languages/languageConfigurationRegistry", "vs/editor/browser/services/editorWorkerService", "vs/editor/common/languages/enterAction", "vs/editor/common/commands/shiftCommand", "vs/editor/common/cursor/cursorTypeEditOperations", "vs/editor/common/cursor/cursorTypeOperations", "vs/editor/common/cursor/cursor", "vs/editor/common/model/tokenizationTextModelPart", "vs/editor/common/services/getIconClasses", "vs/editor/common/services/languagesAssociations", "vs/editor/common/services/languagesRegistry", "vs/editor/common/services/languageService", "vs/editor/contrib/hover/browser/marginHoverWidget", "vs/editor/contrib/hover/browser/marginHoverController", "vs/editor/contrib/indentation/common/indentation", "vs/editor/contrib/inlineCompletions/browser/model/inlineCompletionsSource", "vs/editor/contrib/linesOperations/browser/moveLinesCommand", "vs/editor/contrib/suggest/browser/suggestWidgetDetails", "vs/platform/configuration/common/configurationModels", "vs/platform/configuration/common/configurations", "vs/platform/keybinding/common/keybindingsRegistry", "vs/platform/actions/common/actions", "vs/editor/contrib/toggleTabFocusMode/browser/toggleTabFocusMode", "vs/platform/history/browser/contextScopedHistoryWidget", "vs/editor/contrib/suggest/browser/suggest", "vs/platform/quickinput/browser/quickInputActions", "vs/platform/quickinput/common/quickAccess", "vs/platform/quickinput/browser/helpQuickAccess", "vs/editor/standalone/browser/quickAccess/standaloneHelpQuickAccess", "vs/platform/quickinput/browser/quickAccess", "vs/platform/severityIcon/browser/severityIcon", "vs/platform/storage/common/storage", "vs/editor/contrib/codelens/browser/codeLensCache", "vs/editor/contrib/suggest/browser/suggestMemory", "vs/platform/actions/common/menuService", "vs/platform/telemetry/common/telemetry", "vs/editor/browser/editorExtensions", "vs/editor/browser/coreCommands", "vs/editor/browser/services/markerDecorations", "vs/editor/browser/view/viewController", "vs/editor/browser/widget/diffEditor/diffProviderFactoryService", "vs/editor/browser/widget/diffEditor/diffEditorViewModel", "vs/editor/contrib/anchorSelect/browser/anchorSelect", "vs/editor/contrib/caretOperations/browser/caretOperations", "vs/editor/contrib/caretOperations/browser/transpose", "vs/editor/contrib/comment/browser/comment", "vs/editor/contrib/cursorUndo/browser/cursorUndo", "vs/editor/contrib/editorState/browser/keybindingCancellation", "vs/editor/contrib/editorState/browser/editorState", "vs/editor/contrib/codeAction/browser/codeAction", "vs/editor/contrib/codeAction/browser/codeActionKeybindingResolver", "vs/editor/contrib/codeAction/browser/codeActionModel", "vs/editor/contrib/fontZoom/browser/fontZoom", "vs/editor/contrib/format/browser/format", "vs/editor/contrib/format/browser/formatActions", "vs/editor/contrib/gotoSymbol/browser/goToSymbol", "vs/editor/contrib/gotoSymbol/browser/symbolNavigation", "vs/editor/contrib/hover/browser/getHover", "vs/editor/contrib/indentation/browser/indentation", "vs/editor/contrib/lineSelection/browser/lineSelection", "vs/editor/contrib/linesOperations/browser/linesOperations", "vs/editor/contrib/longLinesHelper/browser/longLinesHelper", "vs/editor/contrib/message/browser/messageController", "vs/editor/contrib/readOnlyMessage/browser/contribution", "vs/editor/contrib/smartSelect/browser/smartSelect", "vs/editor/contrib/tokenization/browser/tokenization", "vs/editor/contrib/unusualLineTerminators/browser/unusualLineTerminators", "vs/editor/contrib/wordOperations/browser/wordOperations", "vs/editor/contrib/wordPartOperations/browser/wordPartOperations", "vs/editor/standalone/browser/iPadShowKeyboard/iPadShowKeyboard", "vs/editor/standalone/browser/inspectTokens/inspectTokens", "vs/platform/quickinput/browser/commandsQuickAccess", "vs/editor/contrib/quickAccess/browser/commandsQuickAccess", "vs/editor/standalone/browser/quickAccess/standaloneCommandsQuickAccess", "vs/platform/theme/common/colorUtils", "vs/platform/theme/common/colors/baseColors", "vs/platform/theme/common/colors/miscColors", "vs/platform/theme/common/colors/editorColors", "vs/platform/theme/common/colors/inputColors", "vs/platform/theme/common/colors/listColors", "vs/platform/theme/common/colors/menuColors", "vs/platform/theme/common/colors/minimapColors", "vs/platform/theme/common/colors/chartsColors", "vs/platform/theme/common/colors/quickpickColors", "vs/platform/theme/common/colors/searchColors", "vs/platform/theme/common/colorRegistry", "vs/editor/browser/editorDom", "vs/editor/browser/viewParts/minimap/minimap", "vs/editor/browser/widget/multiDiffEditor/colors", "vs/editor/contrib/symbolIcons/browser/symbolIcons", "vs/editor/contrib/codeAction/browser/codeActionMenu", "vs/platform/theme/browser/defaultStyles", "vs/editor/contrib/gotoSymbol/browser/peek/referencesTree", "vs/platform/actionWidget/browser/actionList", "vs/platform/actionWidget/browser/actionWidget", "vs/platform/contextview/browser/contextMenuHandler", "vs/platform/list/browser/listService", "vs/platform/theme/common/iconRegistry", "vs/editor/browser/widget/diffEditor/components/accessibleDiffViewer", "vs/editor/contrib/colorPicker/browser/colorPickerWidget", "vs/editor/contrib/hover/browser/markdownHoverParticipant", "vs/editor/contrib/parameterHints/browser/parameterHintsWidget", "vs/editor/contrib/parameterHints/browser/parameterHints", "vs/editor/contrib/unicodeHighlighter/browser/bannerController", "vs/platform/theme/browser/iconsStyleSheet", "vs/platform/theme/common/theme", "vs/editor/browser/viewParts/lines/viewLine", "vs/editor/browser/controller/mouseTarget", "vs/editor/browser/controller/mouseHandler", "vs/editor/browser/controller/pointerHandler", "vs/editor/browser/viewParts/lines/viewLines", "vs/platform/theme/common/themeService", "vs/editor/browser/services/abstractCodeEditorService", "vs/editor/browser/services/hoverService/hoverService", "vs/editor/browser/viewParts/editorScrollbar/editorScrollbar", "vs/editor/browser/viewParts/selections/selections", "vs/editor/browser/widget/diffEditor/features/overviewRulerFeature", "vs/editor/browser/widget/diffEditor/components/diffEditorEditors", "vs/editor/common/core/editorColorRegistry", "vs/editor/browser/viewParts/currentLineHighlight/currentLineHighlight", "vs/editor/browser/viewParts/indentGuides/indentGuides", "vs/editor/browser/viewParts/lineNumbers/lineNumbers", "vs/editor/browser/controller/text<PERSON><PERSON>Hand<PERSON>", "vs/editor/browser/viewParts/overviewRuler/decorationsOverviewRuler", "vs/editor/browser/viewParts/viewCursors/viewCursors", "vs/editor/browser/viewParts/whitespace/whitespace", "vs/editor/browser/view", "vs/editor/common/model/bracketPairsTextModelPart/colorizedBracketPairsDecorationProvider", "vs/editor/common/services/markerDecorationsService", "vs/editor/common/services/semanticTokensProviderStyling", "vs/editor/common/services/semanticTokensStylingService", "vs/editor/contrib/placeholderText/browser/placeholderText.contribution", "vs/editor/contrib/quickAccess/browser/editorNavigationQuickAccess", "vs/editor/contrib/quickAccess/browser/gotoLineQuickAccess", "vs/editor/contrib/quickAccess/browser/gotoSymbolQuickAccess", "vs/editor/contrib/rename/browser/renameWidget", "vs/editor/contrib/rename/browser/rename", "vs/editor/contrib/semanticTokens/browser/documentSemanticTokens", "vs/editor/contrib/semanticTokens/browser/viewportSemanticTokens", "vs/editor/contrib/suggest/browser/suggestWidgetRenderer", "vs/editor/standalone/browser/quickAccess/standaloneGotoLineQuickAccess", "vs/editor/standalone/browser/quickAccess/standaloneGotoSymbolQuickAccess", "vs/editor/standalone/browser/standaloneCodeEditorService", "vs/editor/standalone/common/themes", "vs/editor/standalone/browser/standaloneThemeService", "vs/editor/standalone/browser/toggleHighContrast/toggleHighContrast", "vs/platform/actions/browser/menuEntryActionViewItem", "vs/editor/contrib/suggest/browser/suggestWidgetStatus", "vs/platform/actions/browser/toolbar", "vs/editor/browser/widget/diffEditor/features/gutterFeature", "vs/editor/contrib/inlineCompletions/browser/hintsWidget/inlineCompletionsHintsWidget", "vs/editor/contrib/inlineEdit/browser/inlineEditHintsWidget", "vs/platform/contextview/browser/contextMenuService", "vs/platform/quickinput/browser/quickInputTree", "vs/platform/quickinput/browser/quickInputController", "vs/platform/quickinput/browser/quickInputService", "vs/editor/standalone/browser/quickInput/standaloneQuickInputService", "vs/platform/undoRedo/common/undoRedo", "vs/editor/common/model/textModel", "vs/editor/browser/widget/diffEditor/registrations.contribution", "vs/editor/browser/widget/diffEditor/components/diffEditorViewZones/diffEditorViewZones", "vs/editor/browser/widget/diffEditor/components/diffEditorDecorations", "vs/editor/browser/widget/diffEditor/diffEditorOptions", "vs/editor/common/services/modelService", "vs/editor/common/viewModel/viewModelLines", "vs/editor/common/viewModel/viewModelImpl", "vs/editor/browser/widget/codeEditor/codeEditorWidget", "vs/editor/browser/widget/codeEditor/embeddedCodeEditorWidget", "vs/editor/browser/widget/diffEditor/diffEditorWidget", "vs/editor/browser/widget/diffEditor/commands", "vs/editor/browser/widget/diffEditor/diffEditor.contribution", "vs/editor/browser/widget/multiDiffEditor/diffEditorItemTemplate", "vs/editor/browser/widget/multiDiffEditor/multiDiffEditorWidgetImpl", "vs/editor/browser/widget/multiDiffEditor/multiDiffEditorWidget", "vs/editor/contrib/bracketMatching/browser/bracketMatching", "vs/editor/contrib/codeAction/browser/lightBulbWidget", "vs/editor/contrib/codeAction/browser/codeActionController", "vs/editor/contrib/codeAction/browser/codeActionCommands", "vs/editor/contrib/codeAction/browser/codeActionContributions", "vs/editor/contrib/codelens/browser/codelensWidget", "vs/editor/contrib/codelens/browser/codelensController", "vs/editor/contrib/colorPicker/browser/colorDetector", "vs/editor/contrib/colorPicker/browser/colorHoverParticipant", "vs/editor/contrib/colorPicker/browser/standaloneColorPickerWidget", "vs/editor/contrib/colorPicker/browser/standaloneColorPickerActions", "vs/editor/contrib/dnd/browser/dnd", "vs/editor/contrib/find/browser/findDecorations", "vs/editor/contrib/find/browser/findModel", "vs/editor/contrib/find/browser/findOptionsWidget", "vs/editor/contrib/find/browser/findState", "vs/editor/contrib/find/browser/findWidget", "vs/editor/contrib/find/browser/findController", "vs/editor/contrib/folding/browser/foldingDecorations", "vs/editor/contrib/folding/browser/folding", "vs/editor/contrib/inPlaceReplace/browser/inPlaceReplace", "vs/editor/contrib/inlineEdit/browser/ghostTextWidget", "vs/editor/contrib/inlineEdit/browser/inlineEditSideBySideWidget", "vs/editor/contrib/inlineEdit/browser/inlineEditController", "vs/editor/contrib/inlineEdit/browser/commands", "vs/editor/contrib/inlineEdit/browser/inlineEdit.contribution", "vs/editor/contrib/inlineProgress/browser/inlineProgress", "vs/editor/contrib/dropOrPasteInto/browser/dropIntoEditorController", "vs/editor/contrib/linkedEditing/browser/linkedEditing", "vs/editor/contrib/links/browser/links", "vs/editor/contrib/sectionHeaders/browser/sectionHeaders", "vs/editor/contrib/stickyScroll/browser/stickyScrollModelProvider", "vs/editor/contrib/stickyScroll/browser/stickyScrollProvider", "vs/editor/contrib/stickyScroll/browser/stickyScrollWidget", "vs/editor/contrib/suggest/browser/suggestWidget", "vs/editor/contrib/wordHighlighter/browser/highlightDecorations", "vs/editor/contrib/multicursor/browser/multicursor", "vs/editor/contrib/wordHighlighter/browser/wordHighlighter", "vs/editor/contrib/zoneWidget/browser/zoneWidget", "vs/editor/contrib/peekView/browser/peekView", "vs/editor/contrib/gotoError/browser/gotoErrorWidget", "vs/editor/contrib/gotoError/browser/gotoError", "vs/editor/contrib/gotoSymbol/browser/peek/referencesWidget", "vs/editor/contrib/gotoSymbol/browser/peek/referencesController", "vs/editor/contrib/gotoSymbol/browser/goToCommands", "vs/editor/contrib/gotoSymbol/browser/link/goToDefinitionAtPosition", "vs/editor/contrib/hover/browser/markerHoverParticipant", "vs/editor/contrib/inlayHints/browser/inlayHintsLocations", "vs/editor/contrib/inlayHints/browser/inlayHintsController", "vs/editor/contrib/inlayHints/browser/inlayHintsHover", "vs/editor/contrib/hover/browser/contentHoverRendered", "vs/editor/contrib/hover/browser/contentHoverWidgetWrapper", "vs/editor/contrib/hover/browser/contentHoverController2", "vs/editor/contrib/colorPicker/browser/colorContributions", "vs/editor/contrib/hover/browser/hoverActions", "vs/editor/contrib/hover/browser/hoverContribution", "vs/editor/contrib/inlayHints/browser/inlayHintsContribution", "vs/editor/contrib/stickyScroll/browser/stickyScrollController", "vs/editor/contrib/stickyScroll/browser/stickyScrollActions", "vs/editor/contrib/stickyScroll/browser/stickyScrollContribution", "vs/editor/standalone/browser/referenceSearch/standaloneReferenceSearch", "vs/platform/undoRedo/common/undoRedoService", "vs/platform/workspace/common/workspace", "vs/editor/contrib/contextmenu/browser/contextmenu", "vs/editor/contrib/dropOrPasteInto/browser/defaultProviders", "vs/editor/contrib/dropOrPasteInto/browser/copyPasteController", "vs/editor/contrib/clipboard/browser/clipboard", "vs/editor/contrib/dropOrPasteInto/browser/copyPasteContribution", "vs/editor/contrib/dropOrPasteInto/browser/dropIntoEditorContribution", "vs/editor/contrib/snippet/browser/snippetVariables", "vs/editor/contrib/snippet/browser/snippetSession", "vs/editor/contrib/snippet/browser/snippetController2", "vs/editor/contrib/inlineCompletions/browser/model/inlineCompletionsModel", "vs/editor/contrib/suggest/browser/suggestModel", "vs/editor/contrib/suggest/browser/suggestController", "vs/editor/contrib/inlineCompletions/browser/model/suggestWidgetAdaptor", "vs/editor/contrib/inlineCompletions/browser/controller/inlineCompletionsController", "vs/editor/contrib/inlineCompletions/browser/controller/commands", "vs/editor/contrib/inlineCompletions/browser/hintsWidget/hoverParticipant", "vs/editor/contrib/inlineCompletions/browser/inlineCompletions.contribution", "vs/editor/contrib/inlineEdits/browser/inlineEditsWidget", "vs/editor/contrib/inlineEdits/browser/inlineEditsModel", "vs/editor/contrib/inlineEdits/browser/inlineEditsController", "vs/editor/contrib/inlineEdits/browser/commands", "vs/editor/contrib/inlineEdits/browser/inlineEdits.contribution", "vs/editor/contrib/suggest/browser/suggestInlineCompletions", "vs/platform/workspace/common/workspaceTrust", "vs/editor/contrib/unicodeHighlighter/browser/unicodeHighlighter", "vs/editor/editor.all", "vs/editor/standalone/browser/standaloneServices", "vs/editor/standalone/browser/standaloneCodeEditor", "vs/editor/standalone/browser/standaloneLanguages", "vs/editor/standalone/browser/standaloneWebWorker", "vs/editor/standalone/browser/standaloneEditor", "vs/editor/editor.api", "vs/editor/editor.main"], "vs/base/common/worker/simpleWorker": ["vs/base/common/arrays", "vs/base/common/arraysFind", "vs/base/common/cache", "vs/base/common/color", "vs/base/common/diff/diffChange", "vs/base/common/errors", "vs/base/common/assert", "vs/base/common/functional", "vs/base/common/iterator", "vs/base/common/keyCodes", "vs/base/common/lazy", "vs/base/common/lifecycle", "vs/base/common/linkedList", "vs/base/common/map", "vs/base/common/stopwatch", "vs/base/common/event", "vs/base/common/cancellation", "vs/base/common/strings", "vs/base/common/hash", "vs/base/common/diff/diff", "vs/base/common/symbols", "vs/base/common/types", "vs/base/common/codiconsUtil", "vs/base/common/codiconsLibrary", "vs/base/common/codicons", "vs/base/common/objects", "vs/base/common/uint", "vs/editor/common/core/characterClassifier", "vs/editor/common/core/offsetRange", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/core/lineRange", "vs/editor/common/core/selection", "vs/editor/common/core/textLength", "vs/editor/common/core/positionToOffset", "vs/editor/common/core/textEdit", "vs/editor/common/core/wordCharacterClassifier", "vs/editor/common/core/wordHelper", "vs/editor/common/diff/defaultLinesDiffComputer/algorithms/diffAlgorithm", "vs/editor/common/diff/defaultLinesDiffComputer/algorithms/myersDiffAlgorithm", "vs/editor/common/diff/defaultLinesDiffComputer/heuristicSequenceOptimizations", "vs/editor/common/diff/defaultLinesDiffComputer/lineSequence", "vs/editor/common/diff/defaultLinesDiffComputer/utils", "vs/editor/common/diff/defaultLinesDiffComputer/algorithms/dynamicProgrammingDiffing", "vs/editor/common/diff/defaultLinesDiffComputer/linesSliceCharSequence", "vs/editor/common/diff/linesDiffComputer", "vs/editor/common/diff/rangeMapping", "vs/editor/common/diff/defaultLinesDiffComputer/computeMovedLines", "vs/editor/common/diff/defaultLinesDiffComputer/defaultLinesDiffComputer", "vs/editor/common/diff/legacyLinesDiffComputer", "vs/editor/common/diff/linesDiffComputers", "vs/editor/common/languages/defaultDocumentColorsComputer", "vs/editor/common/languages/linkComputer", "vs/editor/common/languages/supports/inplaceReplaceSupport", "vs/editor/common/model", "vs/editor/common/model/prefixSumComputer", "vs/editor/common/model/mirrorTextModel", "vs/editor/common/model/textModelSearch", "vs/editor/common/services/editorWorkerHost", "vs/editor/common/services/findSectionHeaders", "vs/editor/common/services/unicodeTextModelHighlighter", "vs/editor/common/standalone/standaloneEnums", "vs/editor/common/tokenizationRegistry", "vs/nls.messages", "vs/nls", "vs/base/common/platform", "vs/base/common/async", "vs/base/common/process", "vs/base/common/path", "vs/base/common/uri", "vs/base/common/network", "vs/base/common/worker/simpleWorker", "vs/editor/common/languages", "vs/editor/common/services/editorBaseApi", "vs/editor/common/services/textModelSync/textModelSync.impl", "vs/editor/common/services/editorSimpleWorker"]}}