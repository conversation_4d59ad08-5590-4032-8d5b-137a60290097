{"version": 3, "sources": ["out-editor/vs/loader.js"], "sourcesContent": ["/*!-----------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/vscode/blob/main/LICENSE.txt\n *-----------------------------------------------------------*/\n\n/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n'use strict';\n/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n/*---------------------------------------------------------------------------------------------\n *---------------------------------------------------------------------------------------------\n *---------------------------------------------------------------------------------------------\n *---------------------------------------------------------------------------------------------\n *---------------------------------------------------------------------------------------------\n * Please make sure to make edits in the .ts file at https://github.com/microsoft/vscode-loader/\n *---------------------------------------------------------------------------------------------\n *---------------------------------------------------------------------------------------------\n *---------------------------------------------------------------------------------------------\n *---------------------------------------------------------------------------------------------\n *--------------------------------------------------------------------------------------------*/\nconst _amdLoaderGlobal = this;\nconst _commonjsGlobal = typeof global === 'object' ? global : {};\nvar AMDLoader;\n(function (AMDLoader) {\n\tAMDLoader.global = _amdLoaderGlobal;\n\tclass Environment {\n\t\tget isWindows() {\n\t\t\tthis._detect();\n\t\t\treturn this._isWindows;\n\t\t}\n\t\tget isNode() {\n\t\t\tthis._detect();\n\t\t\treturn this._isNode;\n\t\t}\n\t\tget isElectronRenderer() {\n\t\t\tthis._detect();\n\t\t\treturn this._isElectronRenderer;\n\t\t}\n\t\tget isWebWorker() {\n\t\t\tthis._detect();\n\t\t\treturn this._isWebWorker;\n\t\t}\n\t\tget isElectronNodeIntegrationWebWorker() {\n\t\t\tthis._detect();\n\t\t\treturn this._isElectronNodeIntegrationWebWorker;\n\t\t}\n\t\tconstructor() {\n\t\t\tthis._detected = false;\n\t\t\tthis._isWindows = false;\n\t\t\tthis._isNode = false;\n\t\t\tthis._isElectronRenderer = false;\n\t\t\tthis._isWebWorker = false;\n\t\t\tthis._isElectronNodeIntegrationWebWorker = false;\n\t\t}\n\t\t_detect() {\n\t\t\tif (this._detected) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tthis._detected = true;\n\t\t\tthis._isWindows = Environment._isWindows();\n\t\t\tthis._isNode = (typeof module !== 'undefined' && !!module.exports);\n\t\t\tthis._isElectronRenderer = (typeof process !== 'undefined' && typeof process.versions !== 'undefined' && typeof process.versions.electron !== 'undefined' && process.type === 'renderer');\n\t\t\tthis._isWebWorker = (typeof AMDLoader.global.importScripts === 'function');\n\t\t\tthis._isElectronNodeIntegrationWebWorker = this._isWebWorker && (typeof process !== 'undefined' && typeof process.versions !== 'undefined' && typeof process.versions.electron !== 'undefined' && process.type === 'worker');\n\t\t}\n\t\tstatic _isWindows() {\n\t\t\tif (typeof navigator !== 'undefined') {\n\t\t\t\tif (navigator.userAgent && navigator.userAgent.indexOf('Windows') >= 0) {\n\t\t\t\t\treturn true;\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (typeof process !== 'undefined') {\n\t\t\t\treturn (process.platform === 'win32');\n\t\t\t}\n\t\t\treturn false;\n\t\t}\n\t}\n\tAMDLoader.Environment = Environment;\n})(AMDLoader || (AMDLoader = {}));\n/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar AMDLoader;\n(function (AMDLoader) {\n\tclass LoaderEvent {\n\t\tconstructor(type, detail, timestamp) {\n\t\t\tthis.type = type;\n\t\t\tthis.detail = detail;\n\t\t\tthis.timestamp = timestamp;\n\t\t}\n\t}\n\tAMDLoader.LoaderEvent = LoaderEvent;\n\tclass LoaderEventRecorder {\n\t\tconstructor(loaderAvailableTimestamp) {\n\t\t\tthis._events = [new LoaderEvent(1 /* LoaderEventType.LoaderAvailable */, '', loaderAvailableTimestamp)];\n\t\t}\n\t\trecord(type, detail) {\n\t\t\tthis._events.push(new LoaderEvent(type, detail, AMDLoader.Utilities.getHighPerformanceTimestamp()));\n\t\t}\n\t\tgetEvents() {\n\t\t\treturn this._events;\n\t\t}\n\t}\n\tAMDLoader.LoaderEventRecorder = LoaderEventRecorder;\n\tclass NullLoaderEventRecorder {\n\t\trecord(type, detail) {\n\t\t\t// Nothing to do\n\t\t}\n\t\tgetEvents() {\n\t\t\treturn [];\n\t\t}\n\t}\n\tNullLoaderEventRecorder.INSTANCE = new NullLoaderEventRecorder();\n\tAMDLoader.NullLoaderEventRecorder = NullLoaderEventRecorder;\n})(AMDLoader || (AMDLoader = {}));\n/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar AMDLoader;\n(function (AMDLoader) {\n\tclass Utilities {\n\t\t/**\n\t\t * This method does not take care of / vs \\\n\t\t */\n\t\tstatic fileUriToFilePath(isWindows, uri) {\n\t\t\turi = decodeURI(uri).replace(/%23/g, '#');\n\t\t\tif (isWindows) {\n\t\t\t\tif (/^file:\\/\\/\\//.test(uri)) {\n\t\t\t\t\t// This is a URI without a hostname => return only the path segment\n\t\t\t\t\treturn uri.substr(8);\n\t\t\t\t}\n\t\t\t\tif (/^file:\\/\\//.test(uri)) {\n\t\t\t\t\treturn uri.substr(5);\n\t\t\t\t}\n\t\t\t}\n\t\t\telse {\n\t\t\t\tif (/^file:\\/\\//.test(uri)) {\n\t\t\t\t\treturn uri.substr(7);\n\t\t\t\t}\n\t\t\t}\n\t\t\t// Not sure...\n\t\t\treturn uri;\n\t\t}\n\t\tstatic startsWith(haystack, needle) {\n\t\t\treturn haystack.length >= needle.length && haystack.substr(0, needle.length) === needle;\n\t\t}\n\t\tstatic endsWith(haystack, needle) {\n\t\t\treturn haystack.length >= needle.length && haystack.substr(haystack.length - needle.length) === needle;\n\t\t}\n\t\t// only check for \"?\" before \"#\" to ensure that there is a real Query-String\n\t\tstatic containsQueryString(url) {\n\t\t\treturn /^[^\\#]*\\?/gi.test(url);\n\t\t}\n\t\t/**\n\t\t * Does `url` start with http:// or https:// or file:// or / ?\n\t\t */\n\t\tstatic isAbsolutePath(url) {\n\t\t\treturn /^((http:\\/\\/)|(https:\\/\\/)|(file:\\/\\/)|(\\/))/.test(url);\n\t\t}\n\t\tstatic forEachProperty(obj, callback) {\n\t\t\tif (obj) {\n\t\t\t\tlet key;\n\t\t\t\tfor (key in obj) {\n\t\t\t\t\tif (obj.hasOwnProperty(key)) {\n\t\t\t\t\t\tcallback(key, obj[key]);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\tstatic isEmpty(obj) {\n\t\t\tlet isEmpty = true;\n\t\t\tUtilities.forEachProperty(obj, () => {\n\t\t\t\tisEmpty = false;\n\t\t\t});\n\t\t\treturn isEmpty;\n\t\t}\n\t\tstatic recursiveClone(obj) {\n\t\t\tif (!obj || typeof obj !== 'object' || obj instanceof RegExp) {\n\t\t\t\treturn obj;\n\t\t\t}\n\t\t\tif (!Array.isArray(obj) && Object.getPrototypeOf(obj) !== Object.prototype) {\n\t\t\t\t// only clone \"simple\" objects\n\t\t\t\treturn obj;\n\t\t\t}\n\t\t\tlet result = Array.isArray(obj) ? [] : {};\n\t\t\tUtilities.forEachProperty(obj, (key, value) => {\n\t\t\t\tif (value && typeof value === 'object') {\n\t\t\t\t\tresult[key] = Utilities.recursiveClone(value);\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\tresult[key] = value;\n\t\t\t\t}\n\t\t\t});\n\t\t\treturn result;\n\t\t}\n\t\tstatic generateAnonymousModule() {\n\t\t\treturn '===anonymous' + (Utilities.NEXT_ANONYMOUS_ID++) + '===';\n\t\t}\n\t\tstatic isAnonymousModule(id) {\n\t\t\treturn Utilities.startsWith(id, '===anonymous');\n\t\t}\n\t\tstatic getHighPerformanceTimestamp() {\n\t\t\tif (!this.PERFORMANCE_NOW_PROBED) {\n\t\t\t\tthis.PERFORMANCE_NOW_PROBED = true;\n\t\t\t\tthis.HAS_PERFORMANCE_NOW = (AMDLoader.global.performance && typeof AMDLoader.global.performance.now === 'function');\n\t\t\t}\n\t\t\treturn (this.HAS_PERFORMANCE_NOW ? AMDLoader.global.performance.now() : Date.now());\n\t\t}\n\t}\n\tUtilities.NEXT_ANONYMOUS_ID = 1;\n\tUtilities.PERFORMANCE_NOW_PROBED = false;\n\tUtilities.HAS_PERFORMANCE_NOW = false;\n\tAMDLoader.Utilities = Utilities;\n})(AMDLoader || (AMDLoader = {}));\n/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar AMDLoader;\n(function (AMDLoader) {\n\tfunction ensureError(err) {\n\t\tif (err instanceof Error) {\n\t\t\treturn err;\n\t\t}\n\t\tconst result = new Error(err.message || String(err) || 'Unknown Error');\n\t\tif (err.stack) {\n\t\t\tresult.stack = err.stack;\n\t\t}\n\t\treturn result;\n\t}\n\tAMDLoader.ensureError = ensureError;\n\t;\n\tclass ConfigurationOptionsUtil {\n\t\t/**\n\t\t * Ensure configuration options make sense\n\t\t */\n\t\tstatic validateConfigurationOptions(options) {\n\t\t\tfunction defaultOnError(err) {\n\t\t\t\tif (err.phase === 'loading') {\n\t\t\t\t\tconsole.error('Loading \"' + err.moduleId + '\" failed');\n\t\t\t\t\tconsole.error(err);\n\t\t\t\t\tconsole.error('Here are the modules that depend on it:');\n\t\t\t\t\tconsole.error(err.neededBy);\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tif (err.phase === 'factory') {\n\t\t\t\t\tconsole.error('The factory function of \"' + err.moduleId + '\" has thrown an exception');\n\t\t\t\t\tconsole.error(err);\n\t\t\t\t\tconsole.error('Here are the modules that depend on it:');\n\t\t\t\t\tconsole.error(err.neededBy);\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t}\n\t\t\toptions = options || {};\n\t\t\tif (typeof options.baseUrl !== 'string') {\n\t\t\t\toptions.baseUrl = '';\n\t\t\t}\n\t\t\tif (typeof options.isBuild !== 'boolean') {\n\t\t\t\toptions.isBuild = false;\n\t\t\t}\n\t\t\tif (typeof options.paths !== 'object') {\n\t\t\t\toptions.paths = {};\n\t\t\t}\n\t\t\tif (typeof options.config !== 'object') {\n\t\t\t\toptions.config = {};\n\t\t\t}\n\t\t\tif (typeof options.catchError === 'undefined') {\n\t\t\t\toptions.catchError = false;\n\t\t\t}\n\t\t\tif (typeof options.recordStats === 'undefined') {\n\t\t\t\toptions.recordStats = false;\n\t\t\t}\n\t\t\tif (typeof options.urlArgs !== 'string') {\n\t\t\t\toptions.urlArgs = '';\n\t\t\t}\n\t\t\tif (typeof options.onError !== 'function') {\n\t\t\t\toptions.onError = defaultOnError;\n\t\t\t}\n\t\t\tif (!Array.isArray(options.ignoreDuplicateModules)) {\n\t\t\t\toptions.ignoreDuplicateModules = [];\n\t\t\t}\n\t\t\tif (options.baseUrl.length > 0) {\n\t\t\t\tif (!AMDLoader.Utilities.endsWith(options.baseUrl, '/')) {\n\t\t\t\t\toptions.baseUrl += '/';\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (typeof options.cspNonce !== 'string') {\n\t\t\t\toptions.cspNonce = '';\n\t\t\t}\n\t\t\tif (typeof options.preferScriptTags === 'undefined') {\n\t\t\t\toptions.preferScriptTags = false;\n\t\t\t}\n\t\t\tif (options.nodeCachedData && typeof options.nodeCachedData === 'object') {\n\t\t\t\tif (typeof options.nodeCachedData.seed !== 'string') {\n\t\t\t\t\toptions.nodeCachedData.seed = 'seed';\n\t\t\t\t}\n\t\t\t\tif (typeof options.nodeCachedData.writeDelay !== 'number' || options.nodeCachedData.writeDelay < 0) {\n\t\t\t\t\toptions.nodeCachedData.writeDelay = 1000 * 7;\n\t\t\t\t}\n\t\t\t\tif (!options.nodeCachedData.path || typeof options.nodeCachedData.path !== 'string') {\n\t\t\t\t\tconst err = ensureError(new Error('INVALID cached data configuration, \\'path\\' MUST be set'));\n\t\t\t\t\terr.phase = 'configuration';\n\t\t\t\t\toptions.onError(err);\n\t\t\t\t\toptions.nodeCachedData = undefined;\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn options;\n\t\t}\n\t\tstatic mergeConfigurationOptions(overwrite = null, base = null) {\n\t\t\tlet result = AMDLoader.Utilities.recursiveClone(base || {});\n\t\t\t// Merge known properties and overwrite the unknown ones\n\t\t\tAMDLoader.Utilities.forEachProperty(overwrite, (key, value) => {\n\t\t\t\tif (key === 'ignoreDuplicateModules' && typeof result.ignoreDuplicateModules !== 'undefined') {\n\t\t\t\t\tresult.ignoreDuplicateModules = result.ignoreDuplicateModules.concat(value);\n\t\t\t\t}\n\t\t\t\telse if (key === 'paths' && typeof result.paths !== 'undefined') {\n\t\t\t\t\tAMDLoader.Utilities.forEachProperty(value, (key2, value2) => result.paths[key2] = value2);\n\t\t\t\t}\n\t\t\t\telse if (key === 'config' && typeof result.config !== 'undefined') {\n\t\t\t\t\tAMDLoader.Utilities.forEachProperty(value, (key2, value2) => result.config[key2] = value2);\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\tresult[key] = AMDLoader.Utilities.recursiveClone(value);\n\t\t\t\t}\n\t\t\t});\n\t\t\treturn ConfigurationOptionsUtil.validateConfigurationOptions(result);\n\t\t}\n\t}\n\tAMDLoader.ConfigurationOptionsUtil = ConfigurationOptionsUtil;\n\tclass Configuration {\n\t\tconstructor(env, options) {\n\t\t\tthis._env = env;\n\t\t\tthis.options = ConfigurationOptionsUtil.mergeConfigurationOptions(options);\n\t\t\tthis._createIgnoreDuplicateModulesMap();\n\t\t\tthis._createSortedPathsRules();\n\t\t\tif (this.options.baseUrl === '') {\n\t\t\t\tif (this.options.nodeRequire && this.options.nodeRequire.main && this.options.nodeRequire.main.filename && this._env.isNode) {\n\t\t\t\t\tlet nodeMain = this.options.nodeRequire.main.filename;\n\t\t\t\t\tlet dirnameIndex = Math.max(nodeMain.lastIndexOf('/'), nodeMain.lastIndexOf('\\\\'));\n\t\t\t\t\tthis.options.baseUrl = nodeMain.substring(0, dirnameIndex + 1);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\t_createIgnoreDuplicateModulesMap() {\n\t\t\t// Build a map out of the ignoreDuplicateModules array\n\t\t\tthis.ignoreDuplicateModulesMap = {};\n\t\t\tfor (let i = 0; i < this.options.ignoreDuplicateModules.length; i++) {\n\t\t\t\tthis.ignoreDuplicateModulesMap[this.options.ignoreDuplicateModules[i]] = true;\n\t\t\t}\n\t\t}\n\t\t_createSortedPathsRules() {\n\t\t\t// Create an array our of the paths rules, sorted descending by length to\n\t\t\t// result in a more specific -> less specific order\n\t\t\tthis.sortedPathsRules = [];\n\t\t\tAMDLoader.Utilities.forEachProperty(this.options.paths, (from, to) => {\n\t\t\t\tif (!Array.isArray(to)) {\n\t\t\t\t\tthis.sortedPathsRules.push({\n\t\t\t\t\t\tfrom: from,\n\t\t\t\t\t\tto: [to]\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\tthis.sortedPathsRules.push({\n\t\t\t\t\t\tfrom: from,\n\t\t\t\t\t\tto: to\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t});\n\t\t\tthis.sortedPathsRules.sort((a, b) => {\n\t\t\t\treturn b.from.length - a.from.length;\n\t\t\t});\n\t\t}\n\t\t/**\n\t\t * Clone current configuration and overwrite options selectively.\n\t\t * @param options The selective options to overwrite with.\n\t\t * @result A new configuration\n\t\t */\n\t\tcloneAndMerge(options) {\n\t\t\treturn new Configuration(this._env, ConfigurationOptionsUtil.mergeConfigurationOptions(options, this.options));\n\t\t}\n\t\t/**\n\t\t * Get current options bag. Useful for passing it forward to plugins.\n\t\t */\n\t\tgetOptionsLiteral() {\n\t\t\treturn this.options;\n\t\t}\n\t\t_applyPaths(moduleId) {\n\t\t\tlet pathRule;\n\t\t\tfor (let i = 0, len = this.sortedPathsRules.length; i < len; i++) {\n\t\t\t\tpathRule = this.sortedPathsRules[i];\n\t\t\t\tif (AMDLoader.Utilities.startsWith(moduleId, pathRule.from)) {\n\t\t\t\t\tlet result = [];\n\t\t\t\t\tfor (let j = 0, lenJ = pathRule.to.length; j < lenJ; j++) {\n\t\t\t\t\t\tresult.push(pathRule.to[j] + moduleId.substr(pathRule.from.length));\n\t\t\t\t\t}\n\t\t\t\t\treturn result;\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn [moduleId];\n\t\t}\n\t\t_addUrlArgsToUrl(url) {\n\t\t\tif (AMDLoader.Utilities.containsQueryString(url)) {\n\t\t\t\treturn url + '&' + this.options.urlArgs;\n\t\t\t}\n\t\t\telse {\n\t\t\t\treturn url + '?' + this.options.urlArgs;\n\t\t\t}\n\t\t}\n\t\t_addUrlArgsIfNecessaryToUrl(url) {\n\t\t\tif (this.options.urlArgs) {\n\t\t\t\treturn this._addUrlArgsToUrl(url);\n\t\t\t}\n\t\t\treturn url;\n\t\t}\n\t\t_addUrlArgsIfNecessaryToUrls(urls) {\n\t\t\tif (this.options.urlArgs) {\n\t\t\t\tfor (let i = 0, len = urls.length; i < len; i++) {\n\t\t\t\t\turls[i] = this._addUrlArgsToUrl(urls[i]);\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn urls;\n\t\t}\n\t\t/**\n\t\t * Transform a module id to a location. Appends .js to module ids\n\t\t */\n\t\tmoduleIdToPaths(moduleId) {\n\t\t\tif (this._env.isNode) {\n\t\t\t\tconst isNodeModule = (this.options.amdModulesPattern instanceof RegExp\n\t\t\t\t\t&& !this.options.amdModulesPattern.test(moduleId));\n\t\t\t\tif (isNodeModule) {\n\t\t\t\t\t// This is a node module...\n\t\t\t\t\tif (this.isBuild()) {\n\t\t\t\t\t\t// ...and we are at build time, drop it\n\t\t\t\t\t\treturn ['empty:'];\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\t// ...and at runtime we create a `shortcut`-path\n\t\t\t\t\t\treturn ['node|' + moduleId];\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\tlet result = moduleId;\n\t\t\tlet results;\n\t\t\tif (!AMDLoader.Utilities.endsWith(result, '.js') && !AMDLoader.Utilities.isAbsolutePath(result)) {\n\t\t\t\tresults = this._applyPaths(result);\n\t\t\t\tfor (let i = 0, len = results.length; i < len; i++) {\n\t\t\t\t\tif (this.isBuild() && results[i] === 'empty:') {\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\t\t\t\t\tif (!AMDLoader.Utilities.isAbsolutePath(results[i])) {\n\t\t\t\t\t\tresults[i] = this.options.baseUrl + results[i];\n\t\t\t\t\t}\n\t\t\t\t\tif (!AMDLoader.Utilities.endsWith(results[i], '.js') && !AMDLoader.Utilities.containsQueryString(results[i])) {\n\t\t\t\t\t\tresults[i] = results[i] + '.js';\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\telse {\n\t\t\t\tif (!AMDLoader.Utilities.endsWith(result, '.js') && !AMDLoader.Utilities.containsQueryString(result)) {\n\t\t\t\t\tresult = result + '.js';\n\t\t\t\t}\n\t\t\t\tresults = [result];\n\t\t\t}\n\t\t\treturn this._addUrlArgsIfNecessaryToUrls(results);\n\t\t}\n\t\t/**\n\t\t * Transform a module id or url to a location.\n\t\t */\n\t\trequireToUrl(url) {\n\t\t\tlet result = url;\n\t\t\tif (!AMDLoader.Utilities.isAbsolutePath(result)) {\n\t\t\t\tresult = this._applyPaths(result)[0];\n\t\t\t\tif (!AMDLoader.Utilities.isAbsolutePath(result)) {\n\t\t\t\t\tresult = this.options.baseUrl + result;\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn this._addUrlArgsIfNecessaryToUrl(result);\n\t\t}\n\t\t/**\n\t\t * Flag to indicate if current execution is as part of a build.\n\t\t */\n\t\tisBuild() {\n\t\t\treturn this.options.isBuild;\n\t\t}\n\t\tshouldInvokeFactory(strModuleId) {\n\t\t\tif (!this.options.isBuild) {\n\t\t\t\t// outside of a build, all factories should be invoked\n\t\t\t\treturn true;\n\t\t\t}\n\t\t\t// during a build, only explicitly marked or anonymous modules get their factories invoked\n\t\t\tif (AMDLoader.Utilities.isAnonymousModule(strModuleId)) {\n\t\t\t\treturn true;\n\t\t\t}\n\t\t\tif (this.options.buildForceInvokeFactory && this.options.buildForceInvokeFactory[strModuleId]) {\n\t\t\t\treturn true;\n\t\t\t}\n\t\t\treturn false;\n\t\t}\n\t\t/**\n\t\t * Test if module `moduleId` is expected to be defined multiple times\n\t\t */\n\t\tisDuplicateMessageIgnoredFor(moduleId) {\n\t\t\treturn this.ignoreDuplicateModulesMap.hasOwnProperty(moduleId);\n\t\t}\n\t\t/**\n\t\t * Get the configuration settings for the provided module id\n\t\t */\n\t\tgetConfigForModule(moduleId) {\n\t\t\tif (this.options.config) {\n\t\t\t\treturn this.options.config[moduleId];\n\t\t\t}\n\t\t}\n\t\t/**\n\t\t * Should errors be caught when executing module factories?\n\t\t */\n\t\tshouldCatchError() {\n\t\t\treturn this.options.catchError;\n\t\t}\n\t\t/**\n\t\t * Should statistics be recorded?\n\t\t */\n\t\tshouldRecordStats() {\n\t\t\treturn this.options.recordStats;\n\t\t}\n\t\t/**\n\t\t * Forward an error to the error handler.\n\t\t */\n\t\tonError(err) {\n\t\t\tthis.options.onError(err);\n\t\t}\n\t}\n\tAMDLoader.Configuration = Configuration;\n})(AMDLoader || (AMDLoader = {}));\n/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar AMDLoader;\n(function (AMDLoader) {\n\t/**\n\t * Load `scriptSrc` only once (avoid multiple <script> tags)\n\t */\n\tclass OnlyOnceScriptLoader {\n\t\tconstructor(env) {\n\t\t\tthis._env = env;\n\t\t\tthis._scriptLoader = null;\n\t\t\tthis._callbackMap = {};\n\t\t}\n\t\tload(moduleManager, scriptSrc, callback, errorback) {\n\t\t\tif (!this._scriptLoader) {\n\t\t\t\tif (this._env.isWebWorker) {\n\t\t\t\t\tthis._scriptLoader = new WorkerScriptLoader();\n\t\t\t\t}\n\t\t\t\telse if (this._env.isElectronRenderer) {\n\t\t\t\t\tconst { preferScriptTags } = moduleManager.getConfig().getOptionsLiteral();\n\t\t\t\t\tif (preferScriptTags) {\n\t\t\t\t\t\tthis._scriptLoader = new BrowserScriptLoader();\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\tthis._scriptLoader = new NodeScriptLoader(this._env);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\telse if (this._env.isNode) {\n\t\t\t\t\tthis._scriptLoader = new NodeScriptLoader(this._env);\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\tthis._scriptLoader = new BrowserScriptLoader();\n\t\t\t\t}\n\t\t\t}\n\t\t\tlet scriptCallbacks = {\n\t\t\t\tcallback: callback,\n\t\t\t\terrorback: errorback\n\t\t\t};\n\t\t\tif (this._callbackMap.hasOwnProperty(scriptSrc)) {\n\t\t\t\tthis._callbackMap[scriptSrc].push(scriptCallbacks);\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tthis._callbackMap[scriptSrc] = [scriptCallbacks];\n\t\t\tthis._scriptLoader.load(moduleManager, scriptSrc, () => this.triggerCallback(scriptSrc), (err) => this.triggerErrorback(scriptSrc, err));\n\t\t}\n\t\ttriggerCallback(scriptSrc) {\n\t\t\tlet scriptCallbacks = this._callbackMap[scriptSrc];\n\t\t\tdelete this._callbackMap[scriptSrc];\n\t\t\tfor (let i = 0; i < scriptCallbacks.length; i++) {\n\t\t\t\tscriptCallbacks[i].callback();\n\t\t\t}\n\t\t}\n\t\ttriggerErrorback(scriptSrc, err) {\n\t\t\tlet scriptCallbacks = this._callbackMap[scriptSrc];\n\t\t\tdelete this._callbackMap[scriptSrc];\n\t\t\tfor (let i = 0; i < scriptCallbacks.length; i++) {\n\t\t\t\tscriptCallbacks[i].errorback(err);\n\t\t\t}\n\t\t}\n\t}\n\tclass BrowserScriptLoader {\n\t\t/**\n\t\t * Attach load / error listeners to a script element and remove them when either one has fired.\n\t\t * Implemented for browsers supporting HTML5 standard 'load' and 'error' events.\n\t\t */\n\t\tattachListeners(script, callback, errorback) {\n\t\t\tlet unbind = () => {\n\t\t\t\tscript.removeEventListener('load', loadEventListener);\n\t\t\t\tscript.removeEventListener('error', errorEventListener);\n\t\t\t};\n\t\t\tlet loadEventListener = (e) => {\n\t\t\t\tunbind();\n\t\t\t\tcallback();\n\t\t\t};\n\t\t\tlet errorEventListener = (e) => {\n\t\t\t\tunbind();\n\t\t\t\terrorback(e);\n\t\t\t};\n\t\t\tscript.addEventListener('load', loadEventListener);\n\t\t\tscript.addEventListener('error', errorEventListener);\n\t\t}\n\t\tload(moduleManager, scriptSrc, callback, errorback) {\n\t\t\tif (/^node\\|/.test(scriptSrc)) {\n\t\t\t\tlet opts = moduleManager.getConfig().getOptionsLiteral();\n\t\t\t\tlet nodeRequire = ensureRecordedNodeRequire(moduleManager.getRecorder(), (opts.nodeRequire || AMDLoader.global.nodeRequire));\n\t\t\t\tlet pieces = scriptSrc.split('|');\n\t\t\t\tlet moduleExports = null;\n\t\t\t\ttry {\n\t\t\t\t\tmoduleExports = nodeRequire(pieces[1]);\n\t\t\t\t}\n\t\t\t\tcatch (err) {\n\t\t\t\t\terrorback(err);\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tmoduleManager.enqueueDefineAnonymousModule([], () => moduleExports);\n\t\t\t\tcallback();\n\t\t\t}\n\t\t\telse {\n\t\t\t\tlet script = document.createElement('script');\n\t\t\t\tscript.setAttribute('async', 'async');\n\t\t\t\tscript.setAttribute('type', 'text/javascript');\n\t\t\t\tthis.attachListeners(script, callback, errorback);\n\t\t\t\tconst { trustedTypesPolicy } = moduleManager.getConfig().getOptionsLiteral();\n\t\t\t\tif (trustedTypesPolicy) {\n\t\t\t\t\tscriptSrc = trustedTypesPolicy.createScriptURL(scriptSrc);\n\t\t\t\t}\n\t\t\t\tscript.setAttribute('src', scriptSrc);\n\t\t\t\t// Propagate CSP nonce to dynamically created script tag.\n\t\t\t\tconst { cspNonce } = moduleManager.getConfig().getOptionsLiteral();\n\t\t\t\tif (cspNonce) {\n\t\t\t\t\tscript.setAttribute('nonce', cspNonce);\n\t\t\t\t}\n\t\t\t\tdocument.getElementsByTagName('head')[0].appendChild(script);\n\t\t\t}\n\t\t}\n\t}\n\tfunction canUseEval(moduleManager) {\n\t\tconst { trustedTypesPolicy } = moduleManager.getConfig().getOptionsLiteral();\n\t\ttry {\n\t\t\tconst func = (trustedTypesPolicy\n\t\t\t\t? self.eval(trustedTypesPolicy.createScript('', 'true')) // CodeQL [SM01632] the loader is responsible with loading code, fetch + eval is used on the web worker instead of importScripts if possible because importScripts is synchronous and we observed deadlocks on Safari\n\t\t\t\t: new Function('true') // CodeQL [SM01632] the loader is responsible with loading code, fetch + eval is used on the web worker instead of importScripts if possible because importScripts is synchronous and we observed deadlocks on Safari\n\t\t\t);\n\t\t\tfunc.call(self);\n\t\t\treturn true;\n\t\t}\n\t\tcatch (err) {\n\t\t\treturn false;\n\t\t}\n\t}\n\tclass WorkerScriptLoader {\n\t\tconstructor() {\n\t\t\tthis._cachedCanUseEval = null;\n\t\t}\n\t\t_canUseEval(moduleManager) {\n\t\t\tif (this._cachedCanUseEval === null) {\n\t\t\t\tthis._cachedCanUseEval = canUseEval(moduleManager);\n\t\t\t}\n\t\t\treturn this._cachedCanUseEval;\n\t\t}\n\t\tload(moduleManager, scriptSrc, callback, errorback) {\n\t\t\tif (/^node\\|/.test(scriptSrc)) {\n\t\t\t\tconst opts = moduleManager.getConfig().getOptionsLiteral();\n\t\t\t\tconst nodeRequire = ensureRecordedNodeRequire(moduleManager.getRecorder(), (opts.nodeRequire || AMDLoader.global.nodeRequire));\n\t\t\t\tconst pieces = scriptSrc.split('|');\n\t\t\t\tlet moduleExports = null;\n\t\t\t\ttry {\n\t\t\t\t\tmoduleExports = nodeRequire(pieces[1]);\n\t\t\t\t}\n\t\t\t\tcatch (err) {\n\t\t\t\t\terrorback(err);\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tmoduleManager.enqueueDefineAnonymousModule([], function () { return moduleExports; });\n\t\t\t\tcallback();\n\t\t\t}\n\t\t\telse {\n\t\t\t\tconst { trustedTypesPolicy } = moduleManager.getConfig().getOptionsLiteral();\n\t\t\t\tconst isCrossOrigin = (/^((http:)|(https:)|(file:))/.test(scriptSrc) && scriptSrc.substring(0, self.origin.length) !== self.origin);\n\t\t\t\tif (!isCrossOrigin && this._canUseEval(moduleManager)) {\n\t\t\t\t\t// use `fetch` if possible because `importScripts`\n\t\t\t\t\t// is synchronous and can lead to deadlocks on Safari\n\t\t\t\t\tfetch(scriptSrc).then((response) => {\n\t\t\t\t\t\tif (response.status !== 200) {\n\t\t\t\t\t\t\tthrow new Error(response.statusText);\n\t\t\t\t\t\t}\n\t\t\t\t\t\treturn response.text();\n\t\t\t\t\t}).then((text) => {\n\t\t\t\t\t\ttext = `${text}\\n//# sourceURL=${scriptSrc}`;\n\t\t\t\t\t\tconst func = (trustedTypesPolicy\n\t\t\t\t\t\t\t? self.eval(trustedTypesPolicy.createScript('', text)) // CodeQL [SM01632] the loader is responsible with loading code, fetch + eval is used on the web worker instead of importScripts if possible because importScripts is synchronous and we observed deadlocks on Safari\n\t\t\t\t\t\t\t: new Function(text) // CodeQL [SM01632] the loader is responsible with loading code, fetch + eval is used on the web worker instead of importScripts if possible because importScripts is synchronous and we observed deadlocks on Safari\n\t\t\t\t\t\t);\n\t\t\t\t\t\tfunc.call(self);\n\t\t\t\t\t\tcallback();\n\t\t\t\t\t}).then(undefined, errorback);\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\ttry {\n\t\t\t\t\tif (trustedTypesPolicy) {\n\t\t\t\t\t\tscriptSrc = trustedTypesPolicy.createScriptURL(scriptSrc);\n\t\t\t\t\t}\n\t\t\t\t\timportScripts(scriptSrc);\n\t\t\t\t\tcallback();\n\t\t\t\t}\n\t\t\t\tcatch (e) {\n\t\t\t\t\terrorback(e);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\tclass NodeScriptLoader {\n\t\tconstructor(env) {\n\t\t\tthis._env = env;\n\t\t\tthis._didInitialize = false;\n\t\t\tthis._didPatchNodeRequire = false;\n\t\t}\n\t\t_init(nodeRequire) {\n\t\t\tif (this._didInitialize) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tthis._didInitialize = true;\n\t\t\t// capture node modules\n\t\t\tthis._fs = nodeRequire('fs');\n\t\t\tthis._vm = nodeRequire('vm');\n\t\t\tthis._path = nodeRequire('path');\n\t\t\tthis._crypto = nodeRequire('crypto');\n\t\t}\n\t\t// patch require-function of nodejs such that we can manually create a script\n\t\t// from cached data. this is done by overriding the `Module._compile` function\n\t\t_initNodeRequire(nodeRequire, moduleManager) {\n\t\t\t// It is important to check for `nodeCachedData` first and then set `_didPatchNodeRequire`.\n\t\t\t// That's because `nodeCachedData` is set _after_ calling this for the first time...\n\t\t\tconst { nodeCachedData } = moduleManager.getConfig().getOptionsLiteral();\n\t\t\tif (!nodeCachedData) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tif (this._didPatchNodeRequire) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tthis._didPatchNodeRequire = true;\n\t\t\tconst that = this;\n\t\t\tconst Module = nodeRequire('module');\n\t\t\tfunction makeRequireFunction(mod) {\n\t\t\t\tconst Module = mod.constructor;\n\t\t\t\tlet require = function require(path) {\n\t\t\t\t\ttry {\n\t\t\t\t\t\treturn mod.require(path);\n\t\t\t\t\t}\n\t\t\t\t\tfinally {\n\t\t\t\t\t\t// nothing\n\t\t\t\t\t}\n\t\t\t\t};\n\t\t\t\trequire.resolve = function resolve(request, options) {\n\t\t\t\t\treturn Module._resolveFilename(request, mod, false, options);\n\t\t\t\t};\n\t\t\t\trequire.resolve.paths = function paths(request) {\n\t\t\t\t\treturn Module._resolveLookupPaths(request, mod);\n\t\t\t\t};\n\t\t\t\trequire.main = process.mainModule;\n\t\t\t\trequire.extensions = Module._extensions;\n\t\t\t\trequire.cache = Module._cache;\n\t\t\t\treturn require;\n\t\t\t}\n\t\t\tModule.prototype._compile = function (content, filename) {\n\t\t\t\t// remove shebang and create wrapper function\n\t\t\t\tconst scriptSource = Module.wrap(content.replace(/^#!.*/, ''));\n\t\t\t\t// create script\n\t\t\t\tconst recorder = moduleManager.getRecorder();\n\t\t\t\tconst cachedDataPath = that._getCachedDataPath(nodeCachedData, filename);\n\t\t\t\tconst options = { filename };\n\t\t\t\tlet hashData;\n\t\t\t\ttry {\n\t\t\t\t\tconst data = that._fs.readFileSync(cachedDataPath);\n\t\t\t\t\thashData = data.slice(0, 16);\n\t\t\t\t\toptions.cachedData = data.slice(16);\n\t\t\t\t\trecorder.record(60 /* LoaderEventType.CachedDataFound */, cachedDataPath);\n\t\t\t\t}\n\t\t\t\tcatch (_e) {\n\t\t\t\t\trecorder.record(61 /* LoaderEventType.CachedDataMissed */, cachedDataPath);\n\t\t\t\t}\n\t\t\t\tconst script = new that._vm.Script(scriptSource, options);\n\t\t\t\tconst compileWrapper = script.runInThisContext(options);\n\t\t\t\t// run script\n\t\t\t\tconst dirname = that._path.dirname(filename);\n\t\t\t\tconst require = makeRequireFunction(this);\n\t\t\t\tconst args = [this.exports, require, this, filename, dirname, process, _commonjsGlobal, Buffer];\n\t\t\t\tconst result = compileWrapper.apply(this.exports, args);\n\t\t\t\t// cached data aftermath\n\t\t\t\tthat._handleCachedData(script, scriptSource, cachedDataPath, !options.cachedData, moduleManager);\n\t\t\t\tthat._verifyCachedData(script, scriptSource, cachedDataPath, hashData, moduleManager);\n\t\t\t\treturn result;\n\t\t\t};\n\t\t}\n\t\tload(moduleManager, scriptSrc, callback, errorback) {\n\t\t\tconst opts = moduleManager.getConfig().getOptionsLiteral();\n\t\t\tconst nodeRequire = ensureRecordedNodeRequire(moduleManager.getRecorder(), (opts.nodeRequire || AMDLoader.global.nodeRequire));\n\t\t\tconst nodeInstrumenter = (opts.nodeInstrumenter || function (c) { return c; });\n\t\t\tthis._init(nodeRequire);\n\t\t\tthis._initNodeRequire(nodeRequire, moduleManager);\n\t\t\tlet recorder = moduleManager.getRecorder();\n\t\t\tif (/^node\\|/.test(scriptSrc)) {\n\t\t\t\tlet pieces = scriptSrc.split('|');\n\t\t\t\tlet moduleExports = null;\n\t\t\t\ttry {\n\t\t\t\t\tmoduleExports = nodeRequire(pieces[1]);\n\t\t\t\t}\n\t\t\t\tcatch (err) {\n\t\t\t\t\terrorback(err);\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tmoduleManager.enqueueDefineAnonymousModule([], () => moduleExports);\n\t\t\t\tcallback();\n\t\t\t}\n\t\t\telse {\n\t\t\t\tscriptSrc = AMDLoader.Utilities.fileUriToFilePath(this._env.isWindows, scriptSrc);\n\t\t\t\tconst normalizedScriptSrc = this._path.normalize(scriptSrc);\n\t\t\t\tconst vmScriptPathOrUri = this._getElectronRendererScriptPathOrUri(normalizedScriptSrc);\n\t\t\t\tconst wantsCachedData = Boolean(opts.nodeCachedData);\n\t\t\t\tconst cachedDataPath = wantsCachedData ? this._getCachedDataPath(opts.nodeCachedData, scriptSrc) : undefined;\n\t\t\t\tthis._readSourceAndCachedData(normalizedScriptSrc, cachedDataPath, recorder, (err, data, cachedData, hashData) => {\n\t\t\t\t\tif (err) {\n\t\t\t\t\t\terrorback(err);\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t\tlet scriptSource;\n\t\t\t\t\tif (data.charCodeAt(0) === NodeScriptLoader._BOM) {\n\t\t\t\t\t\tscriptSource = NodeScriptLoader._PREFIX + data.substring(1) + NodeScriptLoader._SUFFIX;\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\tscriptSource = NodeScriptLoader._PREFIX + data + NodeScriptLoader._SUFFIX;\n\t\t\t\t\t}\n\t\t\t\t\tscriptSource = nodeInstrumenter(scriptSource, normalizedScriptSrc);\n\t\t\t\t\tconst scriptOpts = { filename: vmScriptPathOrUri, cachedData };\n\t\t\t\t\tconst script = this._createAndEvalScript(moduleManager, scriptSource, scriptOpts, callback, errorback);\n\t\t\t\t\tthis._handleCachedData(script, scriptSource, cachedDataPath, wantsCachedData && !cachedData, moduleManager);\n\t\t\t\t\tthis._verifyCachedData(script, scriptSource, cachedDataPath, hashData, moduleManager);\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\t\t_createAndEvalScript(moduleManager, contents, options, callback, errorback) {\n\t\t\tconst recorder = moduleManager.getRecorder();\n\t\t\trecorder.record(31 /* LoaderEventType.NodeBeginEvaluatingScript */, options.filename);\n\t\t\tconst script = new this._vm.Script(contents, options);\n\t\t\tconst ret = script.runInThisContext(options);\n\t\t\tconst globalDefineFunc = moduleManager.getGlobalAMDDefineFunc();\n\t\t\tlet receivedDefineCall = false;\n\t\t\tconst localDefineFunc = function () {\n\t\t\t\treceivedDefineCall = true;\n\t\t\t\treturn globalDefineFunc.apply(null, arguments);\n\t\t\t};\n\t\t\tlocalDefineFunc.amd = globalDefineFunc.amd;\n\t\t\tret.call(AMDLoader.global, moduleManager.getGlobalAMDRequireFunc(), localDefineFunc, options.filename, this._path.dirname(options.filename));\n\t\t\trecorder.record(32 /* LoaderEventType.NodeEndEvaluatingScript */, options.filename);\n\t\t\tif (receivedDefineCall) {\n\t\t\t\tcallback();\n\t\t\t}\n\t\t\telse {\n\t\t\t\terrorback(new Error(`Didn't receive define call in ${options.filename}!`));\n\t\t\t}\n\t\t\treturn script;\n\t\t}\n\t\t_getElectronRendererScriptPathOrUri(path) {\n\t\t\tif (!this._env.isElectronRenderer) {\n\t\t\t\treturn path;\n\t\t\t}\n\t\t\tlet driveLetterMatch = path.match(/^([a-z])\\:(.*)/i);\n\t\t\tif (driveLetterMatch) {\n\t\t\t\t// windows\n\t\t\t\treturn `file:///${(driveLetterMatch[1].toUpperCase() + ':' + driveLetterMatch[2]).replace(/\\\\/g, '/')}`;\n\t\t\t}\n\t\t\telse {\n\t\t\t\t// nix\n\t\t\t\treturn `file://${path}`;\n\t\t\t}\n\t\t}\n\t\t_getCachedDataPath(config, filename) {\n\t\t\tconst hash = this._crypto.createHash('md5').update(filename, 'utf8').update(config.seed, 'utf8').update(process.arch, '').digest('hex');\n\t\t\tconst basename = this._path.basename(filename).replace(/\\.js$/, '');\n\t\t\treturn this._path.join(config.path, `${basename}-${hash}.code`);\n\t\t}\n\t\t_handleCachedData(script, scriptSource, cachedDataPath, createCachedData, moduleManager) {\n\t\t\tif (script.cachedDataRejected) {\n\t\t\t\t// cached data got rejected -> delete and re-create\n\t\t\t\tthis._fs.unlink(cachedDataPath, err => {\n\t\t\t\t\tmoduleManager.getRecorder().record(62 /* LoaderEventType.CachedDataRejected */, cachedDataPath);\n\t\t\t\t\tthis._createAndWriteCachedData(script, scriptSource, cachedDataPath, moduleManager);\n\t\t\t\t\tif (err) {\n\t\t\t\t\t\tmoduleManager.getConfig().onError(err);\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t}\n\t\t\telse if (createCachedData) {\n\t\t\t\t// no cached data, but wanted\n\t\t\t\tthis._createAndWriteCachedData(script, scriptSource, cachedDataPath, moduleManager);\n\t\t\t}\n\t\t}\n\t\t// Cached data format: | SOURCE_HASH | V8_CACHED_DATA |\n\t\t// -SOURCE_HASH is the md5 hash of the JS source (always 16 bytes)\n\t\t// -V8_CACHED_DATA is what v8 produces\n\t\t_createAndWriteCachedData(script, scriptSource, cachedDataPath, moduleManager) {\n\t\t\tlet timeout = Math.ceil(moduleManager.getConfig().getOptionsLiteral().nodeCachedData.writeDelay * (1 + Math.random()));\n\t\t\tlet lastSize = -1;\n\t\t\tlet iteration = 0;\n\t\t\tlet hashData = undefined;\n\t\t\tconst createLoop = () => {\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tif (!hashData) {\n\t\t\t\t\t\thashData = this._crypto.createHash('md5').update(scriptSource, 'utf8').digest();\n\t\t\t\t\t}\n\t\t\t\t\tconst cachedData = script.createCachedData();\n\t\t\t\t\tif (cachedData.length === 0 || cachedData.length === lastSize || iteration >= 5) {\n\t\t\t\t\t\t// done\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t\tif (cachedData.length < lastSize) {\n\t\t\t\t\t\t// less data than before: skip, try again next round\n\t\t\t\t\t\tcreateLoop();\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t\tlastSize = cachedData.length;\n\t\t\t\t\tthis._fs.writeFile(cachedDataPath, Buffer.concat([hashData, cachedData]), err => {\n\t\t\t\t\t\tif (err) {\n\t\t\t\t\t\t\tmoduleManager.getConfig().onError(err);\n\t\t\t\t\t\t}\n\t\t\t\t\t\tmoduleManager.getRecorder().record(63 /* LoaderEventType.CachedDataCreated */, cachedDataPath);\n\t\t\t\t\t\tcreateLoop();\n\t\t\t\t\t});\n\t\t\t\t}, timeout * (Math.pow(4, iteration++)));\n\t\t\t};\n\t\t\t// with some delay (`timeout`) create cached data\n\t\t\t// and repeat that (with backoff delay) until the\n\t\t\t// data seems to be not changing anymore\n\t\t\tcreateLoop();\n\t\t}\n\t\t_readSourceAndCachedData(sourcePath, cachedDataPath, recorder, callback) {\n\t\t\tif (!cachedDataPath) {\n\t\t\t\t// no cached data case\n\t\t\t\tthis._fs.readFile(sourcePath, { encoding: 'utf8' }, callback);\n\t\t\t}\n\t\t\telse {\n\t\t\t\t// cached data case: read both files in parallel\n\t\t\t\tlet source = undefined;\n\t\t\t\tlet cachedData = undefined;\n\t\t\t\tlet hashData = undefined;\n\t\t\t\tlet steps = 2;\n\t\t\t\tconst step = (err) => {\n\t\t\t\t\tif (err) {\n\t\t\t\t\t\tcallback(err);\n\t\t\t\t\t}\n\t\t\t\t\telse if (--steps === 0) {\n\t\t\t\t\t\tcallback(undefined, source, cachedData, hashData);\n\t\t\t\t\t}\n\t\t\t\t};\n\t\t\t\tthis._fs.readFile(sourcePath, { encoding: 'utf8' }, (err, data) => {\n\t\t\t\t\tsource = data;\n\t\t\t\t\tstep(err);\n\t\t\t\t});\n\t\t\t\tthis._fs.readFile(cachedDataPath, (err, data) => {\n\t\t\t\t\tif (!err && data && data.length > 0) {\n\t\t\t\t\t\thashData = data.slice(0, 16);\n\t\t\t\t\t\tcachedData = data.slice(16);\n\t\t\t\t\t\trecorder.record(60 /* LoaderEventType.CachedDataFound */, cachedDataPath);\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\trecorder.record(61 /* LoaderEventType.CachedDataMissed */, cachedDataPath);\n\t\t\t\t\t}\n\t\t\t\t\tstep(); // ignored: cached data is optional\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\t\t_verifyCachedData(script, scriptSource, cachedDataPath, hashData, moduleManager) {\n\t\t\tif (!hashData) {\n\t\t\t\t// nothing to do\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tif (script.cachedDataRejected) {\n\t\t\t\t// invalid anyways\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tsetTimeout(() => {\n\t\t\t\t// check source hash - the contract is that file paths change when file content\n\t\t\t\t// change (e.g use the commit or version id as cache path). this check is\n\t\t\t\t// for violations of this contract.\n\t\t\t\tconst hashDataNow = this._crypto.createHash('md5').update(scriptSource, 'utf8').digest();\n\t\t\t\tif (!hashData.equals(hashDataNow)) {\n\t\t\t\t\tmoduleManager.getConfig().onError(new Error(`FAILED TO VERIFY CACHED DATA, deleting stale '${cachedDataPath}' now, but a RESTART IS REQUIRED`));\n\t\t\t\t\tthis._fs.unlink(cachedDataPath, err => {\n\t\t\t\t\t\tif (err) {\n\t\t\t\t\t\t\tmoduleManager.getConfig().onError(err);\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t}, Math.ceil(5000 * (1 + Math.random())));\n\t\t}\n\t}\n\tNodeScriptLoader._BOM = 0xFEFF;\n\tNodeScriptLoader._PREFIX = '(function (require, define, __filename, __dirname) { ';\n\tNodeScriptLoader._SUFFIX = '\\n});';\n\tfunction ensureRecordedNodeRequire(recorder, _nodeRequire) {\n\t\tif (_nodeRequire.__$__isRecorded) {\n\t\t\t// it is already recorded\n\t\t\treturn _nodeRequire;\n\t\t}\n\t\tconst nodeRequire = function nodeRequire(what) {\n\t\t\trecorder.record(33 /* LoaderEventType.NodeBeginNativeRequire */, what);\n\t\t\ttry {\n\t\t\t\treturn _nodeRequire(what);\n\t\t\t}\n\t\t\tfinally {\n\t\t\t\trecorder.record(34 /* LoaderEventType.NodeEndNativeRequire */, what);\n\t\t\t}\n\t\t};\n\t\tnodeRequire.__$__isRecorded = true;\n\t\treturn nodeRequire;\n\t}\n\tAMDLoader.ensureRecordedNodeRequire = ensureRecordedNodeRequire;\n\tfunction createScriptLoader(env) {\n\t\treturn new OnlyOnceScriptLoader(env);\n\t}\n\tAMDLoader.createScriptLoader = createScriptLoader;\n})(AMDLoader || (AMDLoader = {}));\n/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar AMDLoader;\n(function (AMDLoader) {\n\t// ------------------------------------------------------------------------\n\t// ModuleIdResolver\n\tclass ModuleIdResolver {\n\t\tconstructor(fromModuleId) {\n\t\t\tlet lastSlash = fromModuleId.lastIndexOf('/');\n\t\t\tif (lastSlash !== -1) {\n\t\t\t\tthis.fromModulePath = fromModuleId.substr(0, lastSlash + 1);\n\t\t\t}\n\t\t\telse {\n\t\t\t\tthis.fromModulePath = '';\n\t\t\t}\n\t\t}\n\t\t/**\n\t\t * Normalize 'a/../name' to 'name', etc.\n\t\t */\n\t\tstatic _normalizeModuleId(moduleId) {\n\t\t\tlet r = moduleId, pattern;\n\t\t\t// replace /./ => /\n\t\t\tpattern = /\\/\\.\\//;\n\t\t\twhile (pattern.test(r)) {\n\t\t\t\tr = r.replace(pattern, '/');\n\t\t\t}\n\t\t\t// replace ^./ => nothing\n\t\t\tr = r.replace(/^\\.\\//g, '');\n\t\t\t// replace /aa/../ => / (BUT IGNORE /../../)\n\t\t\tpattern = /\\/(([^\\/])|([^\\/][^\\/\\.])|([^\\/\\.][^\\/])|([^\\/][^\\/][^\\/]+))\\/\\.\\.\\//;\n\t\t\twhile (pattern.test(r)) {\n\t\t\t\tr = r.replace(pattern, '/');\n\t\t\t}\n\t\t\t// replace ^aa/../ => nothing (BUT IGNORE ../../)\n\t\t\tr = r.replace(/^(([^\\/])|([^\\/][^\\/\\.])|([^\\/\\.][^\\/])|([^\\/][^\\/][^\\/]+))\\/\\.\\.\\//, '');\n\t\t\treturn r;\n\t\t}\n\t\t/**\n\t\t * Resolve relative module ids\n\t\t */\n\t\tresolveModule(moduleId) {\n\t\t\tlet result = moduleId;\n\t\t\tif (!AMDLoader.Utilities.isAbsolutePath(result)) {\n\t\t\t\tif (AMDLoader.Utilities.startsWith(result, './') || AMDLoader.Utilities.startsWith(result, '../')) {\n\t\t\t\t\tresult = ModuleIdResolver._normalizeModuleId(this.fromModulePath + result);\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn result;\n\t\t}\n\t}\n\tModuleIdResolver.ROOT = new ModuleIdResolver('');\n\tAMDLoader.ModuleIdResolver = ModuleIdResolver;\n\t// ------------------------------------------------------------------------\n\t// Module\n\tclass Module {\n\t\tconstructor(id, strId, dependencies, callback, errorback, moduleIdResolver) {\n\t\t\tthis.id = id;\n\t\t\tthis.strId = strId;\n\t\t\tthis.dependencies = dependencies;\n\t\t\tthis._callback = callback;\n\t\t\tthis._errorback = errorback;\n\t\t\tthis.moduleIdResolver = moduleIdResolver;\n\t\t\tthis.exports = {};\n\t\t\tthis.error = null;\n\t\t\tthis.exportsPassedIn = false;\n\t\t\tthis.unresolvedDependenciesCount = this.dependencies.length;\n\t\t\tthis._isComplete = false;\n\t\t}\n\t\tstatic _safeInvokeFunction(callback, args) {\n\t\t\ttry {\n\t\t\t\treturn {\n\t\t\t\t\treturnedValue: callback.apply(AMDLoader.global, args),\n\t\t\t\t\tproducedError: null\n\t\t\t\t};\n\t\t\t}\n\t\t\tcatch (e) {\n\t\t\t\treturn {\n\t\t\t\t\treturnedValue: null,\n\t\t\t\t\tproducedError: e\n\t\t\t\t};\n\t\t\t}\n\t\t}\n\t\tstatic _invokeFactory(config, strModuleId, callback, dependenciesValues) {\n\t\t\tif (!config.shouldInvokeFactory(strModuleId)) {\n\t\t\t\treturn {\n\t\t\t\t\treturnedValue: null,\n\t\t\t\t\tproducedError: null\n\t\t\t\t};\n\t\t\t}\n\t\t\tif (config.shouldCatchError()) {\n\t\t\t\treturn this._safeInvokeFunction(callback, dependenciesValues);\n\t\t\t}\n\t\t\treturn {\n\t\t\t\treturnedValue: callback.apply(AMDLoader.global, dependenciesValues),\n\t\t\t\tproducedError: null\n\t\t\t};\n\t\t}\n\t\tcomplete(recorder, config, dependenciesValues, inversedependenciesProvider) {\n\t\t\tthis._isComplete = true;\n\t\t\tlet producedError = null;\n\t\t\tif (this._callback) {\n\t\t\t\tif (typeof this._callback === 'function') {\n\t\t\t\t\trecorder.record(21 /* LoaderEventType.BeginInvokeFactory */, this.strId);\n\t\t\t\t\tlet r = Module._invokeFactory(config, this.strId, this._callback, dependenciesValues);\n\t\t\t\t\tproducedError = r.producedError;\n\t\t\t\t\trecorder.record(22 /* LoaderEventType.EndInvokeFactory */, this.strId);\n\t\t\t\t\tif (!producedError && typeof r.returnedValue !== 'undefined' && (!this.exportsPassedIn || AMDLoader.Utilities.isEmpty(this.exports))) {\n\t\t\t\t\t\tthis.exports = r.returnedValue;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\tthis.exports = this._callback;\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (producedError) {\n\t\t\t\tlet err = AMDLoader.ensureError(producedError);\n\t\t\t\terr.phase = 'factory';\n\t\t\t\terr.moduleId = this.strId;\n\t\t\t\terr.neededBy = inversedependenciesProvider(this.id);\n\t\t\t\tthis.error = err;\n\t\t\t\tconfig.onError(err);\n\t\t\t}\n\t\t\tthis.dependencies = null;\n\t\t\tthis._callback = null;\n\t\t\tthis._errorback = null;\n\t\t\tthis.moduleIdResolver = null;\n\t\t}\n\t\t/**\n\t\t * One of the direct dependencies or a transitive dependency has failed to load.\n\t\t */\n\t\tonDependencyError(err) {\n\t\t\tthis._isComplete = true;\n\t\t\tthis.error = err;\n\t\t\tif (this._errorback) {\n\t\t\t\tthis._errorback(err);\n\t\t\t\treturn true;\n\t\t\t}\n\t\t\treturn false;\n\t\t}\n\t\t/**\n\t\t * Is the current module complete?\n\t\t */\n\t\tisComplete() {\n\t\t\treturn this._isComplete;\n\t\t}\n\t}\n\tAMDLoader.Module = Module;\n\tclass ModuleIdProvider {\n\t\tconstructor() {\n\t\t\tthis._nextId = 0;\n\t\t\tthis._strModuleIdToIntModuleId = new Map();\n\t\t\tthis._intModuleIdToStrModuleId = [];\n\t\t\t// Ensure values 0, 1, 2 are assigned accordingly with ModuleId\n\t\t\tthis.getModuleId('exports');\n\t\t\tthis.getModuleId('module');\n\t\t\tthis.getModuleId('require');\n\t\t}\n\t\tgetMaxModuleId() {\n\t\t\treturn this._nextId;\n\t\t}\n\t\tgetModuleId(strModuleId) {\n\t\t\tlet id = this._strModuleIdToIntModuleId.get(strModuleId);\n\t\t\tif (typeof id === 'undefined') {\n\t\t\t\tid = this._nextId++;\n\t\t\t\tthis._strModuleIdToIntModuleId.set(strModuleId, id);\n\t\t\t\tthis._intModuleIdToStrModuleId[id] = strModuleId;\n\t\t\t}\n\t\t\treturn id;\n\t\t}\n\t\tgetStrModuleId(moduleId) {\n\t\t\treturn this._intModuleIdToStrModuleId[moduleId];\n\t\t}\n\t}\n\tclass RegularDependency {\n\t\tconstructor(id) {\n\t\t\tthis.id = id;\n\t\t}\n\t}\n\tRegularDependency.EXPORTS = new RegularDependency(0 /* ModuleId.EXPORTS */);\n\tRegularDependency.MODULE = new RegularDependency(1 /* ModuleId.MODULE */);\n\tRegularDependency.REQUIRE = new RegularDependency(2 /* ModuleId.REQUIRE */);\n\tAMDLoader.RegularDependency = RegularDependency;\n\tclass PluginDependency {\n\t\tconstructor(id, pluginId, pluginParam) {\n\t\t\tthis.id = id;\n\t\t\tthis.pluginId = pluginId;\n\t\t\tthis.pluginParam = pluginParam;\n\t\t}\n\t}\n\tAMDLoader.PluginDependency = PluginDependency;\n\tclass ModuleManager {\n\t\tconstructor(env, scriptLoader, defineFunc, requireFunc, loaderAvailableTimestamp = 0) {\n\t\t\tthis._env = env;\n\t\t\tthis._scriptLoader = scriptLoader;\n\t\t\tthis._loaderAvailableTimestamp = loaderAvailableTimestamp;\n\t\t\tthis._defineFunc = defineFunc;\n\t\t\tthis._requireFunc = requireFunc;\n\t\t\tthis._moduleIdProvider = new ModuleIdProvider();\n\t\t\tthis._config = new AMDLoader.Configuration(this._env);\n\t\t\tthis._hasDependencyCycle = false;\n\t\t\tthis._modules2 = [];\n\t\t\tthis._knownModules2 = [];\n\t\t\tthis._inverseDependencies2 = [];\n\t\t\tthis._inversePluginDependencies2 = new Map();\n\t\t\tthis._currentAnonymousDefineCall = null;\n\t\t\tthis._recorder = null;\n\t\t\tthis._buildInfoPath = [];\n\t\t\tthis._buildInfoDefineStack = [];\n\t\t\tthis._buildInfoDependencies = [];\n\t\t\tthis._requireFunc.moduleManager = this;\n\t\t}\n\t\treset() {\n\t\t\treturn new ModuleManager(this._env, this._scriptLoader, this._defineFunc, this._requireFunc, this._loaderAvailableTimestamp);\n\t\t}\n\t\tgetGlobalAMDDefineFunc() {\n\t\t\treturn this._defineFunc;\n\t\t}\n\t\tgetGlobalAMDRequireFunc() {\n\t\t\treturn this._requireFunc;\n\t\t}\n\t\tstatic _findRelevantLocationInStack(needle, stack) {\n\t\t\tlet normalize = (str) => str.replace(/\\\\/g, '/');\n\t\t\tlet normalizedPath = normalize(needle);\n\t\t\tlet stackPieces = stack.split(/\\n/);\n\t\t\tfor (let i = 0; i < stackPieces.length; i++) {\n\t\t\t\tlet m = stackPieces[i].match(/(.*):(\\d+):(\\d+)\\)?$/);\n\t\t\t\tif (m) {\n\t\t\t\t\tlet stackPath = m[1];\n\t\t\t\t\tlet stackLine = m[2];\n\t\t\t\t\tlet stackColumn = m[3];\n\t\t\t\t\tlet trimPathOffset = Math.max(stackPath.lastIndexOf(' ') + 1, stackPath.lastIndexOf('(') + 1);\n\t\t\t\t\tstackPath = stackPath.substr(trimPathOffset);\n\t\t\t\t\tstackPath = normalize(stackPath);\n\t\t\t\t\tif (stackPath === normalizedPath) {\n\t\t\t\t\t\tlet r = {\n\t\t\t\t\t\t\tline: parseInt(stackLine, 10),\n\t\t\t\t\t\t\tcol: parseInt(stackColumn, 10)\n\t\t\t\t\t\t};\n\t\t\t\t\t\tif (r.line === 1) {\n\t\t\t\t\t\t\tr.col -= '(function (require, define, __filename, __dirname) { '.length;\n\t\t\t\t\t\t}\n\t\t\t\t\t\treturn r;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\tthrow new Error('Could not correlate define call site for needle ' + needle);\n\t\t}\n\t\tgetBuildInfo() {\n\t\t\tif (!this._config.isBuild()) {\n\t\t\t\treturn null;\n\t\t\t}\n\t\t\tlet result = [], resultLen = 0;\n\t\t\tfor (let i = 0, len = this._modules2.length; i < len; i++) {\n\t\t\t\tlet m = this._modules2[i];\n\t\t\t\tif (!m) {\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t\tlet location = this._buildInfoPath[m.id] || null;\n\t\t\t\tlet defineStack = this._buildInfoDefineStack[m.id] || null;\n\t\t\t\tlet dependencies = this._buildInfoDependencies[m.id];\n\t\t\t\tresult[resultLen++] = {\n\t\t\t\t\tid: m.strId,\n\t\t\t\t\tpath: location,\n\t\t\t\t\tdefineLocation: (location && defineStack ? ModuleManager._findRelevantLocationInStack(location, defineStack) : null),\n\t\t\t\t\tdependencies: dependencies,\n\t\t\t\t\tshim: null,\n\t\t\t\t\texports: m.exports\n\t\t\t\t};\n\t\t\t}\n\t\t\treturn result;\n\t\t}\n\t\tgetRecorder() {\n\t\t\tif (!this._recorder) {\n\t\t\t\tif (this._config.shouldRecordStats()) {\n\t\t\t\t\tthis._recorder = new AMDLoader.LoaderEventRecorder(this._loaderAvailableTimestamp);\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\tthis._recorder = AMDLoader.NullLoaderEventRecorder.INSTANCE;\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn this._recorder;\n\t\t}\n\t\tgetLoaderEvents() {\n\t\t\treturn this.getRecorder().getEvents();\n\t\t}\n\t\t/**\n\t\t * Defines an anonymous module (without an id). Its name will be resolved as we receive a callback from the scriptLoader.\n\t\t * @param dependencies @see defineModule\n\t\t * @param callback @see defineModule\n\t\t */\n\t\tenqueueDefineAnonymousModule(dependencies, callback) {\n\t\t\tif (this._currentAnonymousDefineCall !== null) {\n\t\t\t\tthrow new Error('Can only have one anonymous define call per script file');\n\t\t\t}\n\t\t\tlet stack = null;\n\t\t\tif (this._config.isBuild()) {\n\t\t\t\tstack = new Error('StackLocation').stack || null;\n\t\t\t}\n\t\t\tthis._currentAnonymousDefineCall = {\n\t\t\t\tstack: stack,\n\t\t\t\tdependencies: dependencies,\n\t\t\t\tcallback: callback\n\t\t\t};\n\t\t}\n\t\t/**\n\t\t * Creates a module and stores it in _modules. The manager will immediately begin resolving its dependencies.\n\t\t * @param strModuleId An unique and absolute id of the module. This must not collide with another module's id\n\t\t * @param dependencies An array with the dependencies of the module. Special keys are: \"require\", \"exports\" and \"module\"\n\t\t * @param callback if callback is a function, it will be called with the resolved dependencies. if callback is an object, it will be considered as the exports of the module.\n\t\t */\n\t\tdefineModule(strModuleId, dependencies, callback, errorback, stack, moduleIdResolver = new ModuleIdResolver(strModuleId)) {\n\t\t\tlet moduleId = this._moduleIdProvider.getModuleId(strModuleId);\n\t\t\tif (this._modules2[moduleId]) {\n\t\t\t\tif (!this._config.isDuplicateMessageIgnoredFor(strModuleId)) {\n\t\t\t\t\tconsole.warn('Duplicate definition of module \\'' + strModuleId + '\\'');\n\t\t\t\t}\n\t\t\t\t// Super important! Completely ignore duplicate module definition\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tlet m = new Module(moduleId, strModuleId, this._normalizeDependencies(dependencies, moduleIdResolver), callback, errorback, moduleIdResolver);\n\t\t\tthis._modules2[moduleId] = m;\n\t\t\tif (this._config.isBuild()) {\n\t\t\t\tthis._buildInfoDefineStack[moduleId] = stack;\n\t\t\t\tthis._buildInfoDependencies[moduleId] = (m.dependencies || []).map(dep => this._moduleIdProvider.getStrModuleId(dep.id));\n\t\t\t}\n\t\t\t// Resolving of dependencies is immediate (not in a timeout). If there's a need to support a packer that concatenates in an\n\t\t\t// unordered manner, in order to finish processing the file, execute the following method in a timeout\n\t\t\tthis._resolve(m);\n\t\t}\n\t\t_normalizeDependency(dependency, moduleIdResolver) {\n\t\t\tif (dependency === 'exports') {\n\t\t\t\treturn RegularDependency.EXPORTS;\n\t\t\t}\n\t\t\tif (dependency === 'module') {\n\t\t\t\treturn RegularDependency.MODULE;\n\t\t\t}\n\t\t\tif (dependency === 'require') {\n\t\t\t\treturn RegularDependency.REQUIRE;\n\t\t\t}\n\t\t\t// Normalize dependency and then request it from the manager\n\t\t\tlet bangIndex = dependency.indexOf('!');\n\t\t\tif (bangIndex >= 0) {\n\t\t\t\tlet strPluginId = moduleIdResolver.resolveModule(dependency.substr(0, bangIndex));\n\t\t\t\tlet pluginParam = moduleIdResolver.resolveModule(dependency.substr(bangIndex + 1));\n\t\t\t\tlet dependencyId = this._moduleIdProvider.getModuleId(strPluginId + '!' + pluginParam);\n\t\t\t\tlet pluginId = this._moduleIdProvider.getModuleId(strPluginId);\n\t\t\t\treturn new PluginDependency(dependencyId, pluginId, pluginParam);\n\t\t\t}\n\t\t\treturn new RegularDependency(this._moduleIdProvider.getModuleId(moduleIdResolver.resolveModule(dependency)));\n\t\t}\n\t\t_normalizeDependencies(dependencies, moduleIdResolver) {\n\t\t\tlet result = [], resultLen = 0;\n\t\t\tfor (let i = 0, len = dependencies.length; i < len; i++) {\n\t\t\t\tresult[resultLen++] = this._normalizeDependency(dependencies[i], moduleIdResolver);\n\t\t\t}\n\t\t\treturn result;\n\t\t}\n\t\t_relativeRequire(moduleIdResolver, dependencies, callback, errorback) {\n\t\t\tif (typeof dependencies === 'string') {\n\t\t\t\treturn this.synchronousRequire(dependencies, moduleIdResolver);\n\t\t\t}\n\t\t\tthis.defineModule(AMDLoader.Utilities.generateAnonymousModule(), dependencies, callback, errorback, null, moduleIdResolver);\n\t\t}\n\t\t/**\n\t\t * Require synchronously a module by its absolute id. If the module is not loaded, an exception will be thrown.\n\t\t * @param id The unique and absolute id of the required module\n\t\t * @return The exports of module 'id'\n\t\t */\n\t\tsynchronousRequire(_strModuleId, moduleIdResolver = new ModuleIdResolver(_strModuleId)) {\n\t\t\tlet dependency = this._normalizeDependency(_strModuleId, moduleIdResolver);\n\t\t\tlet m = this._modules2[dependency.id];\n\t\t\tif (!m) {\n\t\t\t\tthrow new Error('Check dependency list! Synchronous require cannot resolve module \\'' + _strModuleId + '\\'. This is the first mention of this module!');\n\t\t\t}\n\t\t\tif (!m.isComplete()) {\n\t\t\t\tthrow new Error('Check dependency list! Synchronous require cannot resolve module \\'' + _strModuleId + '\\'. This module has not been resolved completely yet.');\n\t\t\t}\n\t\t\tif (m.error) {\n\t\t\t\tthrow m.error;\n\t\t\t}\n\t\t\treturn m.exports;\n\t\t}\n\t\tconfigure(params, shouldOverwrite) {\n\t\t\tlet oldShouldRecordStats = this._config.shouldRecordStats();\n\t\t\tif (shouldOverwrite) {\n\t\t\t\tthis._config = new AMDLoader.Configuration(this._env, params);\n\t\t\t}\n\t\t\telse {\n\t\t\t\tthis._config = this._config.cloneAndMerge(params);\n\t\t\t}\n\t\t\tif (this._config.shouldRecordStats() && !oldShouldRecordStats) {\n\t\t\t\tthis._recorder = null;\n\t\t\t}\n\t\t}\n\t\tgetConfig() {\n\t\t\treturn this._config;\n\t\t}\n\t\t/**\n\t\t * Callback from the scriptLoader when a module has been loaded.\n\t\t * This means its code is available and has been executed.\n\t\t */\n\t\t_onLoad(moduleId) {\n\t\t\tif (this._currentAnonymousDefineCall !== null) {\n\t\t\t\tlet defineCall = this._currentAnonymousDefineCall;\n\t\t\t\tthis._currentAnonymousDefineCall = null;\n\t\t\t\t// Hit an anonymous define call\n\t\t\t\tthis.defineModule(this._moduleIdProvider.getStrModuleId(moduleId), defineCall.dependencies, defineCall.callback, null, defineCall.stack);\n\t\t\t}\n\t\t}\n\t\t_createLoadError(moduleId, _err) {\n\t\t\tlet strModuleId = this._moduleIdProvider.getStrModuleId(moduleId);\n\t\t\tlet neededBy = (this._inverseDependencies2[moduleId] || []).map((intModuleId) => this._moduleIdProvider.getStrModuleId(intModuleId));\n\t\t\tconst err = AMDLoader.ensureError(_err);\n\t\t\terr.phase = 'loading';\n\t\t\terr.moduleId = strModuleId;\n\t\t\terr.neededBy = neededBy;\n\t\t\treturn err;\n\t\t}\n\t\t/**\n\t\t * Callback from the scriptLoader when a module hasn't been loaded.\n\t\t * This means that the script was not found (e.g. 404) or there was an error in the script.\n\t\t */\n\t\t_onLoadError(moduleId, err) {\n\t\t\tconst error = this._createLoadError(moduleId, err);\n\t\t\tif (!this._modules2[moduleId]) {\n\t\t\t\tthis._modules2[moduleId] = new Module(moduleId, this._moduleIdProvider.getStrModuleId(moduleId), [], () => { }, null, null);\n\t\t\t}\n\t\t\t// Find any 'local' error handlers, walk the entire chain of inverse dependencies if necessary.\n\t\t\tlet seenModuleId = [];\n\t\t\tfor (let i = 0, len = this._moduleIdProvider.getMaxModuleId(); i < len; i++) {\n\t\t\t\tseenModuleId[i] = false;\n\t\t\t}\n\t\t\tlet someoneNotified = false;\n\t\t\tlet queue = [];\n\t\t\tqueue.push(moduleId);\n\t\t\tseenModuleId[moduleId] = true;\n\t\t\twhile (queue.length > 0) {\n\t\t\t\tlet queueElement = queue.shift();\n\t\t\t\tlet m = this._modules2[queueElement];\n\t\t\t\tif (m) {\n\t\t\t\t\tsomeoneNotified = m.onDependencyError(error) || someoneNotified;\n\t\t\t\t}\n\t\t\t\tlet inverseDeps = this._inverseDependencies2[queueElement];\n\t\t\t\tif (inverseDeps) {\n\t\t\t\t\tfor (let i = 0, len = inverseDeps.length; i < len; i++) {\n\t\t\t\t\t\tlet inverseDep = inverseDeps[i];\n\t\t\t\t\t\tif (!seenModuleId[inverseDep]) {\n\t\t\t\t\t\t\tqueue.push(inverseDep);\n\t\t\t\t\t\t\tseenModuleId[inverseDep] = true;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (!someoneNotified) {\n\t\t\t\tthis._config.onError(error);\n\t\t\t}\n\t\t}\n\t\t/**\n\t\t * Walks (recursively) the dependencies of 'from' in search of 'to'.\n\t\t * Returns true if there is such a path or false otherwise.\n\t\t * @param from Module id to start at\n\t\t * @param to Module id to look for\n\t\t */\n\t\t_hasDependencyPath(fromId, toId) {\n\t\t\tlet from = this._modules2[fromId];\n\t\t\tif (!from) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\tlet inQueue = [];\n\t\t\tfor (let i = 0, len = this._moduleIdProvider.getMaxModuleId(); i < len; i++) {\n\t\t\t\tinQueue[i] = false;\n\t\t\t}\n\t\t\tlet queue = [];\n\t\t\t// Insert 'from' in queue\n\t\t\tqueue.push(from);\n\t\t\tinQueue[fromId] = true;\n\t\t\twhile (queue.length > 0) {\n\t\t\t\t// Pop first inserted element of queue\n\t\t\t\tlet element = queue.shift();\n\t\t\t\tlet dependencies = element.dependencies;\n\t\t\t\tif (dependencies) {\n\t\t\t\t\t// Walk the element's dependencies\n\t\t\t\t\tfor (let i = 0, len = dependencies.length; i < len; i++) {\n\t\t\t\t\t\tlet dependency = dependencies[i];\n\t\t\t\t\t\tif (dependency.id === toId) {\n\t\t\t\t\t\t\t// There is a path to 'to'\n\t\t\t\t\t\t\treturn true;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tlet dependencyModule = this._modules2[dependency.id];\n\t\t\t\t\t\tif (dependencyModule && !inQueue[dependency.id]) {\n\t\t\t\t\t\t\t// Insert 'dependency' in queue\n\t\t\t\t\t\t\tinQueue[dependency.id] = true;\n\t\t\t\t\t\t\tqueue.push(dependencyModule);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\t// There is no path to 'to'\n\t\t\treturn false;\n\t\t}\n\t\t/**\n\t\t * Walks (recursively) the dependencies of 'from' in search of 'to'.\n\t\t * Returns cycle as array.\n\t\t * @param from Module id to start at\n\t\t * @param to Module id to look for\n\t\t */\n\t\t_findCyclePath(fromId, toId, depth) {\n\t\t\tif (fromId === toId || depth === 50) {\n\t\t\t\treturn [fromId];\n\t\t\t}\n\t\t\tlet from = this._modules2[fromId];\n\t\t\tif (!from) {\n\t\t\t\treturn null;\n\t\t\t}\n\t\t\t// Walk the element's dependencies\n\t\t\tlet dependencies = from.dependencies;\n\t\t\tif (dependencies) {\n\t\t\t\tfor (let i = 0, len = dependencies.length; i < len; i++) {\n\t\t\t\t\tlet path = this._findCyclePath(dependencies[i].id, toId, depth + 1);\n\t\t\t\t\tif (path !== null) {\n\t\t\t\t\t\tpath.push(fromId);\n\t\t\t\t\t\treturn path;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn null;\n\t\t}\n\t\t/**\n\t\t * Create the local 'require' that is passed into modules\n\t\t */\n\t\t_createRequire(moduleIdResolver) {\n\t\t\tlet result = ((dependencies, callback, errorback) => {\n\t\t\t\treturn this._relativeRequire(moduleIdResolver, dependencies, callback, errorback);\n\t\t\t});\n\t\t\tresult.toUrl = (id) => {\n\t\t\t\treturn this._config.requireToUrl(moduleIdResolver.resolveModule(id));\n\t\t\t};\n\t\t\tresult.getStats = () => {\n\t\t\t\treturn this.getLoaderEvents();\n\t\t\t};\n\t\t\tresult.hasDependencyCycle = () => {\n\t\t\t\treturn this._hasDependencyCycle;\n\t\t\t};\n\t\t\tresult.config = (params, shouldOverwrite = false) => {\n\t\t\t\tthis.configure(params, shouldOverwrite);\n\t\t\t};\n\t\t\tresult.__$__nodeRequire = AMDLoader.global.nodeRequire;\n\t\t\treturn result;\n\t\t}\n\t\t_loadModule(moduleId) {\n\t\t\tif (this._modules2[moduleId] || this._knownModules2[moduleId]) {\n\t\t\t\t// known module\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tthis._knownModules2[moduleId] = true;\n\t\t\tlet strModuleId = this._moduleIdProvider.getStrModuleId(moduleId);\n\t\t\tlet paths = this._config.moduleIdToPaths(strModuleId);\n\t\t\tlet scopedPackageRegex = /^@[^\\/]+\\/[^\\/]+$/; // matches @scope/package-name\n\t\t\tif (this._env.isNode && (strModuleId.indexOf('/') === -1 || scopedPackageRegex.test(strModuleId))) {\n\t\t\t\tpaths.push('node|' + strModuleId);\n\t\t\t}\n\t\t\tlet lastPathIndex = -1;\n\t\t\tlet loadNextPath = (err) => {\n\t\t\t\tlastPathIndex++;\n\t\t\t\tif (lastPathIndex >= paths.length) {\n\t\t\t\t\t// No more paths to try\n\t\t\t\t\tthis._onLoadError(moduleId, err);\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\tlet currentPath = paths[lastPathIndex];\n\t\t\t\t\tlet recorder = this.getRecorder();\n\t\t\t\t\tif (this._config.isBuild() && currentPath === 'empty:') {\n\t\t\t\t\t\tthis._buildInfoPath[moduleId] = currentPath;\n\t\t\t\t\t\tthis.defineModule(this._moduleIdProvider.getStrModuleId(moduleId), [], null, null, null);\n\t\t\t\t\t\tthis._onLoad(moduleId);\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t\trecorder.record(10 /* LoaderEventType.BeginLoadingScript */, currentPath);\n\t\t\t\t\tthis._scriptLoader.load(this, currentPath, () => {\n\t\t\t\t\t\tif (this._config.isBuild()) {\n\t\t\t\t\t\t\tthis._buildInfoPath[moduleId] = currentPath;\n\t\t\t\t\t\t}\n\t\t\t\t\t\trecorder.record(11 /* LoaderEventType.EndLoadingScriptOK */, currentPath);\n\t\t\t\t\t\tthis._onLoad(moduleId);\n\t\t\t\t\t}, (err) => {\n\t\t\t\t\t\trecorder.record(12 /* LoaderEventType.EndLoadingScriptError */, currentPath);\n\t\t\t\t\t\tloadNextPath(err);\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t};\n\t\t\tloadNextPath(null);\n\t\t}\n\t\t/**\n\t\t * Resolve a plugin dependency with the plugin loaded & complete\n\t\t * @param module The module that has this dependency\n\t\t * @param pluginDependency The semi-normalized dependency that appears in the module. e.g. 'vs/css!./mycssfile'. Only the plugin part (before !) is normalized\n\t\t * @param plugin The plugin (what the plugin exports)\n\t\t */\n\t\t_loadPluginDependency(plugin, pluginDependency) {\n\t\t\tif (this._modules2[pluginDependency.id] || this._knownModules2[pluginDependency.id]) {\n\t\t\t\t// known module\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tthis._knownModules2[pluginDependency.id] = true;\n\t\t\t// Delegate the loading of the resource to the plugin\n\t\t\tlet load = ((value) => {\n\t\t\t\tthis.defineModule(this._moduleIdProvider.getStrModuleId(pluginDependency.id), [], value, null, null);\n\t\t\t});\n\t\t\tload.error = (err) => {\n\t\t\t\tthis._config.onError(this._createLoadError(pluginDependency.id, err));\n\t\t\t};\n\t\t\tplugin.load(pluginDependency.pluginParam, this._createRequire(ModuleIdResolver.ROOT), load, this._config.getOptionsLiteral());\n\t\t}\n\t\t/**\n\t\t * Examine the dependencies of module 'module' and resolve them as needed.\n\t\t */\n\t\t_resolve(module) {\n\t\t\tlet dependencies = module.dependencies;\n\t\t\tif (dependencies) {\n\t\t\t\tfor (let i = 0, len = dependencies.length; i < len; i++) {\n\t\t\t\t\tlet dependency = dependencies[i];\n\t\t\t\t\tif (dependency === RegularDependency.EXPORTS) {\n\t\t\t\t\t\tmodule.exportsPassedIn = true;\n\t\t\t\t\t\tmodule.unresolvedDependenciesCount--;\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\t\t\t\t\tif (dependency === RegularDependency.MODULE) {\n\t\t\t\t\t\tmodule.unresolvedDependenciesCount--;\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\t\t\t\t\tif (dependency === RegularDependency.REQUIRE) {\n\t\t\t\t\t\tmodule.unresolvedDependenciesCount--;\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\t\t\t\t\tlet dependencyModule = this._modules2[dependency.id];\n\t\t\t\t\tif (dependencyModule && dependencyModule.isComplete()) {\n\t\t\t\t\t\tif (dependencyModule.error) {\n\t\t\t\t\t\t\tmodule.onDependencyError(dependencyModule.error);\n\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tmodule.unresolvedDependenciesCount--;\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\t\t\t\t\tif (this._hasDependencyPath(dependency.id, module.id)) {\n\t\t\t\t\t\tthis._hasDependencyCycle = true;\n\t\t\t\t\t\tconsole.warn('There is a dependency cycle between \\'' + this._moduleIdProvider.getStrModuleId(dependency.id) + '\\' and \\'' + this._moduleIdProvider.getStrModuleId(module.id) + '\\'. The cyclic path follows:');\n\t\t\t\t\t\tlet cyclePath = this._findCyclePath(dependency.id, module.id, 0) || [];\n\t\t\t\t\t\tcyclePath.reverse();\n\t\t\t\t\t\tcyclePath.push(dependency.id);\n\t\t\t\t\t\tconsole.warn(cyclePath.map(id => this._moduleIdProvider.getStrModuleId(id)).join(' => \\n'));\n\t\t\t\t\t\t// Break the cycle\n\t\t\t\t\t\tmodule.unresolvedDependenciesCount--;\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\t\t\t\t\t// record inverse dependency\n\t\t\t\t\tthis._inverseDependencies2[dependency.id] = this._inverseDependencies2[dependency.id] || [];\n\t\t\t\t\tthis._inverseDependencies2[dependency.id].push(module.id);\n\t\t\t\t\tif (dependency instanceof PluginDependency) {\n\t\t\t\t\t\tlet plugin = this._modules2[dependency.pluginId];\n\t\t\t\t\t\tif (plugin && plugin.isComplete()) {\n\t\t\t\t\t\t\tthis._loadPluginDependency(plugin.exports, dependency);\n\t\t\t\t\t\t\tcontinue;\n\t\t\t\t\t\t}\n\t\t\t\t\t\t// Record dependency for when the plugin gets loaded\n\t\t\t\t\t\tlet inversePluginDeps = this._inversePluginDependencies2.get(dependency.pluginId);\n\t\t\t\t\t\tif (!inversePluginDeps) {\n\t\t\t\t\t\t\tinversePluginDeps = [];\n\t\t\t\t\t\t\tthis._inversePluginDependencies2.set(dependency.pluginId, inversePluginDeps);\n\t\t\t\t\t\t}\n\t\t\t\t\t\tinversePluginDeps.push(dependency);\n\t\t\t\t\t\tthis._loadModule(dependency.pluginId);\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\t\t\t\t\tthis._loadModule(dependency.id);\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (module.unresolvedDependenciesCount === 0) {\n\t\t\t\tthis._onModuleComplete(module);\n\t\t\t}\n\t\t}\n\t\t_onModuleComplete(module) {\n\t\t\tlet recorder = this.getRecorder();\n\t\t\tif (module.isComplete()) {\n\t\t\t\t// already done\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tlet dependencies = module.dependencies;\n\t\t\tlet dependenciesValues = [];\n\t\t\tif (dependencies) {\n\t\t\t\tfor (let i = 0, len = dependencies.length; i < len; i++) {\n\t\t\t\t\tlet dependency = dependencies[i];\n\t\t\t\t\tif (dependency === RegularDependency.EXPORTS) {\n\t\t\t\t\t\tdependenciesValues[i] = module.exports;\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\t\t\t\t\tif (dependency === RegularDependency.MODULE) {\n\t\t\t\t\t\tdependenciesValues[i] = {\n\t\t\t\t\t\t\tid: module.strId,\n\t\t\t\t\t\t\tconfig: () => {\n\t\t\t\t\t\t\t\treturn this._config.getConfigForModule(module.strId);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t};\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\t\t\t\t\tif (dependency === RegularDependency.REQUIRE) {\n\t\t\t\t\t\tdependenciesValues[i] = this._createRequire(module.moduleIdResolver);\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\t\t\t\t\tlet dependencyModule = this._modules2[dependency.id];\n\t\t\t\t\tif (dependencyModule) {\n\t\t\t\t\t\tdependenciesValues[i] = dependencyModule.exports;\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\t\t\t\t\tdependenciesValues[i] = null;\n\t\t\t\t}\n\t\t\t}\n\t\t\tconst inversedependenciesProvider = (moduleId) => {\n\t\t\t\treturn (this._inverseDependencies2[moduleId] || []).map((intModuleId) => this._moduleIdProvider.getStrModuleId(intModuleId));\n\t\t\t};\n\t\t\tmodule.complete(recorder, this._config, dependenciesValues, inversedependenciesProvider);\n\t\t\t// Fetch and clear inverse dependencies\n\t\t\tlet inverseDeps = this._inverseDependencies2[module.id];\n\t\t\tthis._inverseDependencies2[module.id] = null;\n\t\t\tif (inverseDeps) {\n\t\t\t\t// Resolve one inverse dependency at a time, always\n\t\t\t\t// on the lookout for a completed module.\n\t\t\t\tfor (let i = 0, len = inverseDeps.length; i < len; i++) {\n\t\t\t\t\tlet inverseDependencyId = inverseDeps[i];\n\t\t\t\t\tlet inverseDependency = this._modules2[inverseDependencyId];\n\t\t\t\t\tinverseDependency.unresolvedDependenciesCount--;\n\t\t\t\t\tif (inverseDependency.unresolvedDependenciesCount === 0) {\n\t\t\t\t\t\tthis._onModuleComplete(inverseDependency);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\tlet inversePluginDeps = this._inversePluginDependencies2.get(module.id);\n\t\t\tif (inversePluginDeps) {\n\t\t\t\t// This module is used as a plugin at least once\n\t\t\t\t// Fetch and clear these inverse plugin dependencies\n\t\t\t\tthis._inversePluginDependencies2.delete(module.id);\n\t\t\t\t// Resolve plugin dependencies one at a time\n\t\t\t\tfor (let i = 0, len = inversePluginDeps.length; i < len; i++) {\n\t\t\t\t\tthis._loadPluginDependency(module.exports, inversePluginDeps[i]);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\tAMDLoader.ModuleManager = ModuleManager;\n})(AMDLoader || (AMDLoader = {}));\nvar define;\nvar AMDLoader;\n(function (AMDLoader) {\n\tconst env = new AMDLoader.Environment();\n\tlet moduleManager = null;\n\tconst DefineFunc = function (id, dependencies, callback) {\n\t\tif (typeof id !== 'string') {\n\t\t\tcallback = dependencies;\n\t\t\tdependencies = id;\n\t\t\tid = null;\n\t\t}\n\t\tif (typeof dependencies !== 'object' || !Array.isArray(dependencies)) {\n\t\t\tcallback = dependencies;\n\t\t\tdependencies = null;\n\t\t}\n\t\tif (!dependencies) {\n\t\t\tdependencies = ['require', 'exports', 'module'];\n\t\t}\n\t\tif (id) {\n\t\t\tmoduleManager.defineModule(id, dependencies, callback, null, null);\n\t\t}\n\t\telse {\n\t\t\tmoduleManager.enqueueDefineAnonymousModule(dependencies, callback);\n\t\t}\n\t};\n\tDefineFunc.amd = {\n\t\tjQuery: true\n\t};\n\tconst _requireFunc_config = function (params, shouldOverwrite = false) {\n\t\tmoduleManager.configure(params, shouldOverwrite);\n\t};\n\tconst RequireFunc = function () {\n\t\tif (arguments.length === 1) {\n\t\t\tif ((arguments[0] instanceof Object) && !Array.isArray(arguments[0])) {\n\t\t\t\t_requireFunc_config(arguments[0]);\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tif (typeof arguments[0] === 'string') {\n\t\t\t\treturn moduleManager.synchronousRequire(arguments[0]);\n\t\t\t}\n\t\t}\n\t\tif (arguments.length === 2 || arguments.length === 3) {\n\t\t\tif (Array.isArray(arguments[0])) {\n\t\t\t\tmoduleManager.defineModule(AMDLoader.Utilities.generateAnonymousModule(), arguments[0], arguments[1], arguments[2], null);\n\t\t\t\treturn;\n\t\t\t}\n\t\t}\n\t\tthrow new Error('Unrecognized require call');\n\t};\n\tRequireFunc.config = _requireFunc_config;\n\tRequireFunc.getConfig = function () {\n\t\treturn moduleManager.getConfig().getOptionsLiteral();\n\t};\n\tRequireFunc.reset = function () {\n\t\tmoduleManager = moduleManager.reset();\n\t};\n\tRequireFunc.getBuildInfo = function () {\n\t\treturn moduleManager.getBuildInfo();\n\t};\n\tRequireFunc.getStats = function () {\n\t\treturn moduleManager.getLoaderEvents();\n\t};\n\tRequireFunc.define = DefineFunc;\n\tfunction init() {\n\t\tif (typeof AMDLoader.global.require !== 'undefined' || typeof require !== 'undefined') {\n\t\t\tconst _nodeRequire = (AMDLoader.global.require || require);\n\t\t\tif (typeof _nodeRequire === 'function' && typeof _nodeRequire.resolve === 'function') {\n\t\t\t\t// re-expose node's require function\n\t\t\t\tconst nodeRequire = AMDLoader.ensureRecordedNodeRequire(moduleManager.getRecorder(), _nodeRequire);\n\t\t\t\tAMDLoader.global.nodeRequire = nodeRequire;\n\t\t\t\tRequireFunc.nodeRequire = nodeRequire;\n\t\t\t\tRequireFunc.__$__nodeRequire = nodeRequire;\n\t\t\t}\n\t\t}\n\t\tif (env.isNode && !env.isElectronRenderer && !env.isElectronNodeIntegrationWebWorker) {\n\t\t\tmodule.exports = RequireFunc;\n\t\t}\n\t\telse {\n\t\t\tif (!env.isElectronRenderer) {\n\t\t\t\tAMDLoader.global.define = DefineFunc;\n\t\t\t}\n\t\t\tAMDLoader.global.require = RequireFunc;\n\t\t}\n\t}\n\tAMDLoader.init = init;\n\tif (typeof AMDLoader.global.define !== 'function' || !AMDLoader.global.define.amd) {\n\t\tmoduleManager = new AMDLoader.ModuleManager(env, AMDLoader.createScriptLoader(env), DefineFunc, RequireFunc, AMDLoader.Utilities.getHighPerformanceTimestamp());\n\t\t// The global variable require can configure the loader\n\t\tif (typeof AMDLoader.global.require !== 'undefined' && typeof AMDLoader.global.require !== 'function') {\n\t\t\tRequireFunc.config(AMDLoader.global.require);\n\t\t}\n\t\t// This define is for the local closure defined in node in the case that the loader is concatenated\n\t\tdefine = function () {\n\t\t\treturn DefineFunc.apply(null, arguments);\n\t\t};\n\t\tdefine.amd = DefineFunc.amd;\n\t\tif (typeof doNotInitLoader === 'undefined') {\n\t\t\tinit();\n\t\t}\n\t}\n})(AMDLoader || (AMDLoader = {}));\n"], "mappings": "aAAA;AAAA;AAAA;AAAA;AAAA;AAAA,+DA2BA,MAAM,iBAAmB,KACnB,gBAAkB,OAAO,QAAW,SAAW,OAAS,CAAC,EAC/D,IAAI,WACH,SAAUA,EAAW,CACrBA,EAAU,OAAS,iBACnB,MAAMC,CAAY,CACjB,IAAI,WAAY,CACf,YAAK,QAAQ,EACN,KAAK,UACb,CACA,IAAI,QAAS,CACZ,YAAK,QAAQ,EACN,KAAK,OACb,CACA,IAAI,oBAAqB,CACxB,YAAK,QAAQ,EACN,KAAK,mBACb,CACA,IAAI,aAAc,CACjB,YAAK,QAAQ,EACN,KAAK,YACb,CACA,IAAI,oCAAqC,CACxC,YAAK,QAAQ,EACN,KAAK,mCACb,CACA,aAAc,CACb,KAAK,UAAY,GACjB,KAAK,WAAa,GAClB,KAAK,QAAU,GACf,KAAK,oBAAsB,GAC3B,KAAK,aAAe,GACpB,KAAK,oCAAsC,EAC5C,CACA,SAAU,CACL,KAAK,YAGT,KAAK,UAAY,GACjB,KAAK,WAAaA,EAAY,WAAW,EACzC,KAAK,QAAW,OAAO,OAAW,KAAe,CAAC,CAAC,OAAO,QAC1D,KAAK,oBAAuB,OAAO,QAAY,KAAe,OAAO,QAAQ,SAAa,KAAe,OAAO,QAAQ,SAAS,SAAa,KAAe,QAAQ,OAAS,WAC9K,KAAK,aAAgB,OAAOD,EAAU,OAAO,eAAkB,WAC/D,KAAK,oCAAsC,KAAK,cAAiB,OAAO,QAAY,KAAe,OAAO,QAAQ,SAAa,KAAe,OAAO,QAAQ,SAAS,SAAa,KAAe,QAAQ,OAAS,SACpN,CACA,OAAO,YAAa,CACnB,OAAI,OAAO,UAAc,KACpB,UAAU,WAAa,UAAU,UAAU,QAAQ,SAAS,GAAK,EAC7D,GAGL,OAAO,QAAY,IACd,QAAQ,WAAa,QAEvB,EACR,CACD,CACAA,EAAU,YAAcC,CACzB,GAAG,YAAc,UAAY,CAAC,EAAE,EAKhC,IAAI,WACH,SAAUD,EAAW,CACrB,MAAME,CAAY,CACjB,YAAYC,EAAMC,EAAQC,EAAW,CACpC,KAAK,KAAOF,EACZ,KAAK,OAASC,EACd,KAAK,UAAYC,CAClB,CACD,CACAL,EAAU,YAAcE,EACxB,MAAMI,CAAoB,CACzB,YAAYC,EAA0B,CACrC,KAAK,QAAU,CAAC,IAAIL,EAAY,EAAyC,GAAIK,CAAwB,CAAC,CACvG,CACA,OAAOJ,EAAMC,EAAQ,CACpB,KAAK,QAAQ,KAAK,IAAIF,EAAYC,EAAMC,EAAQJ,EAAU,UAAU,4BAA4B,CAAC,CAAC,CACnG,CACA,WAAY,CACX,OAAO,KAAK,OACb,CACD,CACAA,EAAU,oBAAsBM,EAChC,MAAME,CAAwB,CAC7B,OAAOL,EAAMC,EAAQ,CAErB,CACA,WAAY,CACX,MAAO,CAAC,CACT,CACD,CACAI,EAAwB,SAAW,IAAIA,EACvCR,EAAU,wBAA0BQ,CACrC,GAAG,YAAc,UAAY,CAAC,EAAE,EAKhC,IAAI,WACH,SAAUR,EAAW,CACrB,MAAMS,CAAU,CAIf,OAAO,kBAAkBC,EAAWC,EAAK,CAExC,GADAA,EAAM,UAAUA,CAAG,EAAE,QAAQ,OAAQ,GAAG,EACpCD,EAAW,CACd,GAAI,eAAe,KAAKC,CAAG,EAE1B,OAAOA,EAAI,OAAO,CAAC,EAEpB,GAAI,aAAa,KAAKA,CAAG,EACxB,OAAOA,EAAI,OAAO,CAAC,CAErB,SAEK,aAAa,KAAKA,CAAG,EACxB,OAAOA,EAAI,OAAO,CAAC,EAIrB,OAAOA,CACR,CACA,OAAO,WAAWC,EAAUC,EAAQ,CACnC,OAAOD,EAAS,QAAUC,EAAO,QAAUD,EAAS,OAAO,EAAGC,EAAO,MAAM,IAAMA,CAClF,CACA,OAAO,SAASD,EAAUC,EAAQ,CACjC,OAAOD,EAAS,QAAUC,EAAO,QAAUD,EAAS,OAAOA,EAAS,OAASC,EAAO,MAAM,IAAMA,CACjG,CAEA,OAAO,oBAAoBC,EAAK,CAC/B,MAAO,cAAc,KAAKA,CAAG,CAC9B,CAIA,OAAO,eAAeA,EAAK,CAC1B,MAAO,+CAA+C,KAAKA,CAAG,CAC/D,CACA,OAAO,gBAAgBC,EAAKC,EAAU,CACrC,GAAID,EAAK,CACR,IAAIE,EACJ,IAAKA,KAAOF,EACPA,EAAI,eAAeE,CAAG,GACzBD,EAASC,EAAKF,EAAIE,CAAG,CAAC,CAGzB,CACD,CACA,OAAO,QAAQF,EAAK,CACnB,IAAIG,EAAU,GACd,OAAAT,EAAU,gBAAgBM,EAAK,IAAM,CACpCG,EAAU,EACX,CAAC,EACMA,CACR,CACA,OAAO,eAAeH,EAAK,CAI1B,GAHI,CAACA,GAAO,OAAOA,GAAQ,UAAYA,aAAe,QAGlD,CAAC,MAAM,QAAQA,CAAG,GAAK,OAAO,eAAeA,CAAG,IAAM,OAAO,UAEhE,OAAOA,EAER,IAAII,EAAS,MAAM,QAAQJ,CAAG,EAAI,CAAC,EAAI,CAAC,EACxC,OAAAN,EAAU,gBAAgBM,EAAK,CAACE,EAAKG,IAAU,CAC1CA,GAAS,OAAOA,GAAU,SAC7BD,EAAOF,CAAG,EAAIR,EAAU,eAAeW,CAAK,EAG5CD,EAAOF,CAAG,EAAIG,CAEhB,CAAC,EACMD,CACR,CACA,OAAO,yBAA0B,CAChC,MAAO,eAAkBV,EAAU,oBAAuB,KAC3D,CACA,OAAO,kBAAkBY,EAAI,CAC5B,OAAOZ,EAAU,WAAWY,EAAI,cAAc,CAC/C,CACA,OAAO,6BAA8B,CACpC,OAAK,KAAK,yBACT,KAAK,uBAAyB,GAC9B,KAAK,oBAAuBrB,EAAU,OAAO,aAAe,OAAOA,EAAU,OAAO,YAAY,KAAQ,YAEjG,KAAK,oBAAsBA,EAAU,OAAO,YAAY,IAAI,EAAI,KAAK,IAAI,CAClF,CACD,CACAS,EAAU,kBAAoB,EAC9BA,EAAU,uBAAyB,GACnCA,EAAU,oBAAsB,GAChCT,EAAU,UAAYS,CACvB,GAAG,YAAc,UAAY,CAAC,EAAE,EAKhC,IAAI,WACH,SAAUT,EAAW,CACrB,SAASsB,EAAYC,EAAK,CACzB,GAAIA,aAAe,MAClB,OAAOA,EAER,MAAMJ,EAAS,IAAI,MAAMI,EAAI,SAAW,OAAOA,CAAG,GAAK,eAAe,EACtE,OAAIA,EAAI,QACPJ,EAAO,MAAQI,EAAI,OAEbJ,CACR,CACAnB,EAAU,YAAcsB,EAExB,MAAME,CAAyB,CAI9B,OAAO,6BAA6BC,EAAS,CAC5C,SAASC,EAAeH,EAAK,CAC5B,GAAIA,EAAI,QAAU,UAAW,CAC5B,QAAQ,MAAM,YAAcA,EAAI,SAAW,UAAU,EACrD,QAAQ,MAAMA,CAAG,EACjB,QAAQ,MAAM,yCAAyC,EACvD,QAAQ,MAAMA,EAAI,QAAQ,EAC1B,MACD,CACA,GAAIA,EAAI,QAAU,UAAW,CAC5B,QAAQ,MAAM,4BAA8BA,EAAI,SAAW,2BAA2B,EACtF,QAAQ,MAAMA,CAAG,EACjB,QAAQ,MAAM,yCAAyC,EACvD,QAAQ,MAAMA,EAAI,QAAQ,EAC1B,MACD,CACD,CAwCA,GAvCAE,EAAUA,GAAW,CAAC,EAClB,OAAOA,EAAQ,SAAY,WAC9BA,EAAQ,QAAU,IAEf,OAAOA,EAAQ,SAAY,YAC9BA,EAAQ,QAAU,IAEf,OAAOA,EAAQ,OAAU,WAC5BA,EAAQ,MAAQ,CAAC,GAEd,OAAOA,EAAQ,QAAW,WAC7BA,EAAQ,OAAS,CAAC,GAEf,OAAOA,EAAQ,WAAe,MACjCA,EAAQ,WAAa,IAElB,OAAOA,EAAQ,YAAgB,MAClCA,EAAQ,YAAc,IAEnB,OAAOA,EAAQ,SAAY,WAC9BA,EAAQ,QAAU,IAEf,OAAOA,EAAQ,SAAY,aAC9BA,EAAQ,QAAUC,GAEd,MAAM,QAAQD,EAAQ,sBAAsB,IAChDA,EAAQ,uBAAyB,CAAC,GAE/BA,EAAQ,QAAQ,OAAS,IACvBzB,EAAU,UAAU,SAASyB,EAAQ,QAAS,GAAG,IACrDA,EAAQ,SAAW,MAGjB,OAAOA,EAAQ,UAAa,WAC/BA,EAAQ,SAAW,IAEhB,OAAOA,EAAQ,iBAAqB,MACvCA,EAAQ,iBAAmB,IAExBA,EAAQ,gBAAkB,OAAOA,EAAQ,gBAAmB,WAC3D,OAAOA,EAAQ,eAAe,MAAS,WAC1CA,EAAQ,eAAe,KAAO,SAE3B,OAAOA,EAAQ,eAAe,YAAe,UAAYA,EAAQ,eAAe,WAAa,KAChGA,EAAQ,eAAe,WAAa,IAAO,GAExC,CAACA,EAAQ,eAAe,MAAQ,OAAOA,EAAQ,eAAe,MAAS,UAAU,CACpF,MAAMF,EAAMD,EAAY,IAAI,MAAM,uDAAyD,CAAC,EAC5FC,EAAI,MAAQ,gBACZE,EAAQ,QAAQF,CAAG,EACnBE,EAAQ,eAAiB,MAC1B,CAED,OAAOA,CACR,CACA,OAAO,0BAA0BE,EAAY,KAAMC,EAAO,KAAM,CAC/D,IAAIT,EAASnB,EAAU,UAAU,eAAe4B,GAAQ,CAAC,CAAC,EAE1D,OAAA5B,EAAU,UAAU,gBAAgB2B,EAAW,CAACV,EAAKG,IAAU,CAC1DH,IAAQ,0BAA4B,OAAOE,EAAO,uBAA2B,IAChFA,EAAO,uBAAyBA,EAAO,uBAAuB,OAAOC,CAAK,EAElEH,IAAQ,SAAW,OAAOE,EAAO,MAAU,IACnDnB,EAAU,UAAU,gBAAgBoB,EAAO,CAACS,EAAMC,IAAWX,EAAO,MAAMU,CAAI,EAAIC,CAAM,EAEhFb,IAAQ,UAAY,OAAOE,EAAO,OAAW,IACrDnB,EAAU,UAAU,gBAAgBoB,EAAO,CAACS,EAAMC,IAAWX,EAAO,OAAOU,CAAI,EAAIC,CAAM,EAGzFX,EAAOF,CAAG,EAAIjB,EAAU,UAAU,eAAeoB,CAAK,CAExD,CAAC,EACMI,EAAyB,6BAA6BL,CAAM,CACpE,CACD,CACAnB,EAAU,yBAA2BwB,EACrC,MAAMO,CAAc,CACnB,YAAYC,EAAKP,EAAS,CAKzB,GAJA,KAAK,KAAOO,EACZ,KAAK,QAAUR,EAAyB,0BAA0BC,CAAO,EACzE,KAAK,iCAAiC,EACtC,KAAK,wBAAwB,EACzB,KAAK,QAAQ,UAAY,IACxB,KAAK,QAAQ,aAAe,KAAK,QAAQ,YAAY,MAAQ,KAAK,QAAQ,YAAY,KAAK,UAAY,KAAK,KAAK,OAAQ,CAC5H,IAAIQ,EAAW,KAAK,QAAQ,YAAY,KAAK,SACzCC,EAAe,KAAK,IAAID,EAAS,YAAY,GAAG,EAAGA,EAAS,YAAY,IAAI,CAAC,EACjF,KAAK,QAAQ,QAAUA,EAAS,UAAU,EAAGC,EAAe,CAAC,CAC9D,CAEF,CACA,kCAAmC,CAElC,KAAK,0BAA4B,CAAC,EAClC,QAASC,EAAI,EAAGA,EAAI,KAAK,QAAQ,uBAAuB,OAAQA,IAC/D,KAAK,0BAA0B,KAAK,QAAQ,uBAAuBA,CAAC,CAAC,EAAI,EAE3E,CACA,yBAA0B,CAGzB,KAAK,iBAAmB,CAAC,EACzBnC,EAAU,UAAU,gBAAgB,KAAK,QAAQ,MAAO,CAACoC,EAAMC,IAAO,CAChE,MAAM,QAAQA,CAAE,EAOpB,KAAK,iBAAiB,KAAK,CAC1B,KAAMD,EACN,GAAIC,CACL,CAAC,EATD,KAAK,iBAAiB,KAAK,CAC1B,KAAMD,EACN,GAAI,CAACC,CAAE,CACR,CAAC,CAQH,CAAC,EACD,KAAK,iBAAiB,KAAK,CAACC,EAAGC,IACvBA,EAAE,KAAK,OAASD,EAAE,KAAK,MAC9B,CACF,CAMA,cAAcb,EAAS,CACtB,OAAO,IAAIM,EAAc,KAAK,KAAMP,EAAyB,0BAA0BC,EAAS,KAAK,OAAO,CAAC,CAC9G,CAIA,mBAAoB,CACnB,OAAO,KAAK,OACb,CACA,YAAYe,EAAU,CACrB,IAAIC,EACJ,QAASN,EAAI,EAAGO,EAAM,KAAK,iBAAiB,OAAQP,EAAIO,EAAKP,IAE5D,GADAM,EAAW,KAAK,iBAAiBN,CAAC,EAC9BnC,EAAU,UAAU,WAAWwC,EAAUC,EAAS,IAAI,EAAG,CAC5D,IAAItB,EAAS,CAAC,EACd,QAASwB,EAAI,EAAGC,EAAOH,EAAS,GAAG,OAAQE,EAAIC,EAAMD,IACpDxB,EAAO,KAAKsB,EAAS,GAAGE,CAAC,EAAIH,EAAS,OAAOC,EAAS,KAAK,MAAM,CAAC,EAEnE,OAAOtB,CACR,CAED,MAAO,CAACqB,CAAQ,CACjB,CACA,iBAAiB1B,EAAK,CACrB,OAAId,EAAU,UAAU,oBAAoBc,CAAG,EACvCA,EAAM,IAAM,KAAK,QAAQ,QAGzBA,EAAM,IAAM,KAAK,QAAQ,OAElC,CACA,4BAA4BA,EAAK,CAChC,OAAI,KAAK,QAAQ,QACT,KAAK,iBAAiBA,CAAG,EAE1BA,CACR,CACA,6BAA6B+B,EAAM,CAClC,GAAI,KAAK,QAAQ,QAChB,QAASV,EAAI,EAAGO,EAAMG,EAAK,OAAQV,EAAIO,EAAKP,IAC3CU,EAAKV,CAAC,EAAI,KAAK,iBAAiBU,EAAKV,CAAC,CAAC,EAGzC,OAAOU,CACR,CAIA,gBAAgBL,EAAU,CACzB,GAAI,KAAK,KAAK,QACS,KAAK,QAAQ,6BAA6B,QAC5D,CAAC,KAAK,QAAQ,kBAAkB,KAAKA,CAAQ,EAGhD,OAAI,KAAK,QAAQ,EAET,CAAC,QAAQ,EAIT,CAAC,QAAUA,CAAQ,EAI7B,IAAIrB,EAASqB,EACTM,EACJ,GAAI,CAAC9C,EAAU,UAAU,SAASmB,EAAQ,KAAK,GAAK,CAACnB,EAAU,UAAU,eAAemB,CAAM,EAAG,CAChG2B,EAAU,KAAK,YAAY3B,CAAM,EACjC,QAASgB,EAAI,EAAGO,EAAMI,EAAQ,OAAQX,EAAIO,EAAKP,IAC1C,KAAK,QAAQ,GAAKW,EAAQX,CAAC,IAAM,WAGhCnC,EAAU,UAAU,eAAe8C,EAAQX,CAAC,CAAC,IACjDW,EAAQX,CAAC,EAAI,KAAK,QAAQ,QAAUW,EAAQX,CAAC,GAE1C,CAACnC,EAAU,UAAU,SAAS8C,EAAQX,CAAC,EAAG,KAAK,GAAK,CAACnC,EAAU,UAAU,oBAAoB8C,EAAQX,CAAC,CAAC,IAC1GW,EAAQX,CAAC,EAAIW,EAAQX,CAAC,EAAI,OAG7B,KAEK,CAACnC,EAAU,UAAU,SAASmB,EAAQ,KAAK,GAAK,CAACnB,EAAU,UAAU,oBAAoBmB,CAAM,IAClGA,EAASA,EAAS,OAEnB2B,EAAU,CAAC3B,CAAM,EAElB,OAAO,KAAK,6BAA6B2B,CAAO,CACjD,CAIA,aAAahC,EAAK,CACjB,IAAIK,EAASL,EACb,OAAKd,EAAU,UAAU,eAAemB,CAAM,IAC7CA,EAAS,KAAK,YAAYA,CAAM,EAAE,CAAC,EAC9BnB,EAAU,UAAU,eAAemB,CAAM,IAC7CA,EAAS,KAAK,QAAQ,QAAUA,IAG3B,KAAK,4BAA4BA,CAAM,CAC/C,CAIA,SAAU,CACT,OAAO,KAAK,QAAQ,OACrB,CACA,oBAAoB4B,EAAa,CAShC,MARI,IAAC,KAAK,QAAQ,SAKd/C,EAAU,UAAU,kBAAkB+C,CAAW,GAGjD,KAAK,QAAQ,yBAA2B,KAAK,QAAQ,wBAAwBA,CAAW,EAI7F,CAIA,6BAA6BP,EAAU,CACtC,OAAO,KAAK,0BAA0B,eAAeA,CAAQ,CAC9D,CAIA,mBAAmBA,EAAU,CAC5B,GAAI,KAAK,QAAQ,OAChB,OAAO,KAAK,QAAQ,OAAOA,CAAQ,CAErC,CAIA,kBAAmB,CAClB,OAAO,KAAK,QAAQ,UACrB,CAIA,mBAAoB,CACnB,OAAO,KAAK,QAAQ,WACrB,CAIA,QAAQjB,EAAK,CACZ,KAAK,QAAQ,QAAQA,CAAG,CACzB,CACD,CACAvB,EAAU,cAAgB+B,CAC3B,GAAG,YAAc,UAAY,CAAC,EAAE,EAKhC,IAAI,WACH,SAAU/B,EAAW,CAIrB,MAAMgD,CAAqB,CAC1B,YAAYhB,EAAK,CAChB,KAAK,KAAOA,EACZ,KAAK,cAAgB,KACrB,KAAK,aAAe,CAAC,CACtB,CACA,KAAKiB,EAAeC,EAAWlC,EAAUmC,EAAW,CACnD,GAAI,CAAC,KAAK,cACT,GAAI,KAAK,KAAK,YACb,KAAK,cAAgB,IAAIC,UAEjB,KAAK,KAAK,mBAAoB,CACtC,KAAM,CAAE,iBAAAC,CAAiB,EAAIJ,EAAc,UAAU,EAAE,kBAAkB,EACrEI,EACH,KAAK,cAAgB,IAAIC,EAGzB,KAAK,cAAgB,IAAIC,EAAiB,KAAK,IAAI,CAErD,MACS,KAAK,KAAK,OAClB,KAAK,cAAgB,IAAIA,EAAiB,KAAK,IAAI,EAGnD,KAAK,cAAgB,IAAID,EAG3B,IAAIE,EAAkB,CACrB,SAAUxC,EACV,UAAWmC,CACZ,EACA,GAAI,KAAK,aAAa,eAAeD,CAAS,EAAG,CAChD,KAAK,aAAaA,CAAS,EAAE,KAAKM,CAAe,EACjD,MACD,CACA,KAAK,aAAaN,CAAS,EAAI,CAACM,CAAe,EAC/C,KAAK,cAAc,KAAKP,EAAeC,EAAW,IAAM,KAAK,gBAAgBA,CAAS,EAAI3B,GAAQ,KAAK,iBAAiB2B,EAAW3B,CAAG,CAAC,CACxI,CACA,gBAAgB2B,EAAW,CAC1B,IAAIM,EAAkB,KAAK,aAAaN,CAAS,EACjD,OAAO,KAAK,aAAaA,CAAS,EAClC,QAASf,EAAI,EAAGA,EAAIqB,EAAgB,OAAQrB,IAC3CqB,EAAgBrB,CAAC,EAAE,SAAS,CAE9B,CACA,iBAAiBe,EAAW3B,EAAK,CAChC,IAAIiC,EAAkB,KAAK,aAAaN,CAAS,EACjD,OAAO,KAAK,aAAaA,CAAS,EAClC,QAASf,EAAI,EAAGA,EAAIqB,EAAgB,OAAQrB,IAC3CqB,EAAgBrB,CAAC,EAAE,UAAUZ,CAAG,CAElC,CACD,CACA,MAAM+B,CAAoB,CAKzB,gBAAgBG,EAAQzC,EAAUmC,EAAW,CAC5C,IAAIO,EAAS,IAAM,CAClBD,EAAO,oBAAoB,OAAQE,CAAiB,EACpDF,EAAO,oBAAoB,QAASG,CAAkB,CACvD,EACID,EAAqBE,GAAM,CAC9BH,EAAO,EACP1C,EAAS,CACV,EACI4C,EAAsBC,GAAM,CAC/BH,EAAO,EACPP,EAAUU,CAAC,CACZ,EACAJ,EAAO,iBAAiB,OAAQE,CAAiB,EACjDF,EAAO,iBAAiB,QAASG,CAAkB,CACpD,CACA,KAAKX,EAAeC,EAAWlC,EAAUmC,EAAW,CACnD,GAAI,UAAU,KAAKD,CAAS,EAAG,CAC9B,IAAIY,EAAOb,EAAc,UAAU,EAAE,kBAAkB,EACnDc,EAAcC,EAA0Bf,EAAc,YAAY,EAAIa,EAAK,aAAe9D,EAAU,OAAO,WAAY,EACvHiE,EAASf,EAAU,MAAM,GAAG,EAC5BgB,EAAgB,KACpB,GAAI,CACHA,EAAgBH,EAAYE,EAAO,CAAC,CAAC,CACtC,OACO1C,EAAK,CACX4B,EAAU5B,CAAG,EACb,MACD,CACA0B,EAAc,6BAA6B,CAAC,EAAG,IAAMiB,CAAa,EAClElD,EAAS,CACV,KACK,CACJ,IAAIyC,EAAS,SAAS,cAAc,QAAQ,EAC5CA,EAAO,aAAa,QAAS,OAAO,EACpCA,EAAO,aAAa,OAAQ,iBAAiB,EAC7C,KAAK,gBAAgBA,EAAQzC,EAAUmC,CAAS,EAChD,KAAM,CAAE,mBAAAgB,CAAmB,EAAIlB,EAAc,UAAU,EAAE,kBAAkB,EACvEkB,IACHjB,EAAYiB,EAAmB,gBAAgBjB,CAAS,GAEzDO,EAAO,aAAa,MAAOP,CAAS,EAEpC,KAAM,CAAE,SAAAkB,CAAS,EAAInB,EAAc,UAAU,EAAE,kBAAkB,EAC7DmB,GACHX,EAAO,aAAa,QAASW,CAAQ,EAEtC,SAAS,qBAAqB,MAAM,EAAE,CAAC,EAAE,YAAYX,CAAM,CAC5D,CACD,CACD,CACA,SAASY,EAAWpB,EAAe,CAClC,KAAM,CAAE,mBAAAkB,CAAmB,EAAIlB,EAAc,UAAU,EAAE,kBAAkB,EAC3E,GAAI,CAKH,OAJckB,EACX,KAAK,KAAKA,EAAmB,aAAa,GAAI,MAAM,CAAC,EACrD,IAAI,SAAS,MAAM,GAEjB,KAAK,IAAI,EACP,EACR,MACY,CACX,MAAO,EACR,CACD,CACA,MAAMf,CAAmB,CACxB,aAAc,CACb,KAAK,kBAAoB,IAC1B,CACA,YAAYH,EAAe,CAC1B,OAAI,KAAK,oBAAsB,OAC9B,KAAK,kBAAoBoB,EAAWpB,CAAa,GAE3C,KAAK,iBACb,CACA,KAAKA,EAAeC,EAAWlC,EAAUmC,EAAW,CACnD,GAAI,UAAU,KAAKD,CAAS,EAAG,CAC9B,MAAMY,EAAOb,EAAc,UAAU,EAAE,kBAAkB,EACnDc,EAAcC,EAA0Bf,EAAc,YAAY,EAAIa,EAAK,aAAe9D,EAAU,OAAO,WAAY,EACvHiE,EAASf,EAAU,MAAM,GAAG,EAClC,IAAIgB,EAAgB,KACpB,GAAI,CACHA,EAAgBH,EAAYE,EAAO,CAAC,CAAC,CACtC,OACO1C,EAAK,CACX4B,EAAU5B,CAAG,EACb,MACD,CACA0B,EAAc,6BAA6B,CAAC,EAAG,UAAY,CAAE,OAAOiB,CAAe,CAAC,EACpFlD,EAAS,CACV,KACK,CACJ,KAAM,CAAE,mBAAAmD,CAAmB,EAAIlB,EAAc,UAAU,EAAE,kBAAkB,EAE3E,GAAI,EADmB,8BAA8B,KAAKC,CAAS,GAAKA,EAAU,UAAU,EAAG,KAAK,OAAO,MAAM,IAAM,KAAK,SACtG,KAAK,YAAYD,CAAa,EAAG,CAGtD,MAAMC,CAAS,EAAE,KAAMoB,GAAa,CACnC,GAAIA,EAAS,SAAW,IACvB,MAAM,IAAI,MAAMA,EAAS,UAAU,EAEpC,OAAOA,EAAS,KAAK,CACtB,CAAC,EAAE,KAAMC,GAAS,CACjBA,EAAO,GAAGA,CAAI;AAAA,gBAAmBrB,CAAS,IAC5BiB,EACX,KAAK,KAAKA,EAAmB,aAAa,GAAII,CAAI,CAAC,EACnD,IAAI,SAASA,CAAI,GAEf,KAAK,IAAI,EACdvD,EAAS,CACV,CAAC,EAAE,KAAK,OAAWmC,CAAS,EAC5B,MACD,CACA,GAAI,CACCgB,IACHjB,EAAYiB,EAAmB,gBAAgBjB,CAAS,GAEzD,cAAcA,CAAS,EACvBlC,EAAS,CACV,OACO6C,EAAG,CACTV,EAAUU,CAAC,CACZ,CACD,CACD,CACD,CACA,MAAMN,CAAiB,CACtB,YAAYvB,EAAK,CAChB,KAAK,KAAOA,EACZ,KAAK,eAAiB,GACtB,KAAK,qBAAuB,EAC7B,CACA,MAAM+B,EAAa,CACd,KAAK,iBAGT,KAAK,eAAiB,GAEtB,KAAK,IAAMA,EAAY,IAAI,EAC3B,KAAK,IAAMA,EAAY,IAAI,EAC3B,KAAK,MAAQA,EAAY,MAAM,EAC/B,KAAK,QAAUA,EAAY,QAAQ,EACpC,CAGA,iBAAiBA,EAAad,EAAe,CAG5C,KAAM,CAAE,eAAAuB,CAAe,EAAIvB,EAAc,UAAU,EAAE,kBAAkB,EAIvE,GAHI,CAACuB,GAGD,KAAK,qBACR,OAED,KAAK,qBAAuB,GAC5B,MAAMC,EAAO,KACPC,EAASX,EAAY,QAAQ,EACnC,SAASY,EAAoBC,EAAK,CACjC,MAAMF,EAASE,EAAI,YACnB,IAAIC,EAAU,SAAiBC,EAAM,CACpC,GAAI,CACH,OAAOF,EAAI,QAAQE,CAAI,CACxB,QACA,CAEA,CACD,EACA,OAAAD,EAAQ,QAAU,SAAiBE,EAAStD,EAAS,CACpD,OAAOiD,EAAO,iBAAiBK,EAASH,EAAK,GAAOnD,CAAO,CAC5D,EACAoD,EAAQ,QAAQ,MAAQ,SAAeE,EAAS,CAC/C,OAAOL,EAAO,oBAAoBK,EAASH,CAAG,CAC/C,EACAC,EAAQ,KAAO,QAAQ,WACvBA,EAAQ,WAAaH,EAAO,YAC5BG,EAAQ,MAAQH,EAAO,OAChBG,CACR,CACAH,EAAO,UAAU,SAAW,SAAUM,EAASC,EAAU,CAExD,MAAMC,EAAeR,EAAO,KAAKM,EAAQ,QAAQ,QAAS,EAAE,CAAC,EAEvDG,EAAWlC,EAAc,YAAY,EACrCmC,EAAiBX,EAAK,mBAAmBD,EAAgBS,CAAQ,EACjExD,EAAU,CAAE,SAAAwD,CAAS,EAC3B,IAAII,EACJ,GAAI,CACH,MAAMC,EAAOb,EAAK,IAAI,aAAaW,CAAc,EACjDC,EAAWC,EAAK,MAAM,EAAG,EAAE,EAC3B7D,EAAQ,WAAa6D,EAAK,MAAM,EAAE,EAClCH,EAAS,OAAO,GAA0CC,CAAc,CACzE,MACW,CACVD,EAAS,OAAO,GAA2CC,CAAc,CAC1E,CACA,MAAM3B,EAAS,IAAIgB,EAAK,IAAI,OAAOS,EAAczD,CAAO,EAClD8D,EAAiB9B,EAAO,iBAAiBhC,CAAO,EAEhD+D,EAAUf,EAAK,MAAM,QAAQQ,CAAQ,EACrCJ,EAAUF,EAAoB,IAAI,EAClCc,EAAO,CAAC,KAAK,QAASZ,EAAS,KAAMI,EAAUO,EAAS,QAAS,gBAAiB,MAAM,EACxFrE,EAASoE,EAAe,MAAM,KAAK,QAASE,CAAI,EAEtD,OAAAhB,EAAK,kBAAkBhB,EAAQyB,EAAcE,EAAgB,CAAC3D,EAAQ,WAAYwB,CAAa,EAC/FwB,EAAK,kBAAkBhB,EAAQyB,EAAcE,EAAgBC,EAAUpC,CAAa,EAC7E9B,CACR,CACD,CACA,KAAK8B,EAAeC,EAAWlC,EAAUmC,EAAW,CACnD,MAAMW,EAAOb,EAAc,UAAU,EAAE,kBAAkB,EACnDc,EAAcC,EAA0Bf,EAAc,YAAY,EAAIa,EAAK,aAAe9D,EAAU,OAAO,WAAY,EACvH0F,EAAoB5B,EAAK,kBAAoB,SAAU6B,EAAG,CAAE,OAAOA,CAAG,EAC5E,KAAK,MAAM5B,CAAW,EACtB,KAAK,iBAAiBA,EAAad,CAAa,EAChD,IAAIkC,EAAWlC,EAAc,YAAY,EACzC,GAAI,UAAU,KAAKC,CAAS,EAAG,CAC9B,IAAIe,EAASf,EAAU,MAAM,GAAG,EAC5BgB,EAAgB,KACpB,GAAI,CACHA,EAAgBH,EAAYE,EAAO,CAAC,CAAC,CACtC,OACO1C,EAAK,CACX4B,EAAU5B,CAAG,EACb,MACD,CACA0B,EAAc,6BAA6B,CAAC,EAAG,IAAMiB,CAAa,EAClElD,EAAS,CACV,KACK,CACJkC,EAAYlD,EAAU,UAAU,kBAAkB,KAAK,KAAK,UAAWkD,CAAS,EAChF,MAAM0C,EAAsB,KAAK,MAAM,UAAU1C,CAAS,EACpD2C,EAAoB,KAAK,oCAAoCD,CAAmB,EAChFE,EAAkB,EAAQhC,EAAK,eAC/BsB,EAAiBU,EAAkB,KAAK,mBAAmBhC,EAAK,eAAgBZ,CAAS,EAAI,OACnG,KAAK,yBAAyB0C,EAAqBR,EAAgBD,EAAU,CAAC5D,EAAK+D,EAAMS,EAAYV,IAAa,CACjH,GAAI9D,EAAK,CACR4B,EAAU5B,CAAG,EACb,MACD,CACA,IAAI2D,EACAI,EAAK,WAAW,CAAC,IAAM/B,EAAiB,KAC3C2B,EAAe3B,EAAiB,QAAU+B,EAAK,UAAU,CAAC,EAAI/B,EAAiB,QAG/E2B,EAAe3B,EAAiB,QAAU+B,EAAO/B,EAAiB,QAEnE2B,EAAeQ,EAAiBR,EAAcU,CAAmB,EACjE,MAAMI,EAAa,CAAE,SAAUH,EAAmB,WAAAE,CAAW,EACvDtC,EAAS,KAAK,qBAAqBR,EAAeiC,EAAcc,EAAYhF,EAAUmC,CAAS,EACrG,KAAK,kBAAkBM,EAAQyB,EAAcE,EAAgBU,GAAmB,CAACC,EAAY9C,CAAa,EAC1G,KAAK,kBAAkBQ,EAAQyB,EAAcE,EAAgBC,EAAUpC,CAAa,CACrF,CAAC,CACF,CACD,CACA,qBAAqBA,EAAegD,EAAUxE,EAAST,EAAUmC,EAAW,CAC3E,MAAMgC,EAAWlC,EAAc,YAAY,EAC3CkC,EAAS,OAAO,GAAoD1D,EAAQ,QAAQ,EACpF,MAAMgC,EAAS,IAAI,KAAK,IAAI,OAAOwC,EAAUxE,CAAO,EAC9CyE,EAAMzC,EAAO,iBAAiBhC,CAAO,EACrC0E,EAAmBlD,EAAc,uBAAuB,EAC9D,IAAImD,EAAqB,GACzB,MAAMC,EAAkB,UAAY,CACnC,OAAAD,EAAqB,GACdD,EAAiB,MAAM,KAAM,SAAS,CAC9C,EACA,OAAAE,EAAgB,IAAMF,EAAiB,IACvCD,EAAI,KAAKlG,EAAU,OAAQiD,EAAc,wBAAwB,EAAGoD,EAAiB5E,EAAQ,SAAU,KAAK,MAAM,QAAQA,EAAQ,QAAQ,CAAC,EAC3I0D,EAAS,OAAO,GAAkD1D,EAAQ,QAAQ,EAC9E2E,EACHpF,EAAS,EAGTmC,EAAU,IAAI,MAAM,iCAAiC1B,EAAQ,QAAQ,GAAG,CAAC,EAEnEgC,CACR,CACA,oCAAoCqB,EAAM,CACzC,GAAI,CAAC,KAAK,KAAK,mBACd,OAAOA,EAER,IAAIwB,EAAmBxB,EAAK,MAAM,iBAAiB,EACnD,OAAIwB,EAEI,YAAYA,EAAiB,CAAC,EAAE,YAAY,EAAI,IAAMA,EAAiB,CAAC,GAAG,QAAQ,MAAO,GAAG,CAAC,GAI9F,UAAUxB,CAAI,EAEvB,CACA,mBAAmByB,EAAQtB,EAAU,CACpC,MAAMuB,EAAO,KAAK,QAAQ,WAAW,KAAK,EAAE,OAAOvB,EAAU,MAAM,EAAE,OAAOsB,EAAO,KAAM,MAAM,EAAE,OAAO,QAAQ,KAAM,EAAE,EAAE,OAAO,KAAK,EAChIE,EAAW,KAAK,MAAM,SAASxB,CAAQ,EAAE,QAAQ,QAAS,EAAE,EAClE,OAAO,KAAK,MAAM,KAAKsB,EAAO,KAAM,GAAGE,CAAQ,IAAID,CAAI,OAAO,CAC/D,CACA,kBAAkB/C,EAAQyB,EAAcE,EAAgBsB,EAAkBzD,EAAe,CACpFQ,EAAO,mBAEV,KAAK,IAAI,OAAO2B,EAAgB7D,GAAO,CACtC0B,EAAc,YAAY,EAAE,OAAO,GAA6CmC,CAAc,EAC9F,KAAK,0BAA0B3B,EAAQyB,EAAcE,EAAgBnC,CAAa,EAC9E1B,GACH0B,EAAc,UAAU,EAAE,QAAQ1B,CAAG,CAEvC,CAAC,EAEOmF,GAER,KAAK,0BAA0BjD,EAAQyB,EAAcE,EAAgBnC,CAAa,CAEpF,CAIA,0BAA0BQ,EAAQyB,EAAcE,EAAgBnC,EAAe,CAC9E,IAAI0D,EAAU,KAAK,KAAK1D,EAAc,UAAU,EAAE,kBAAkB,EAAE,eAAe,YAAc,EAAI,KAAK,OAAO,EAAE,EACjH2D,EAAW,GACXC,EAAY,EACZxB,EACJ,MAAMyB,EAAa,IAAM,CACxB,WAAW,IAAM,CACXzB,IACJA,EAAW,KAAK,QAAQ,WAAW,KAAK,EAAE,OAAOH,EAAc,MAAM,EAAE,OAAO,GAE/E,MAAMa,EAAatC,EAAO,iBAAiB,EAC3C,GAAI,EAAAsC,EAAW,SAAW,GAAKA,EAAW,SAAWa,GAAYC,GAAa,GAI9E,IAAId,EAAW,OAASa,EAAU,CAEjCE,EAAW,EACX,MACD,CACAF,EAAWb,EAAW,OACtB,KAAK,IAAI,UAAUX,EAAgB,OAAO,OAAO,CAACC,EAAUU,CAAU,CAAC,EAAGxE,GAAO,CAC5EA,GACH0B,EAAc,UAAU,EAAE,QAAQ1B,CAAG,EAEtC0B,EAAc,YAAY,EAAE,OAAO,GAA4CmC,CAAc,EAC7F0B,EAAW,CACZ,CAAC,EACF,EAAGH,EAAW,KAAK,IAAI,EAAGE,GAAW,CAAE,CACxC,EAIAC,EAAW,CACZ,CACA,yBAAyBC,EAAY3B,EAAgBD,EAAUnE,EAAU,CACxE,GAAI,CAACoE,EAEJ,KAAK,IAAI,SAAS2B,EAAY,CAAE,SAAU,MAAO,EAAG/F,CAAQ,MAExD,CAEJ,IAAIgG,EACAjB,EACAV,EACA4B,EAAQ,EACZ,MAAMC,EAAQ3F,GAAQ,CACjBA,EACHP,EAASO,CAAG,EAEJ,EAAE0F,IAAU,GACpBjG,EAAS,OAAWgG,EAAQjB,EAAYV,CAAQ,CAElD,EACA,KAAK,IAAI,SAAS0B,EAAY,CAAE,SAAU,MAAO,EAAG,CAACxF,EAAK+D,IAAS,CAClE0B,EAAS1B,EACT4B,EAAK3F,CAAG,CACT,CAAC,EACD,KAAK,IAAI,SAAS6D,EAAgB,CAAC7D,EAAK+D,IAAS,CAC5C,CAAC/D,GAAO+D,GAAQA,EAAK,OAAS,GACjCD,EAAWC,EAAK,MAAM,EAAG,EAAE,EAC3BS,EAAaT,EAAK,MAAM,EAAE,EAC1BH,EAAS,OAAO,GAA0CC,CAAc,GAGxED,EAAS,OAAO,GAA2CC,CAAc,EAE1E8B,EAAK,CACN,CAAC,CACF,CACD,CACA,kBAAkBzD,EAAQyB,EAAcE,EAAgBC,EAAUpC,EAAe,CAC3EoC,IAID5B,EAAO,oBAIX,WAAW,IAAM,CAIhB,MAAM0D,EAAc,KAAK,QAAQ,WAAW,KAAK,EAAE,OAAOjC,EAAc,MAAM,EAAE,OAAO,EAClFG,EAAS,OAAO8B,CAAW,IAC/BlE,EAAc,UAAU,EAAE,QAAQ,IAAI,MAAM,iDAAiDmC,CAAc,kCAAkC,CAAC,EAC9I,KAAK,IAAI,OAAOA,EAAgB7D,GAAO,CAClCA,GACH0B,EAAc,UAAU,EAAE,QAAQ1B,CAAG,CAEvC,CAAC,EAEH,EAAG,KAAK,KAAK,KAAQ,EAAI,KAAK,OAAO,EAAE,CAAC,EACzC,CACD,CACAgC,EAAiB,KAAO,MACxBA,EAAiB,QAAU,wDAC3BA,EAAiB,QAAU;AAAA,KAC3B,SAASS,EAA0BmB,EAAUiC,EAAc,CAC1D,GAAIA,EAAa,gBAEhB,OAAOA,EAER,MAAMrD,EAAc,SAAqBsD,EAAM,CAC9ClC,EAAS,OAAO,GAAiDkC,CAAI,EACrE,GAAI,CACH,OAAOD,EAAaC,CAAI,CACzB,QACA,CACClC,EAAS,OAAO,GAA+CkC,CAAI,CACpE,CACD,EACA,OAAAtD,EAAY,gBAAkB,GACvBA,CACR,CACA/D,EAAU,0BAA4BgE,EACtC,SAASsD,EAAmBtF,EAAK,CAChC,OAAO,IAAIgB,EAAqBhB,CAAG,CACpC,CACAhC,EAAU,mBAAqBsH,CAChC,GAAG,YAAc,UAAY,CAAC,EAAE,EAKhC,IAAI,WACH,SAAUtH,EAAW,CAGrB,MAAMuH,CAAiB,CACtB,YAAYC,EAAc,CACzB,IAAIC,EAAYD,EAAa,YAAY,GAAG,EACxCC,IAAc,GACjB,KAAK,eAAiBD,EAAa,OAAO,EAAGC,EAAY,CAAC,EAG1D,KAAK,eAAiB,EAExB,CAIA,OAAO,mBAAmBjF,EAAU,CACnC,IAAIkF,EAAIlF,EAAUmF,EAGlB,IADAA,EAAU,SACHA,EAAQ,KAAKD,CAAC,GACpBA,EAAIA,EAAE,QAAQC,EAAS,GAAG,EAM3B,IAHAD,EAAIA,EAAE,QAAQ,SAAU,EAAE,EAE1BC,EAAU,uEACHA,EAAQ,KAAKD,CAAC,GACpBA,EAAIA,EAAE,QAAQC,EAAS,GAAG,EAG3B,OAAAD,EAAIA,EAAE,QAAQ,sEAAuE,EAAE,EAChFA,CACR,CAIA,cAAclF,EAAU,CACvB,IAAIrB,EAASqB,EACb,OAAKxC,EAAU,UAAU,eAAemB,CAAM,IACzCnB,EAAU,UAAU,WAAWmB,EAAQ,IAAI,GAAKnB,EAAU,UAAU,WAAWmB,EAAQ,KAAK,KAC/FA,EAASoG,EAAiB,mBAAmB,KAAK,eAAiBpG,CAAM,GAGpEA,CACR,CACD,CACAoG,EAAiB,KAAO,IAAIA,EAAiB,EAAE,EAC/CvH,EAAU,iBAAmBuH,EAG7B,MAAM7C,CAAO,CACZ,YAAYrD,EAAIuG,EAAOC,EAAc7G,EAAUmC,EAAW2E,EAAkB,CAC3E,KAAK,GAAKzG,EACV,KAAK,MAAQuG,EACb,KAAK,aAAeC,EACpB,KAAK,UAAY7G,EACjB,KAAK,WAAamC,EAClB,KAAK,iBAAmB2E,EACxB,KAAK,QAAU,CAAC,EAChB,KAAK,MAAQ,KACb,KAAK,gBAAkB,GACvB,KAAK,4BAA8B,KAAK,aAAa,OACrD,KAAK,YAAc,EACpB,CACA,OAAO,oBAAoB9G,EAAUyE,EAAM,CAC1C,GAAI,CACH,MAAO,CACN,cAAezE,EAAS,MAAMhB,EAAU,OAAQyF,CAAI,EACpD,cAAe,IAChB,CACD,OACO5B,EAAG,CACT,MAAO,CACN,cAAe,KACf,cAAeA,CAChB,CACD,CACD,CACA,OAAO,eAAe0C,EAAQxD,EAAa/B,EAAU+G,EAAoB,CACxE,OAAKxB,EAAO,oBAAoBxD,CAAW,EAMvCwD,EAAO,iBAAiB,EACpB,KAAK,oBAAoBvF,EAAU+G,CAAkB,EAEtD,CACN,cAAe/G,EAAS,MAAMhB,EAAU,OAAQ+H,CAAkB,EAClE,cAAe,IAChB,EAXQ,CACN,cAAe,KACf,cAAe,IAChB,CASF,CACA,SAAS5C,EAAUoB,EAAQwB,EAAoBC,EAA6B,CAC3E,KAAK,YAAc,GACnB,IAAIC,EAAgB,KACpB,GAAI,KAAK,UACR,GAAI,OAAO,KAAK,WAAc,WAAY,CACzC9C,EAAS,OAAO,GAA6C,KAAK,KAAK,EACvE,IAAIuC,EAAIhD,EAAO,eAAe6B,EAAQ,KAAK,MAAO,KAAK,UAAWwB,CAAkB,EACpFE,EAAgBP,EAAE,cAClBvC,EAAS,OAAO,GAA2C,KAAK,KAAK,EACjE,CAAC8C,GAAiB,OAAOP,EAAE,cAAkB,MAAgB,CAAC,KAAK,iBAAmB1H,EAAU,UAAU,QAAQ,KAAK,OAAO,KACjI,KAAK,QAAU0H,EAAE,cAEnB,MAEC,KAAK,QAAU,KAAK,UAGtB,GAAIO,EAAe,CAClB,IAAI1G,EAAMvB,EAAU,YAAYiI,CAAa,EAC7C1G,EAAI,MAAQ,UACZA,EAAI,SAAW,KAAK,MACpBA,EAAI,SAAWyG,EAA4B,KAAK,EAAE,EAClD,KAAK,MAAQzG,EACbgF,EAAO,QAAQhF,CAAG,CACnB,CACA,KAAK,aAAe,KACpB,KAAK,UAAY,KACjB,KAAK,WAAa,KAClB,KAAK,iBAAmB,IACzB,CAIA,kBAAkBA,EAAK,CAGtB,OAFA,KAAK,YAAc,GACnB,KAAK,MAAQA,EACT,KAAK,YACR,KAAK,WAAWA,CAAG,EACZ,IAED,EACR,CAIA,YAAa,CACZ,OAAO,KAAK,WACb,CACD,CACAvB,EAAU,OAAS0E,EACnB,MAAMwD,CAAiB,CACtB,aAAc,CACb,KAAK,QAAU,EACf,KAAK,0BAA4B,IAAI,IACrC,KAAK,0BAA4B,CAAC,EAElC,KAAK,YAAY,SAAS,EAC1B,KAAK,YAAY,QAAQ,EACzB,KAAK,YAAY,SAAS,CAC3B,CACA,gBAAiB,CAChB,OAAO,KAAK,OACb,CACA,YAAYnF,EAAa,CACxB,IAAI1B,EAAK,KAAK,0BAA0B,IAAI0B,CAAW,EACvD,OAAI,OAAO1B,EAAO,MACjBA,EAAK,KAAK,UACV,KAAK,0BAA0B,IAAI0B,EAAa1B,CAAE,EAClD,KAAK,0BAA0BA,CAAE,EAAI0B,GAE/B1B,CACR,CACA,eAAemB,EAAU,CACxB,OAAO,KAAK,0BAA0BA,CAAQ,CAC/C,CACD,CACA,MAAM2F,CAAkB,CACvB,YAAY9G,EAAI,CACf,KAAK,GAAKA,CACX,CACD,CACA8G,EAAkB,QAAU,IAAIA,EAAkB,CAAwB,EAC1EA,EAAkB,OAAS,IAAIA,EAAkB,CAAuB,EACxEA,EAAkB,QAAU,IAAIA,EAAkB,CAAwB,EAC1EnI,EAAU,kBAAoBmI,EAC9B,MAAMC,CAAiB,CACtB,YAAY/G,EAAIgH,EAAUC,EAAa,CACtC,KAAK,GAAKjH,EACV,KAAK,SAAWgH,EAChB,KAAK,YAAcC,CACpB,CACD,CACAtI,EAAU,iBAAmBoI,EAC7B,MAAMG,CAAc,CACnB,YAAYvG,EAAKwG,EAAcC,EAAYC,EAAanI,EAA2B,EAAG,CACrF,KAAK,KAAOyB,EACZ,KAAK,cAAgBwG,EACrB,KAAK,0BAA4BjI,EACjC,KAAK,YAAckI,EACnB,KAAK,aAAeC,EACpB,KAAK,kBAAoB,IAAIR,EAC7B,KAAK,QAAU,IAAIlI,EAAU,cAAc,KAAK,IAAI,EACpD,KAAK,oBAAsB,GAC3B,KAAK,UAAY,CAAC,EAClB,KAAK,eAAiB,CAAC,EACvB,KAAK,sBAAwB,CAAC,EAC9B,KAAK,4BAA8B,IAAI,IACvC,KAAK,4BAA8B,KACnC,KAAK,UAAY,KACjB,KAAK,eAAiB,CAAC,EACvB,KAAK,sBAAwB,CAAC,EAC9B,KAAK,uBAAyB,CAAC,EAC/B,KAAK,aAAa,cAAgB,IACnC,CACA,OAAQ,CACP,OAAO,IAAIuI,EAAc,KAAK,KAAM,KAAK,cAAe,KAAK,YAAa,KAAK,aAAc,KAAK,yBAAyB,CAC5H,CACA,wBAAyB,CACxB,OAAO,KAAK,WACb,CACA,yBAA0B,CACzB,OAAO,KAAK,YACb,CACA,OAAO,6BAA6B1H,EAAQ8H,EAAO,CAClD,IAAIC,EAAaC,GAAQA,EAAI,QAAQ,MAAO,GAAG,EAC3CC,EAAiBF,EAAU/H,CAAM,EACjCkI,EAAcJ,EAAM,MAAM,IAAI,EAClC,QAASxG,EAAI,EAAGA,EAAI4G,EAAY,OAAQ5G,IAAK,CAC5C,IAAI6G,EAAID,EAAY5G,CAAC,EAAE,MAAM,sBAAsB,EACnD,GAAI6G,EAAG,CACN,IAAIC,EAAYD,EAAE,CAAC,EACfE,EAAYF,EAAE,CAAC,EACfG,EAAcH,EAAE,CAAC,EACjBI,EAAiB,KAAK,IAAIH,EAAU,YAAY,GAAG,EAAI,EAAGA,EAAU,YAAY,GAAG,EAAI,CAAC,EAG5F,GAFAA,EAAYA,EAAU,OAAOG,CAAc,EAC3CH,EAAYL,EAAUK,CAAS,EAC3BA,IAAcH,EAAgB,CACjC,IAAIpB,EAAI,CACP,KAAM,SAASwB,EAAW,EAAE,EAC5B,IAAK,SAASC,EAAa,EAAE,CAC9B,EACA,OAAIzB,EAAE,OAAS,IACdA,EAAE,KAAO,IAEHA,CACR,CACD,CACD,CACA,MAAM,IAAI,MAAM,mDAAqD7G,CAAM,CAC5E,CACA,cAAe,CACd,GAAI,CAAC,KAAK,QAAQ,QAAQ,EACzB,OAAO,KAER,IAAIM,EAAS,CAAC,EAAGkI,EAAY,EAC7B,QAAS,EAAI,EAAG3G,EAAM,KAAK,UAAU,OAAQ,EAAIA,EAAK,IAAK,CAC1D,IAAIsG,EAAI,KAAK,UAAU,CAAC,EACxB,GAAI,CAACA,EACJ,SAED,IAAIM,EAAW,KAAK,eAAeN,EAAE,EAAE,GAAK,KACxCO,EAAc,KAAK,sBAAsBP,EAAE,EAAE,GAAK,KAClDnB,EAAe,KAAK,uBAAuBmB,EAAE,EAAE,EACnD7H,EAAOkI,GAAW,EAAI,CACrB,GAAIL,EAAE,MACN,KAAMM,EACN,eAAiBA,GAAYC,EAAchB,EAAc,6BAA6Be,EAAUC,CAAW,EAAI,KAC/G,aAAc1B,EACd,KAAM,KACN,QAASmB,EAAE,OACZ,CACD,CACA,OAAO7H,CACR,CACA,aAAc,CACb,OAAK,KAAK,YACL,KAAK,QAAQ,kBAAkB,EAClC,KAAK,UAAY,IAAInB,EAAU,oBAAoB,KAAK,yBAAyB,EAGjF,KAAK,UAAYA,EAAU,wBAAwB,UAG9C,KAAK,SACb,CACA,iBAAkB,CACjB,OAAO,KAAK,YAAY,EAAE,UAAU,CACrC,CAMA,6BAA6B6H,EAAc7G,EAAU,CACpD,GAAI,KAAK,8BAAgC,KACxC,MAAM,IAAI,MAAM,yDAAyD,EAE1E,IAAI2H,EAAQ,KACR,KAAK,QAAQ,QAAQ,IACxBA,EAAQ,IAAI,MAAM,eAAe,EAAE,OAAS,MAE7C,KAAK,4BAA8B,CAClC,MAAOA,EACP,aAAcd,EACd,SAAU7G,CACX,CACD,CAOA,aAAa+B,EAAa8E,EAAc7G,EAAUmC,EAAWwF,EAAOb,EAAmB,IAAIP,EAAiBxE,CAAW,EAAG,CACzH,IAAIP,EAAW,KAAK,kBAAkB,YAAYO,CAAW,EAC7D,GAAI,KAAK,UAAUP,CAAQ,EAAG,CACxB,KAAK,QAAQ,6BAA6BO,CAAW,GACzD,QAAQ,KAAK,mCAAsCA,EAAc,GAAI,EAGtE,MACD,CACA,IAAIiG,EAAI,IAAItE,EAAOlC,EAAUO,EAAa,KAAK,uBAAuB8E,EAAcC,CAAgB,EAAG9G,EAAUmC,EAAW2E,CAAgB,EAC5I,KAAK,UAAUtF,CAAQ,EAAIwG,EACvB,KAAK,QAAQ,QAAQ,IACxB,KAAK,sBAAsBxG,CAAQ,EAAImG,EACvC,KAAK,uBAAuBnG,CAAQ,GAAKwG,EAAE,cAAgB,CAAC,GAAG,IAAIQ,GAAO,KAAK,kBAAkB,eAAeA,EAAI,EAAE,CAAC,GAIxH,KAAK,SAASR,CAAC,CAChB,CACA,qBAAqBS,EAAY3B,EAAkB,CAClD,GAAI2B,IAAe,UAClB,OAAOtB,EAAkB,QAE1B,GAAIsB,IAAe,SAClB,OAAOtB,EAAkB,OAE1B,GAAIsB,IAAe,UAClB,OAAOtB,EAAkB,QAG1B,IAAIuB,EAAYD,EAAW,QAAQ,GAAG,EACtC,GAAIC,GAAa,EAAG,CACnB,IAAIC,EAAc7B,EAAiB,cAAc2B,EAAW,OAAO,EAAGC,CAAS,CAAC,EAC5EpB,EAAcR,EAAiB,cAAc2B,EAAW,OAAOC,EAAY,CAAC,CAAC,EAC7EE,EAAe,KAAK,kBAAkB,YAAYD,EAAc,IAAMrB,CAAW,EACjFD,EAAW,KAAK,kBAAkB,YAAYsB,CAAW,EAC7D,OAAO,IAAIvB,EAAiBwB,EAAcvB,EAAUC,CAAW,CAChE,CACA,OAAO,IAAIH,EAAkB,KAAK,kBAAkB,YAAYL,EAAiB,cAAc2B,CAAU,CAAC,CAAC,CAC5G,CACA,uBAAuB5B,EAAcC,EAAkB,CACtD,IAAI3G,EAAS,CAAC,EAAGkI,EAAY,EAC7B,QAASlH,EAAI,EAAGO,EAAMmF,EAAa,OAAQ1F,EAAIO,EAAKP,IACnDhB,EAAOkI,GAAW,EAAI,KAAK,qBAAqBxB,EAAa1F,CAAC,EAAG2F,CAAgB,EAElF,OAAO3G,CACR,CACA,iBAAiB2G,EAAkBD,EAAc7G,EAAUmC,EAAW,CACrE,GAAI,OAAO0E,GAAiB,SAC3B,OAAO,KAAK,mBAAmBA,EAAcC,CAAgB,EAE9D,KAAK,aAAa9H,EAAU,UAAU,wBAAwB,EAAG6H,EAAc7G,EAAUmC,EAAW,KAAM2E,CAAgB,CAC3H,CAMA,mBAAmB+B,EAAc/B,EAAmB,IAAIP,EAAiBsC,CAAY,EAAG,CACvF,IAAIJ,EAAa,KAAK,qBAAqBI,EAAc/B,CAAgB,EACrEkB,EAAI,KAAK,UAAUS,EAAW,EAAE,EACpC,GAAI,CAACT,EACJ,MAAM,IAAI,MAAM,qEAAwEa,EAAe,8CAA+C,EAEvJ,GAAI,CAACb,EAAE,WAAW,EACjB,MAAM,IAAI,MAAM,qEAAwEa,EAAe,sDAAuD,EAE/J,GAAIb,EAAE,MACL,MAAMA,EAAE,MAET,OAAOA,EAAE,OACV,CACA,UAAUc,EAAQC,EAAiB,CAClC,IAAIC,EAAuB,KAAK,QAAQ,kBAAkB,EACtDD,EACH,KAAK,QAAU,IAAI/J,EAAU,cAAc,KAAK,KAAM8J,CAAM,EAG5D,KAAK,QAAU,KAAK,QAAQ,cAAcA,CAAM,EAE7C,KAAK,QAAQ,kBAAkB,GAAK,CAACE,IACxC,KAAK,UAAY,KAEnB,CACA,WAAY,CACX,OAAO,KAAK,OACb,CAKA,QAAQxH,EAAU,CACjB,GAAI,KAAK,8BAAgC,KAAM,CAC9C,IAAIyH,EAAa,KAAK,4BACtB,KAAK,4BAA8B,KAEnC,KAAK,aAAa,KAAK,kBAAkB,eAAezH,CAAQ,EAAGyH,EAAW,aAAcA,EAAW,SAAU,KAAMA,EAAW,KAAK,CACxI,CACD,CACA,iBAAiBzH,EAAU0H,EAAM,CAChC,IAAInH,EAAc,KAAK,kBAAkB,eAAeP,CAAQ,EAC5D2H,GAAY,KAAK,sBAAsB3H,CAAQ,GAAK,CAAC,GAAG,IAAK4H,GAAgB,KAAK,kBAAkB,eAAeA,CAAW,CAAC,EACnI,MAAM7I,EAAMvB,EAAU,YAAYkK,CAAI,EACtC,OAAA3I,EAAI,MAAQ,UACZA,EAAI,SAAWwB,EACfxB,EAAI,SAAW4I,EACR5I,CACR,CAKA,aAAaiB,EAAUjB,EAAK,CAC3B,MAAM8I,EAAQ,KAAK,iBAAiB7H,EAAUjB,CAAG,EAC5C,KAAK,UAAUiB,CAAQ,IAC3B,KAAK,UAAUA,CAAQ,EAAI,IAAIkC,EAAOlC,EAAU,KAAK,kBAAkB,eAAeA,CAAQ,EAAG,CAAC,EAAG,IAAM,CAAE,EAAG,KAAM,IAAI,GAG3H,IAAI8H,EAAe,CAAC,EACpB,QAASnI,EAAI,EAAGO,EAAM,KAAK,kBAAkB,eAAe,EAAGP,EAAIO,EAAKP,IACvEmI,EAAanI,CAAC,EAAI,GAEnB,IAAIoI,EAAkB,GAClBC,EAAQ,CAAC,EAGb,IAFAA,EAAM,KAAKhI,CAAQ,EACnB8H,EAAa9H,CAAQ,EAAI,GAClBgI,EAAM,OAAS,GAAG,CACxB,IAAIC,EAAeD,EAAM,MAAM,EAC3BxB,EAAI,KAAK,UAAUyB,CAAY,EAC/BzB,IACHuB,EAAkBvB,EAAE,kBAAkBqB,CAAK,GAAKE,GAEjD,IAAIG,EAAc,KAAK,sBAAsBD,CAAY,EACzD,GAAIC,EACH,QAASvI,EAAI,EAAGO,EAAMgI,EAAY,OAAQvI,EAAIO,EAAKP,IAAK,CACvD,IAAIwI,EAAaD,EAAYvI,CAAC,EACzBmI,EAAaK,CAAU,IAC3BH,EAAM,KAAKG,CAAU,EACrBL,EAAaK,CAAU,EAAI,GAE7B,CAEF,CACKJ,GACJ,KAAK,QAAQ,QAAQF,CAAK,CAE5B,CAOA,mBAAmBO,EAAQC,EAAM,CAChC,IAAIzI,EAAO,KAAK,UAAUwI,CAAM,EAChC,GAAI,CAACxI,EACJ,MAAO,GAER,IAAI0I,EAAU,CAAC,EACf,QAAS3I,EAAI,EAAGO,EAAM,KAAK,kBAAkB,eAAe,EAAGP,EAAIO,EAAKP,IACvE2I,EAAQ3I,CAAC,EAAI,GAEd,IAAIqI,EAAQ,CAAC,EAIb,IAFAA,EAAM,KAAKpI,CAAI,EACf0I,EAAQF,CAAM,EAAI,GACXJ,EAAM,OAAS,GAAG,CAGxB,IAAI3C,EADU2C,EAAM,MAAM,EACC,aAC3B,GAAI3C,EAEH,QAAS1F,EAAI,EAAGO,EAAMmF,EAAa,OAAQ1F,EAAIO,EAAKP,IAAK,CACxD,IAAIsH,EAAa5B,EAAa1F,CAAC,EAC/B,GAAIsH,EAAW,KAAOoB,EAErB,MAAO,GAER,IAAIE,EAAmB,KAAK,UAAUtB,EAAW,EAAE,EAC/CsB,GAAoB,CAACD,EAAQrB,EAAW,EAAE,IAE7CqB,EAAQrB,EAAW,EAAE,EAAI,GACzBe,EAAM,KAAKO,CAAgB,EAE7B,CAEF,CAEA,MAAO,EACR,CAOA,eAAeH,EAAQC,EAAMG,EAAO,CACnC,GAAIJ,IAAWC,GAAQG,IAAU,GAChC,MAAO,CAACJ,CAAM,EAEf,IAAIxI,EAAO,KAAK,UAAUwI,CAAM,EAChC,GAAI,CAACxI,EACJ,OAAO,KAGR,IAAIyF,EAAezF,EAAK,aACxB,GAAIyF,EACH,QAAS1F,EAAI,EAAGO,EAAMmF,EAAa,OAAQ1F,EAAIO,EAAKP,IAAK,CACxD,IAAI2C,EAAO,KAAK,eAAe+C,EAAa1F,CAAC,EAAE,GAAI0I,EAAMG,EAAQ,CAAC,EAClE,GAAIlG,IAAS,KACZ,OAAAA,EAAK,KAAK8F,CAAM,EACT9F,CAET,CAED,OAAO,IACR,CAIA,eAAegD,EAAkB,CAChC,IAAI3G,EAAU,CAAC0G,EAAc7G,EAAUmC,IAC/B,KAAK,iBAAiB2E,EAAkBD,EAAc7G,EAAUmC,CAAS,EAEjF,OAAAhC,EAAO,MAASE,GACR,KAAK,QAAQ,aAAayG,EAAiB,cAAczG,CAAE,CAAC,EAEpEF,EAAO,SAAW,IACV,KAAK,gBAAgB,EAE7BA,EAAO,mBAAqB,IACpB,KAAK,oBAEbA,EAAO,OAAS,CAAC2I,EAAQC,EAAkB,KAAU,CACpD,KAAK,UAAUD,EAAQC,CAAe,CACvC,EACA5I,EAAO,iBAAmBnB,EAAU,OAAO,YACpCmB,CACR,CACA,YAAYqB,EAAU,CACrB,GAAI,KAAK,UAAUA,CAAQ,GAAK,KAAK,eAAeA,CAAQ,EAE3D,OAED,KAAK,eAAeA,CAAQ,EAAI,GAChC,IAAIO,EAAc,KAAK,kBAAkB,eAAeP,CAAQ,EAC5DyI,EAAQ,KAAK,QAAQ,gBAAgBlI,CAAW,EAChDmI,EAAqB,oBACrB,KAAK,KAAK,SAAWnI,EAAY,QAAQ,GAAG,IAAM,IAAMmI,EAAmB,KAAKnI,CAAW,IAC9FkI,EAAM,KAAK,QAAUlI,CAAW,EAEjC,IAAIoI,EAAgB,GAChBC,EAAgB7J,GAAQ,CAE3B,GADA4J,IACIA,GAAiBF,EAAM,OAE1B,KAAK,aAAazI,EAAUjB,CAAG,MAE3B,CACJ,IAAI8J,EAAcJ,EAAME,CAAa,EACjChG,EAAW,KAAK,YAAY,EAChC,GAAI,KAAK,QAAQ,QAAQ,GAAKkG,IAAgB,SAAU,CACvD,KAAK,eAAe7I,CAAQ,EAAI6I,EAChC,KAAK,aAAa,KAAK,kBAAkB,eAAe7I,CAAQ,EAAG,CAAC,EAAG,KAAM,KAAM,IAAI,EACvF,KAAK,QAAQA,CAAQ,EACrB,MACD,CACA2C,EAAS,OAAO,GAA6CkG,CAAW,EACxE,KAAK,cAAc,KAAK,KAAMA,EAAa,IAAM,CAC5C,KAAK,QAAQ,QAAQ,IACxB,KAAK,eAAe7I,CAAQ,EAAI6I,GAEjClG,EAAS,OAAO,GAA6CkG,CAAW,EACxE,KAAK,QAAQ7I,CAAQ,CACtB,EAAIjB,GAAQ,CACX4D,EAAS,OAAO,GAAgDkG,CAAW,EAC3ED,EAAa7J,CAAG,CACjB,CAAC,CACF,CACD,EACA6J,EAAa,IAAI,CAClB,CAOA,sBAAsBE,EAAQC,EAAkB,CAC/C,GAAI,KAAK,UAAUA,EAAiB,EAAE,GAAK,KAAK,eAAeA,EAAiB,EAAE,EAEjF,OAED,KAAK,eAAeA,EAAiB,EAAE,EAAI,GAE3C,IAAIC,EAASpK,GAAU,CACtB,KAAK,aAAa,KAAK,kBAAkB,eAAemK,EAAiB,EAAE,EAAG,CAAC,EAAGnK,EAAO,KAAM,IAAI,CACpG,EACAoK,EAAK,MAASjK,GAAQ,CACrB,KAAK,QAAQ,QAAQ,KAAK,iBAAiBgK,EAAiB,GAAIhK,CAAG,CAAC,CACrE,EACA+J,EAAO,KAAKC,EAAiB,YAAa,KAAK,eAAehE,EAAiB,IAAI,EAAGiE,EAAM,KAAK,QAAQ,kBAAkB,CAAC,CAC7H,CAIA,SAASC,EAAQ,CAChB,IAAI5D,EAAe4D,EAAO,aAC1B,GAAI5D,EACH,QAAS,EAAI,EAAGnF,EAAMmF,EAAa,OAAQ,EAAInF,EAAK,IAAK,CACxD,IAAI+G,EAAa5B,EAAa,CAAC,EAC/B,GAAI4B,IAAetB,EAAkB,QAAS,CAC7CsD,EAAO,gBAAkB,GACzBA,EAAO,8BACP,QACD,CACA,GAAIhC,IAAetB,EAAkB,OAAQ,CAC5CsD,EAAO,8BACP,QACD,CACA,GAAIhC,IAAetB,EAAkB,QAAS,CAC7CsD,EAAO,8BACP,QACD,CACA,IAAIV,EAAmB,KAAK,UAAUtB,EAAW,EAAE,EACnD,GAAIsB,GAAoBA,EAAiB,WAAW,EAAG,CACtD,GAAIA,EAAiB,MAAO,CAC3BU,EAAO,kBAAkBV,EAAiB,KAAK,EAC/C,MACD,CACAU,EAAO,8BACP,QACD,CACA,GAAI,KAAK,mBAAmBhC,EAAW,GAAIgC,EAAO,EAAE,EAAG,CACtD,KAAK,oBAAsB,GAC3B,QAAQ,KAAK,wCAA2C,KAAK,kBAAkB,eAAehC,EAAW,EAAE,EAAI,UAAc,KAAK,kBAAkB,eAAegC,EAAO,EAAE,EAAI,6BAA8B,EAC9M,IAAIC,EAAY,KAAK,eAAejC,EAAW,GAAIgC,EAAO,GAAI,CAAC,GAAK,CAAC,EACrEC,EAAU,QAAQ,EAClBA,EAAU,KAAKjC,EAAW,EAAE,EAC5B,QAAQ,KAAKiC,EAAU,IAAIrK,GAAM,KAAK,kBAAkB,eAAeA,CAAE,CAAC,EAAE,KAAK;AAAA,CAAQ,CAAC,EAE1FoK,EAAO,8BACP,QACD,CAIA,GAFA,KAAK,sBAAsBhC,EAAW,EAAE,EAAI,KAAK,sBAAsBA,EAAW,EAAE,GAAK,CAAC,EAC1F,KAAK,sBAAsBA,EAAW,EAAE,EAAE,KAAKgC,EAAO,EAAE,EACpDhC,aAAsBrB,EAAkB,CAC3C,IAAIkD,EAAS,KAAK,UAAU7B,EAAW,QAAQ,EAC/C,GAAI6B,GAAUA,EAAO,WAAW,EAAG,CAClC,KAAK,sBAAsBA,EAAO,QAAS7B,CAAU,EACrD,QACD,CAEA,IAAIkC,EAAoB,KAAK,4BAA4B,IAAIlC,EAAW,QAAQ,EAC3EkC,IACJA,EAAoB,CAAC,EACrB,KAAK,4BAA4B,IAAIlC,EAAW,SAAUkC,CAAiB,GAE5EA,EAAkB,KAAKlC,CAAU,EACjC,KAAK,YAAYA,EAAW,QAAQ,EACpC,QACD,CACA,KAAK,YAAYA,EAAW,EAAE,CAC/B,CAEGgC,EAAO,8BAAgC,GAC1C,KAAK,kBAAkBA,CAAM,CAE/B,CACA,kBAAkBA,EAAQ,CACzB,IAAItG,EAAW,KAAK,YAAY,EAChC,GAAIsG,EAAO,WAAW,EAErB,OAED,IAAI5D,EAAe4D,EAAO,aACtB1D,EAAqB,CAAC,EAC1B,GAAIF,EACH,QAAS1F,EAAI,EAAGO,EAAMmF,EAAa,OAAQ1F,EAAIO,EAAKP,IAAK,CACxD,IAAIsH,EAAa5B,EAAa1F,CAAC,EAC/B,GAAIsH,IAAetB,EAAkB,QAAS,CAC7CJ,EAAmB5F,CAAC,EAAIsJ,EAAO,QAC/B,QACD,CACA,GAAIhC,IAAetB,EAAkB,OAAQ,CAC5CJ,EAAmB5F,CAAC,EAAI,CACvB,GAAIsJ,EAAO,MACX,OAAQ,IACA,KAAK,QAAQ,mBAAmBA,EAAO,KAAK,CAErD,EACA,QACD,CACA,GAAIhC,IAAetB,EAAkB,QAAS,CAC7CJ,EAAmB5F,CAAC,EAAI,KAAK,eAAesJ,EAAO,gBAAgB,EACnE,QACD,CACA,IAAIV,EAAmB,KAAK,UAAUtB,EAAW,EAAE,EACnD,GAAIsB,EAAkB,CACrBhD,EAAmB5F,CAAC,EAAI4I,EAAiB,QACzC,QACD,CACAhD,EAAmB5F,CAAC,EAAI,IACzB,CAED,MAAM6F,EAA+BxF,IAC5B,KAAK,sBAAsBA,CAAQ,GAAK,CAAC,GAAG,IAAK4H,GAAgB,KAAK,kBAAkB,eAAeA,CAAW,CAAC,EAE5HqB,EAAO,SAAStG,EAAU,KAAK,QAAS4C,EAAoBC,CAA2B,EAEvF,IAAI0C,EAAc,KAAK,sBAAsBe,EAAO,EAAE,EAEtD,GADA,KAAK,sBAAsBA,EAAO,EAAE,EAAI,KACpCf,EAGH,QAASvI,EAAI,EAAGO,EAAMgI,EAAY,OAAQvI,EAAIO,EAAKP,IAAK,CACvD,IAAIyJ,EAAsBlB,EAAYvI,CAAC,EACnC0J,EAAoB,KAAK,UAAUD,CAAmB,EAC1DC,EAAkB,8BACdA,EAAkB,8BAAgC,GACrD,KAAK,kBAAkBA,CAAiB,CAE1C,CAED,IAAIF,EAAoB,KAAK,4BAA4B,IAAIF,EAAO,EAAE,EACtE,GAAIE,EAAmB,CAGtB,KAAK,4BAA4B,OAAOF,EAAO,EAAE,EAEjD,QAAStJ,EAAI,EAAGO,EAAMiJ,EAAkB,OAAQxJ,EAAIO,EAAKP,IACxD,KAAK,sBAAsBsJ,EAAO,QAASE,EAAkBxJ,CAAC,CAAC,CAEjE,CACD,CACD,CACAnC,EAAU,cAAgBuI,CAC3B,GAAG,YAAc,UAAY,CAAC,EAAE,EAChC,IAAI,OACA,WACH,SAAUvI,EAAW,CACrB,MAAMgC,EAAM,IAAIhC,EAAU,YAC1B,IAAIiD,EAAgB,KACpB,MAAM6I,EAAa,SAAUzK,EAAIwG,EAAc7G,EAAU,CACpD,OAAOK,GAAO,WACjBL,EAAW6G,EACXA,EAAexG,EACfA,EAAK,OAEF,OAAOwG,GAAiB,UAAY,CAAC,MAAM,QAAQA,CAAY,KAClE7G,EAAW6G,EACXA,EAAe,MAEXA,IACJA,EAAe,CAAC,UAAW,UAAW,QAAQ,GAE3CxG,EACH4B,EAAc,aAAa5B,EAAIwG,EAAc7G,EAAU,KAAM,IAAI,EAGjEiC,EAAc,6BAA6B4E,EAAc7G,CAAQ,CAEnE,EACA8K,EAAW,IAAM,CAChB,OAAQ,EACT,EACA,MAAMC,EAAsB,SAAUjC,EAAQC,EAAkB,GAAO,CACtE9G,EAAc,UAAU6G,EAAQC,CAAe,CAChD,EACMiC,EAAc,UAAY,CAC/B,GAAI,UAAU,SAAW,EAAG,CAC3B,GAAK,UAAU,CAAC,YAAa,QAAW,CAAC,MAAM,QAAQ,UAAU,CAAC,CAAC,EAAG,CACrED,EAAoB,UAAU,CAAC,CAAC,EAChC,MACD,CACA,GAAI,OAAO,UAAU,CAAC,GAAM,SAC3B,OAAO9I,EAAc,mBAAmB,UAAU,CAAC,CAAC,CAEtD,CACA,IAAI,UAAU,SAAW,GAAK,UAAU,SAAW,IAC9C,MAAM,QAAQ,UAAU,CAAC,CAAC,EAAG,CAChCA,EAAc,aAAajD,EAAU,UAAU,wBAAwB,EAAG,UAAU,CAAC,EAAG,UAAU,CAAC,EAAG,UAAU,CAAC,EAAG,IAAI,EACxH,MACD,CAED,MAAM,IAAI,MAAM,2BAA2B,CAC5C,EACAgM,EAAY,OAASD,EACrBC,EAAY,UAAY,UAAY,CACnC,OAAO/I,EAAc,UAAU,EAAE,kBAAkB,CACpD,EACA+I,EAAY,MAAQ,UAAY,CAC/B/I,EAAgBA,EAAc,MAAM,CACrC,EACA+I,EAAY,aAAe,UAAY,CACtC,OAAO/I,EAAc,aAAa,CACnC,EACA+I,EAAY,SAAW,UAAY,CAClC,OAAO/I,EAAc,gBAAgB,CACtC,EACA+I,EAAY,OAASF,EACrB,SAASG,GAAO,CACf,GAAI,OAAOjM,EAAU,OAAO,QAAY,KAAe,OAAO,QAAY,IAAa,CACtF,MAAMoH,EAAgBpH,EAAU,OAAO,SAAW,QAClD,GAAI,OAAOoH,GAAiB,YAAc,OAAOA,EAAa,SAAY,WAAY,CAErF,MAAMrD,EAAc/D,EAAU,0BAA0BiD,EAAc,YAAY,EAAGmE,CAAY,EACjGpH,EAAU,OAAO,YAAc+D,EAC/BiI,EAAY,YAAcjI,EAC1BiI,EAAY,iBAAmBjI,CAChC,CACD,CACI/B,EAAI,QAAU,CAACA,EAAI,oBAAsB,CAACA,EAAI,mCACjD,OAAO,QAAUgK,GAGZhK,EAAI,qBACRhC,EAAU,OAAO,OAAS8L,GAE3B9L,EAAU,OAAO,QAAUgM,EAE7B,CACAhM,EAAU,KAAOiM,GACb,OAAOjM,EAAU,OAAO,QAAW,YAAc,CAACA,EAAU,OAAO,OAAO,OAC7EiD,EAAgB,IAAIjD,EAAU,cAAcgC,EAAKhC,EAAU,mBAAmBgC,CAAG,EAAG8J,EAAYE,EAAahM,EAAU,UAAU,4BAA4B,CAAC,EAE1J,OAAOA,EAAU,OAAO,QAAY,KAAe,OAAOA,EAAU,OAAO,SAAY,YAC1FgM,EAAY,OAAOhM,EAAU,OAAO,OAAO,EAG5C,OAAS,UAAY,CACpB,OAAO8L,EAAW,MAAM,KAAM,SAAS,CACxC,EACA,OAAO,IAAMA,EAAW,IACpB,OAAO,gBAAoB,KAC9BG,EAAK,EAGR,GAAG,YAAc,UAAY,CAAC,EAAE", "names": ["AMDLoader", "Environment", "LoaderEvent", "type", "detail", "timestamp", "LoaderEventRecorder", "loaderAvailableTimestamp", "NullLoaderEventRecorder", "Utilities", "isWindows", "uri", "haystack", "needle", "url", "obj", "callback", "key", "isEmpty", "result", "value", "id", "ensureError", "err", "ConfigurationOptionsUtil", "options", "defaultOnError", "overwrite", "base", "key2", "value2", "Configuration", "env", "nodeMain", "dirnameIndex", "i", "from", "to", "a", "b", "moduleId", "pathRule", "len", "j", "lenJ", "urls", "results", "strModuleId", "OnlyOnceScriptLoader", "moduleManager", "scriptSrc", "errorback", "WorkerScriptLoader", "preferScriptTags", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "NodeScriptLoader", "scriptCallbacks", "script", "unbind", "loadEventListener", "errorEventListener", "e", "opts", "nodeRequire", "ensureRecordedNodeRequire", "pieces", "moduleExports", "trustedTypesPolicy", "cspNonce", "canUseEval", "response", "text", "nodeCachedData", "that", "<PERSON><PERSON><PERSON>", "makeRequireFunction", "mod", "require", "path", "request", "content", "filename", "scriptSource", "recorder", "cachedDataPath", "hashData", "data", "compileWrapper", "dirname", "args", "nodeInstrumenter", "c", "normalizedScriptSrc", "vmScriptPathOrUri", "wantsCachedData", "cachedData", "scriptOpts", "contents", "ret", "globalDefineFunc", "receivedDefineCall", "localDefineFunc", "driveLetterMatch", "config", "hash", "basename", "createCachedData", "timeout", "lastSize", "iteration", "createLoop", "sourcePath", "source", "steps", "step", "hashDataNow", "_nodeRequire", "what", "createScriptLoader", "ModuleIdResolver", "fromModuleId", "lastSlash", "r", "pattern", "strId", "dependencies", "moduleIdResolver", "dependenciesValues", "inversedependenciesProvider", "producedError", "ModuleIdProvider", "RegularDependency", "PluginDependency", "pluginId", "pluginParam", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "defineFunc", "requireFunc", "stack", "normalize", "str", "normalizedPath", "stackPieces", "m", "stackPath", "stackLine", "stackColumn", "trimPathOffset", "resultLen", "location", "defineStack", "dep", "dependency", "bangIndex", "strPluginId", "dependencyId", "_strModuleId", "params", "shouldOverwrite", "oldShouldRecordStats", "defineCall", "_err", "needed<PERSON><PERSON>", "intModuleId", "error", "seenModuleId", "someoneNotified", "queue", "queueElement", "inverseDeps", "inverseDep", "fromId", "toId", "inQueue", "dependencyModule", "depth", "paths", "scopedPackageRegex", "lastPathIndex", "loadNextPath", "currentPath", "plugin", "pluginDependency", "load", "module", "cyclePath", "inversePluginDeps", "inverseDependencyId", "inverseDependency", "DefineFunc", "_requireFunc_config", "RequireFunc", "init"], "file": "loader.js"}