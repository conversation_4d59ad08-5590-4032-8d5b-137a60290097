{"version": 3, "sources": ["out-editor/nls.messages.ko.js"], "sourcesContent": ["/*!-----------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/vscode/blob/main/LICENSE.txt\n *-----------------------------------------------------------*/\n\nglobalThis._VSCODE_NLS_MESSAGES=[\"{0}({1})\",\"입력\",\"대/소문자 구분\",\"단어 단위로\",\"정규식 사용\",\"입력\",\"대/소문자 보존\",\"{0}을(를) 사용하여 접근성 보기에서 이를 검사합니다.\",\"현재 키 바인딩을 통해 트리거할 수 없는 접근성 보기 열기 명령을 통해 접근성 보기에서 이를 검사합니다.\",\"오류: {0}\",\"경고: {0}\",\"정보: {0}\",\" 또는 {0} 기록의 경우\",\" ({0} 기록용)\",\"입력이 지워짐\",\"바인딩 안 됨\",\"Box 선택\",\"기타 작업...\",\"필터\",\"유사 항목 일치\",\"필터링할 형식\",\"입력하여 검색\",\"입력하여 검색\",\"닫기\",\"결과 없음\",\"결과를 찾을 수 없습니다.\",null,\"(비어 있음)\",\"{0}: {1}\",\"시스템 오류가 발생했습니다({0}).\",\"알 수 없는 오류가 발생했습니다. 자세한 내용은 로그를 참조하세요.\",\"알 수 없는 오류가 발생했습니다. 자세한 내용은 로그를 참조하세요.\",\"{0}(총 {1}개의 오류)\",\"알 수 없는 오류가 발생했습니다. 자세한 내용은 로그를 참조하세요.\",\"Ctrl\",\"Shift\",\"<Alt>\",\"Windows\",\"Ctrl\",\"Shift\",\"<Alt>\",\"슈퍼\",\"Ctrl\",\"Shift\",\"옵션\",\"명령\",\"Ctrl\",\"Shift\",\"<Alt>\",\"Windows\",\"Ctrl\",\"Shift\",\"<Alt>\",\"슈퍼\",null,null,null,null,null,\"더 긴 줄로 이동하는 경우에도 끝에 고정\",\"더 긴 줄로 이동하는 경우에도 끝에 고정\",\"보조 커서가 제거됨\",\"실행 취소(&&U)\",\"실행 취소\",\"다시 실행(&&R)\",\"다시 실행\",\"모두 선택(&&S)\",\"모두 선택\",\"{0} 키를 눌러 마우스를 위에 놓기\",\"로드 중...\",\"커서 수를 {0}개로 제한했습니다. 더 큰 변경 내용을 위해서는 [찾아서 교체](https://code.visualstudio.com/docs/editor/codebasics#_find-and-replace)를 사용하거나 편집기 다중 커서 제한 설정을 늘리는 것이 좋습니다.\",\"다중 커서 제한 늘리기\",\"변경되지 않은 영역 축소 토글\",\"이동된 코드 블록 표시 토글\",\"공간이 제한된 경우 인라인 보기 사용 설정/해제\",\"diff 편집기\",\"스위치 쪽\",\"비교 이동 종료\",\"변경되지 않은 모든 영역 축소\",\"변경되지 않은 모든 영역 표시\",\"되돌리기\",\"액세스 가능한 Diff 뷰어\",\"다음 다른 항목으로 이동\",\"다음 다른 항목으로 이동\",\"액세스 가능한 Diff 뷰어의 '삽입' 아이콘.\",\"액세스 가능한 Diff 뷰어의 '제거' 아이콘.\",\"접근 가능한 Diff 뷰어의 '닫기' 아이콘.\",\"닫기\",\"액세스 가능한 Diff 뷰어입니다. 탐색하려면 위쪽 및 아래쪽 화살표를 사용합니다.\",\"변경된 줄 없음\",\"선 1개 변경됨\",\"줄 {0}개 변경됨\",\"차이 {0}/{1}: 원래 줄 {2}, {3}, 수정된 줄 {4}, {5}\",\"비어 있음\",\"{0} 변경되지 않은 줄 {1}\",\"{0} 원래 줄 {1} 수정된 줄 {2}\",\"+ {0} 수정된 줄 {1}\",\"- {0} 원래 줄 {1}\",\" {0}을(를) 사용하여 접근성 도움말을 엽니다.\",\"삭제된 줄 복사\",\"삭제된 줄 복사\",\"변경된 줄 복사\",\"변경된 줄 복사\",\"삭제된 줄 복사({0})\",\"변경된 줄({0}) 복사\",\"이 변경 내용 되돌리기\",\"공간이 제한된 경우 인라인 보기 사용\",\"이동된 코드 블록 표시\",\"블록 되돌리기\",\"선택 항목 되돌리기\",\"액세스 가능한 Diff 뷰어 열기\",\"변경되지 않은 영역 접기\",\"숨겨진 선 {0}개\",\"위에 자세히 표시하려면 클릭하거나 끌어다 놓기\",\"변경되지 않은 영역 표시\",\"아래에 자세히 표시하려면 클릭하거나 끌어다 놓기\",\"숨겨진 선 {0}개\",\"두 번 클릭하여 펼치기\",\"변경 사항과 함께 코드가 {0} - {1}줄로 이동됨\",\"변경 사항과 함께 코드가 {0} - {1}줄에서 이동됨\",\"코드가 {0} - {1}줄로 이동됨\",\"코드가 {0} - {1}줄에서 이동됨\",\"선택한 변경 내용 되돌리기\",\"변경 내용 되돌리기\",\"diff 편집기에서 이동된 텍스트의 테두리 색입니다.\",\"diff 편집기에서 이동된 텍스트의 활성 테두리 색입니다.\",\"변경되지 않은 영역 위젯 주위의 그림자 색입니다.\",\"diff 편집기의 삽입에 대한 줄 데코레이션입니다.\",\"diff 편집기의 제거에 대한 줄 데코레이션입니다.\",\"diff 편집기 헤더의 배경색입니다.\",\"다중 파일 diff 편집기 배경색입니다.\",\"다중 파일 차이 편집기의 테두리 색\",\"변경된 파일 없음\",\"편집기\",\"탭이 같은 공백의 수입니다. 이 설정은 {0}이(가) 켜져 있을 때 파일 내용을 기반으로 재정의됩니다.\",\"들여쓰기 또는 `\\\"tabSize\\\"에서 '#editor.tabSize#'의 값을 사용하는 데 사용되는 공백 수입니다. 이 설정은 '#editor.detectIndentation#'이 켜져 있는 경우 파일 내용에 따라 재정의됩니다.\",\"`Tab`을 누를 때 공백을 삽입하세요. 이 설정은 {0}이(가) 켜져 있을 때 파일 내용을 기반으로 재정의됩니다.\",\"파일 내용을 기반으로 파일을 열 때 {0} 및 {1}을(를) 자동으로 감지할지 여부를 제어합니다.\",\"끝에 자동 삽입된 공백을 제거합니다.\",\"큰 파일에 대한 특수 처리로, 메모리를 많이 사용하는 특정 기능을 사용하지 않도록 설정합니다.\",\"단어 기반 추천을 끕니다.\",\"활성 문서에서만 단어를 제안합니다.\",\"같은 언어의 모든 열린 문서에서 단어를 제안합니다.\",\"모든 열린 문서에서 단어를 제안합니다.\",\"문서의 단어를 기준으로 완성도를 계산해야 하는지 여부 및 완성도가 계산되는 문서를 기준으로 계산되는지 여부를 제어합니다.\",\"모든 색 테마에 대해 의미 체계 강조 표시를 사용합니다.\",\"모든 색 테마에 대해 의미 체계 강조 표시를 사용하지 않습니다.\",\"의미 체계 강조 표시는 현재 색 테마의 `semanticHighlighting` 설정에 따라 구성됩니다.\",\"semanticHighlighting이 지원하는 언어에 대해 표시되는지 여부를 제어합니다.\",\"해당 콘텐츠를 두 번 클릭하거나 'Esc' 키를 누르더라도 Peek 편집기를 열린 상태로 유지합니다.\",\"이 길이를 초과하는 줄은 성능상의 이유로 토큰화되지 않습니다.\",\"웹 작업자에서 토큰화가 비동기적으로 수행되어야 하는지 여부를 제어합니다.\",\"비동기 토큰화가 기록되어야 하는지 여부를 제어합니다. 디버깅 전용입니다.\",\"레거시 백그라운드 토큰화에 대해 비동기 토큰화를 확인해야 하는지 여부를 제어합니다. 토큰화 속도가 느려질 수 있습니다. 디버깅 전용입니다.\",\"트리 시터 구문 분석을 설정하고 원격 분석을 수집해야 하는지 여부를 제어합니다. 특정 언어에 대해 'editor.experimental.preferTreeSitter'를 설정하는 것이 우선적으로 적용됩니다.\",\"들여쓰기를 늘리거나 줄이는 대괄호 기호를 정의합니다.\",\"여는 대괄호 문자 또는 문자열 시퀀스입니다.\",\"닫는 대괄호 문자 또는 문자열 시퀀스입니다.\",\"대괄호 쌍 색 지정을 사용하는 경우 중첩 수준에 따라 색이 지정된 대괄호 쌍을 정의합니다.\",\"여는 대괄호 문자 또는 문자열 시퀀스입니다.\",\"닫는 대괄호 문자 또는 문자열 시퀀스입니다.\",\"diff 계산이 취소된 후 밀리초 단위로 시간을 제한합니다. 제한 시간이 없는 경우 0을 사용합니다.\",\"차이를 계산할 최대 파일 크기(MB)입니다. 제한이 없으면 0을 사용합니다.\",\"diff 편집기에서 diff를 나란히 표시할지 인라인으로 표시할지를 제어합니다.\",\"diff 편집기 너비가 이 값보다 작으면 인라인 뷰가 사용됩니다.\",\"사용하도록 설정하고 편집기 너비가 너무 작을 경우 인라인 보기가 사용됩니다.\",\"활성화되면 diff 편집기는 변경 내용을 되돌리기 위해 글리프 여백에 화살표를 표시합니다.\",\"사용하도록 설정하면 diff 편집기에 되돌리기 및 단계 작업을 위한 특수 여백이 표시됩니다.\",\"사용하도록 설정하면 Diff 편집기가 선행 또는 후행 공백의 변경 내용을 무시합니다.\",\"diff 편집기에서 추가/제거된 변경 내용에 대해 +/- 표시기를 표시하는지 여부를 제어합니다.\",\"편집기에서 CodeLens를 표시할 것인지 여부를 제어합니다.\",\"줄이 바뀌지 않습니다.\",\"뷰포트 너비에서 줄이 바뀝니다.\",\"줄은 {0} 설정에 따라 줄 바꿈됩니다.\",\"레거시 비교 알고리즘을 사용합니다.\",\"고급 비교 알고리즘을 사용합니다.\",\"diff 편집기에 변경되지 않은 영역이 표시되는지 여부를 제어합니다.\",\"변경되지 않은 영역에 사용되는 줄 수를 제어합니다.\",\"변경되지 않은 영역의 최소값으로 사용되는 줄 수를 제어합니다.\",\"변경되지 않은 영역을 비교할 때 컨텍스트로 사용되는 줄 수를 제어합니다.\",\"diff 편집기에서 감지된 코드 이동을 표시할지 여부를 제어합니다.\",\"문자가 삽입되거나 삭제된 위치를 볼 수 있도록 diff 편집기에 빈 장식적 요소를 표시할지 여부를 제어합니다.\",\"사용하도록 설정하고 편집기에서 인라인 보기를 사용하는 경우 단어 변경 내용이 인라인으로 렌더링됩니다.\",\"플랫폼 API를 사용하여 화면 읽기 프로그램이 연결된 시기를 감지합니다.\",\"화면 읽기 프로그램을 사용하여 사용을 최적화합니다.\",\"화면 읽기 프로그램이 연결되어 있지 않다고 가정합니다.\",\"화면 판독기에 최적화된 모드에서 UI를 실행해야 하는지 여부를 제어합니다.\",\"주석을 달 때 공백 문자를 삽입할지 여부를 제어합니다.\",\"빈 줄을 줄 주석에 대한 토글, 추가 또는 제거 작업으로 무시해야 하는지를 제어합니다.\",\"선택 영역 없이 현재 줄 복사 여부를 제어합니다.\",\"입력하는 동안 일치 항목을 찾기 위한 커서 이동 여부를 제어합니다.\",\"편집기 선택 영역에서 검색 문자열을 시드하지 마세요.\",\"커서 위치의 단어를 포함하여 항상 편집기 선택 영역에서 검색 문자열을 시드합니다.\",\"편집기 선택 영역에서만 검색 문자열을 시드하세요.\",\"편집기 선택에서 Find Widget의 검색 문자열을 시딩할지 여부를 제어합니다.\",\"선택 영역에서 찾기를 자동으로 켜지 않습니다(기본값).\",\"선택 영역에서 찾기를 항상 자동으로 켭니다.\",\"여러 줄의 콘텐츠를 선택하면 선택 항목에서 찾기가 자동으로 켜집니다.\",\"선택 영역에서 찾기를 자동으로 설정하는 조건을 제어합니다.\",\"macOS에서 Find Widget이 공유 클립보드 찾기를 읽을지 수정할지 제어합니다.\",\"위젯 찾기에서 편집기 맨 위에 줄을 추가해야 하는지 여부를 제어합니다. true인 경우 위젯 찾기가 표시되면 첫 번째 줄 위로 스크롤할 수 있습니다.\",\"더 이상 일치하는 항목이 없을 때 검색을 처음이나 끝에서 자동으로 다시 시작할지 여부를 제어합니다.\",\"글꼴 합자('calt' 및 'liga' 글꼴 기능)를 사용하거나 사용하지 않도록 설정합니다. 'font-feature-settings' CSS 속성의 세분화된 제어를 위해 문자열로 변경합니다.\",\"명시적 'font-feature-settings' CSS 속성입니다. 합자를 켜거나 꺼야 하는 경우에만 부울을 대신 전달할 수 있습니다.\",\"글꼴 합자 또는 글꼴 기능을 구성합니다. CSS 'font-feature-settings' 속성의 값에 대해 합자 또는 문자열을 사용하거나 사용하지 않도록 설정하기 위한 부울일 수 있습니다.\",\"font-weight에서 font-variation-settings로 변환을 사용/사용하지 않습니다. 'font-variation-settings' CSS 속성의 세분화된 컨트롤을 위해 이를 문자열로 변경합니다.\",\"명시적 'font-variation-settings' CSS 속성입니다. font-weight만 font-variation-settings로 변환해야 하는 경우 부울을 대신 전달할 수 있습니다.\",\"글꼴 변형을 구성합니다. font-weight에서 font-variation-settings로 변환을 사용/사용하지 않도록 설정하는 부울이거나 CSS 'font-variation-settings' 속성 값에 대한 문자열일 수 있습니다.\",\"글꼴 크기(픽셀)를 제어합니다.\",\"\\\"표준\\\" 및 \\\"굵게\\\" 키워드 또는 1~1000 사이의 숫자만 허용됩니다.\",\"글꼴 두께를 제어합니다. \\\"표준\\\" 및 \\\"굵게\\\" 키워드 또는 1~1000 사이의 숫자를 허용합니다.\",\"결과의 Peek 보기 표시(기본값)\",\"기본 결과로 이동하여 Peek 보기를 표시합니다.\",\"기본 결과로 이동하여 다른 항목에 대해 Peek 없는 탐색을 사용하도록 설정합니다.\",\"이 설정은 더 이상 사용되지 않습니다. 대신 'editor.editor.gotoLocation.multipleDefinitions' 또는 'editor.editor.gotoLocation.multipleImplementations'와 같은 별도의 설정을 사용하세요.\",\"여러 대상 위치가 있는 경우 '정의로 이동' 명령 동작을 제어합니다.\",\"여러 대상 위치가 있는 경우 '유형 정의로 이동' 명령 동작을 제어합니다.\",\"여러 대상 위치가 있는 경우 'Go to Declaration' 명령 동작을 제어합니다.\",\"여러 대상 위치가 있는 경우 '구현으로 이동' 명령 동작을 제어합니다.\",\"여러 대상 위치가 있는 경우 '참조로 이동' 명령 동작을 제어합니다.\",\"'정의로 이동'의 결과가 현재 위치일 때 실행되는 대체 명령 ID입니다.\",\"'형식 정의로 이동'의 결과가 현재 위치일 때 실행되는 대체 명령 ID입니다.\",\"'선언으로 이동'의 결과가 현재 위치일 때 실행되는 대체 명령 ID입니다.\",\"'구현으로 이동'의 결과가 현재 위치일 때 실행되는 대체 명령 ID입니다.\",\"'참조로 이동'의 결과가 현재 위치일 때 실행되는 대체 명령 ID입니다.\",\"호버 표시 여부를 제어합니다.\",\"호버가 표시되기 전까지의 지연 시간(밀리초)을 제어합니다.\",\"마우스를 해당 항목 위로 이동할 때 호버를 계속 표시할지 여부를 제어합니다.\",\"호버가 숨겨지기 전까지의 지연 시간(밀리초)을 제어합니다. 'editor.hover.sticky'를 사용하도록 설정해야 합니다.\",\"공백이 있는 경우 선 위에 마우스를 가져가는 것을 표시하는 것을 선호합니다.\",\"모든 문자가 동일한 너비라고 가정합니다. 이 알고리즘은 고정 폭 글꼴과 문자 모양의 너비가 같은 특정 스크립트(예: 라틴 문자)에 적절히 작동하는 빠른 알고리즘입니다.\",\"래핑 점 계산을 브라우저에 위임합니다. 이 알고리즘은 매우 느려서 대용량 파일의 경우 중단될 수 있지만 모든 경우에 적절히 작동합니다.\",\"래핑 지점을 계산하는 알고리즘을 제어합니다. 접근성 모드에서는 최상의 환경을 위해 고급 기능이 사용됩니다.\",\"코드 작업 메뉴를 사용하지 않도록 설정합니다.\",\"커서가 코드가 있는 줄에 있을 때 코드 동작 메뉴를 표시합니다.\",\"커서가 코드가 있는 줄 또는 빈 줄에 있는 경우 코드 동작 메뉴를 표시합니다.\",\"편집기에서 코드 동작 전구를 사용하도록 설정합니다.\",\"편집기 위쪽에서 스크롤하는 동안 중첩된 현재 범위를 표시합니다.\",\"표시할 최대 고정 선 수를 정의합니다.\",\"고정할 줄을 결정하는 데 사용할 모델을 정의합니다. 개요 모델이 없으면 들여쓰기 모델에 해당하는 접기 공급자 모델에서 대체됩니다. 이 순서는 세 가지 경우 모두 적용됩니다.\",\"편집기의 가로 스크롤 막대를 사용하여 고정 스크롤 스크롤을 사용하도록 설정합니다.\",\"편집기에서 인레이 힌트를 사용하도록 설정합니다.\",\"인레이 힌트를 사용할 수 있음\",\"인레이 힌트는 기본적으로 표시되고 {0}을(를) 길게 누를 때 숨겨집니다.\",\"인레이 힌트는 기본값으로 숨겨져 있으며 {0}을(를) 길게 누르면 표시됩니다.\",\"인레이 힌트는 사용할 수 없음\",\"편집기에서 인레이 힌트의 글꼴 크기를 제어합니다. 기본적으로 {0}은(는) 구성된 값이 {1}보다 작거나 편집기 글꼴 크기보다 큰 경우에 사용됩니다.\",\"편집기에서 인레이 힌트의 글꼴 패밀리를 제어합니다. 비워 두면 {0}이(가) 사용됩니다.\",\"편집기에서 인레이 힌트 주위의 패딩을 사용하도록 설정합니다.\",\"선 높이를 제어합니다. \\r\\n - 0을 사용하여 글꼴 크기에서 줄 높이를 자동으로 계산합니다.\\r\\n - 0에서 8 사이의 값은 글꼴 크기의 승수로 사용됩니다.\\r\\n - 8보다 크거나 같은 값이 유효 값으로 사용됩니다.\",\"미니맵 표시 여부를 제어합니다.\",\"미니맵을 자동으로 숨길지 여부를 제어합니다.\",\"미니맵의 크기는 편집기 내용과 동일하며 스크롤할 수 있습니다.\",\"편집기의 높이를 맞추기 위해 필요에 따라 미니맵이 확장되거나 축소됩니다(스크롤 없음).\",\"미니맵을 편집기보다 작게 유지할 수 있도록 필요에 따라 미니맵이 축소됩니다(스크롤 없음).\",\"미니맵의 크기를 제어합니다.\",\"미니맵을 렌더링할 측면을 제어합니다.\",\"미니맵 슬라이더가 표시되는 시기를 제어합니다.\",\"미니맵에 그려진 콘텐츠의 배율: 1, 2 또는 3.\",\"줄의 실제 문자(색 블록 아님)를 렌더링합니다.\",\"최대 특정 수의 열을 렌더링하도록 미니맵의 너비를 제한합니다.\",\"명명된 영역을 미니맵에 섹션 머리글로 표시할지 여부를 제어합니다.\",\"MARK: 주석이 미니맵에 섹션 머리글로 표시되는지 여부를 제어합니다.\",\"미니맵에서 섹션 머리글의 글꼴 크기를 제어합니다.\",\"구역 머리글 문자 사이의 간격(픽셀)을 제어합니다. 이렇게 하면 작은 글꼴 크기의 머리글 가독성을 높이는 데 도움이 됩니다.\",\"편집기의 위쪽 가장자리와 첫 번째 줄 사이의 공백을 제어합니다.\",\"편집기의 아래쪽 가장자리와 마지막 줄 사이의 공백을 제어합니다.\",\"입력과 동시에 매개변수 문서와 유형 정보를 표시하는 팝업을 사용하도록 설정합니다.\",\"매개변수 힌트 메뉴의 주기 혹은 목록의 끝에 도달하였을때 종료할 것인지 여부를 결정합니다.\",\"제안 위젯 내부에 빠른 제안이 표시됩니다.\",\"빠른 제안이 유령 텍스트로 표시됨\",\"빠른 제안이 사용 중지되었습니다.\",\"문자열 내에서 빠른 제안을 사용합니다.\",\"주석 내에서 빠른 제안을 사용합니다.\",\"문자열 및 주석 외부에서 빠른 제안을 사용합니다.\",\"입력하는 동안 제안을 자동으로 표시할지 여부를 제어합니다. 이것은 주석, 문자열 및 기타 코드를 입력하기 위해 제어할 수 있습니다. 빠른 제안은 고스트 텍스트 또는 제안 위젯으로 표시하도록 구성할 수 있습니다. 또한 제안이 특수 문자에 의해 실행되는지 여부를 제어하는 {0}-설정에 유의하세요.\",\"줄 번호는 렌더링되지 않습니다.\",\"줄 번호는 절대값으로 렌더링 됩니다.\",\"줄 번호는 커서 위치에서 줄 간격 거리로 렌더링 됩니다.\",\"줄 번호는 매 10 줄마다 렌더링이 이루어집니다.\",\"줄 번호의 표시 여부를 제어합니다.\",\"이 편집기 눈금자에서 렌더링할 고정 폭 문자 수입니다.\",\"이 편집기 눈금자의 색입니다.\",\"특정 수의 고정 폭 문자 뒤에 세로 눈금자를 렌더링합니다. 여러 눈금자의 경우 여러 값을 사용합니다. 배열이 비어 있는 경우 눈금자가 그려지지 않습니다.\",\"세로 스크롤 막대는 필요한 경우에만 표시됩니다.\",\"세로 스크롤 막대가 항상 표시됩니다.\",\"세로 스크롤 막대를 항상 숨깁니다.\",\"세로 스크롤 막대의 표시 유형을 제어합니다.\",\"가로 스크롤 막대는 필요한 경우에만 표시됩니다.\",\"가로 스크롤 막대가 항상 표시됩니다.\",\"가로 스크롤 막대를 항상 숨깁니다.\",\"가로 스크롤 막대의 표시 유형을 제어합니다.\",\"세로 스크롤 막대의 너비입니다.\",\"가로 스크롤 막대의 높이입니다.\",\"클릭이 페이지별로 스크롤되는지 또는 클릭 위치로 이동할지 여부를 제어합니다.\",\"설정하면 가로 스크롤 막대가 편집기 콘텐츠의 크기를 늘리지 않습니다.\",\"기본이 아닌 모든 ASCII 문자를 강조 표시할지 여부를 제어합니다. U+0020과 U+007E 사이의 문자, 탭, 줄 바꿈 및 캐리지 리턴만 기본 ASCII로 간주됩니다.\",\"공백만 예약하거나 너비가 전혀 없는 문자를 강조 표시할지 여부를 제어합니다.\",\"현재 사용자 로캘에서 공통되는 문자를 제외한 기본 ASCII 문자와 혼동할 수 있는 문자를 강조 표시할지 여부를 제어합니다.\",\"주석의 문자에도 유니코드 강조 표시를 적용해야 하는지 여부를 제어합니다.\",\"문자열의 문자에도 유니코드 강조 표시를 적용해야 하는지 여부를 제어합니다.\",\"강조 표시되지 않는 허용된 문자를 정의합니다.\",\"허용된 로캘에서 공통적인 유니코드 문자는 강조 표시되지 않습니다.\",\"편집기에서 인라인 제안을 자동으로 표시할지 여부를 제어합니다.\",\"인라인 추천을 표시힐 때마다 인라인 추천 도구 모음을 표시합니다.\",\"인라인 추천을 마우스로 가리키면 인라인 추천 도구 모음을 표시합니다.\",\"인라인 추천 도구 모음을 표시하지 않습니다.\",\"인라인 추천 도구 모음을 표시할 시기를 제어합니다.\",\"인라인 제안이 제안 위젯과 상호 작용하는 방법을 제어합니다. 사용하도록 설정하면 인라인 제안을 사용할 수 있을 때 제안 위젯이 자동으로 표시되지 않습니다.\",\"인라인 제안의 글꼴 패밀리를 제어합니다.\",null,null,null,null,null,null,\"대괄호 쌍 색 지정을 사용할지 여부를 제어합니다. {0}을(를) 사용하여 대괄호 강조 색을 재정의합니다.\",\"각 대괄호 형식에 고유한 독립적인 색 풀이 있는지 여부를 제어합니다.\",\"대괄호 쌍 가이드를 사용하도록 설정합니다.\",\"활성 대괄호 쌍에 대해서만 대괄호 쌍 가이드를 사용하도록 설정합니다.\",\"대괄호 쌍 가이드를 비활성화합니다.\",\"대괄호 쌍 안내선의 사용 여부를 제어합니다.\",\"수직 대괄호 쌍 가이드에 추가하여 수평 가이드를 사용하도록 설정합니다.\",\"활성 대괄호 쌍에 대해서만 수평 가이드를 사용하도록 설정합니다.\",\"수평 대괄호 쌍 가이드를 비활성화합니다.\",\"가로 대괄호 쌍 안내선의 사용 여부를 제어합니다.\",\"편집기가 활성 브래킷 쌍을 강조 표시해야 하는지 여부를 제어합니다.\",\"편집기에서 들여쓰기 가이드를 렌더링할지를 제어합니다.\",\"활성 들여쓰기 안내선을 강조 표시합니다.\",\"브래킷 안내선이 강조 표시된 경우에도 활성 들여쓰기 안내선을 강조 표시합니다.\",\"활성 들여쓰기 안내선을 강조 표시하지 마세요.\",\"편집기에서 활성 들여쓰기 가이드를 강조 표시할지 여부를 제어합니다.\",\"커서의 텍스트 오른쪽을 덮어 쓰지않고 제안을 삽입합니다.\",\"제안을 삽입하고 커서의 오른쪽 텍스트를 덮어씁니다.\",\"완료를 수락할 때 단어를 덮어쓸지 여부를 제어합니다. 이것은 이 기능을 선택하는 확장에 따라 다릅니다.\",\"제안 필터링 및 정렬에서 작은 오타를 설명하는지 여부를 제어합니다.\",\"정렬할 때 커서 근처에 표시되는 단어를 우선할지를 제어합니다.\",\"저장된 제안 사항 선택 항목을 여러 작업 영역 및 창에서 공유할 것인지 여부를 제어합니다(`#editor.suggestSelection#` 필요).\",\"IntelliSense를 자동으로 트리거할 때 항상 제안을 선택합니다.\",\"IntelliSense를 자동으로 트리거할 때 제안을 선택하지 마세요.\",\"트리거 문자에서 IntelliSense를 트리거할 때만 제안을 선택합니다.\",\"입력할 때 IntelliSense를 트리거할 때만 제안을 선택합니다.\",\"위젯이 표시될 때 제안을 선택할지 여부를 제어합니다. 이는 자동으로 트리거된 제안({0} 및 {1})에만 적용되며, 제안이 명시적으로 호출될 때 항상 선택됩니다(예: 'Ctrl+Space'를 통해).\",\"활성 코드 조각이 빠른 제안을 방지하는지 여부를 제어합니다.\",\"제안의 아이콘을 표시할지 여부를 제어합니다.\",\"제안 위젯 하단의 상태 표시줄 가시성을 제어합니다.\",\"편집기에서 제안 결과를 미리볼지 여부를 제어합니다.\",\"제안 세부 정보가 레이블과 함께 인라인에 표시되는지 아니면 세부 정보 위젯에만 표시되는지를 제어합니다.\",\"이 설정은 더 이상 사용되지 않습니다. 이제 제안 위젯의 크기를 조정할 수 있습니다.\",\"이 설정은 더 이상 사용되지 않습니다. 대신 'editor.suggest.showKeywords'또는 'editor.suggest.showSnippets'와 같은 별도의 설정을 사용하세요.\",\"사용하도록 설정되면 IntelliSense에 `메서드` 제안이 표시됩니다.\",\"사용하도록 설정되면 IntelliSense에 '함수' 제안이 표시됩니다.\",\"사용하도록 설정되면 IntelliSense에 '생성자' 제안이 표시됩니다.\",\"사용하도록 설정되면 IntelliSense에 '사용되지 않음' 제안이 표시됩니다.\",\"IntelliSense 필터링을 활성화하면 첫 번째 문자가 단어 시작 부분과 일치해야 합니다(예: `c`의 경우 `Console` 또는 `WebContext`가 될 수 있으며 `description`은 _안 됨_). 비활성화하면 IntelliSense가 더 많은 결과를 표시하지만 여전히 일치 품질을 기준으로 정렬합니다.\",\"사용하도록 설정되면 IntelliSense에 '필드' 제안이 표시됩니다.\",\"사용하도록 설정되면 IntelliSense에 '변수' 제안이 표시됩니다.\",\"사용하도록 설정되면 IntelliSense에 '클래스' 제안이 표시됩니다.\",\"사용하도록 설정되면 IntelliSense에 '구조' 제안이 표시됩니다.\",\"사용하도록 설정되면 IntelliSense에 '인터페이스' 제안이 표시됩니다.\",\"사용하도록 설정되면 IntelliSense에 '모듈' 제안이 표시됩니다.\",\"사용하도록 설정되면 IntelliSense에 '속성' 제안이 표시됩니다.\",\"사용하도록 설정되면 IntelliSense에 '이벤트' 제안이 표시됩니다.\",\"사용하도록 설정되면 IntelliSense에 `연산자` 제안이 표시됩니다.\",\"사용하도록 설정되면 IntelliSense에 '단위' 제안이 표시됩니다.\",\"사용하도록 설정되면 IntelliSense에 '값' 제안이 표시됩니다.\",\"사용하도록 설정되면 IntelliSense에 '상수' 제안이 표시됩니다.\",\"사용하도록 설정되면 IntelliSense에 '열거형' 제안이 표시됩니다.\",\"사용하도록 설정되면 IntelliSense에 `enumMember` 제안이 표시됩니다.\",\"사용하도록 설정되면 IntelliSense에 '키워드' 제안이 표시됩니다.\",\"사용하도록 설정되면 IntelliSense에 '텍스트' 제안이 표시됩니다.\",\"사용하도록 설정되면 IntelliSense에 '색' 제안이 표시됩니다.\",\"사용하도록 설정되면 IntelliSense에 `파일` 제안이 표시됩니다.\",\"사용하도록 설정되면 IntelliSense에 '참조' 제안이 표시됩니다.\",\"사용하도록 설정되면 IntelliSense에 '사용자 지정 색' 제안이 표시됩니다.\",\"사용하도록 설정되면 IntelliSense에 '폴더' 제안이 표시됩니다.\",\"사용하도록 설정된 경우 IntelliSense에 'typeParameter' 제안이 표시됩니다.\",\"사용하도록 설정되면 IntelliSense에 '코드 조각' 제안이 표시됩니다.\",\"IntelliSense를 사용하도록 설정하면 `user`-제안이 표시됩니다.\",\"IntelliSense를 사용하도록 설정한 경우 `issues`-제안을 표시합니다.\",\"선행 및 후행 공백을 항상 선택해야 하는지 여부입니다.\",\"하위 단어(예: 'fooBar'의 'foo' 또는 'foo_bar')를 선택해야 하는지 여부입니다.\",\"단어 관련 탐색 또는 작업을 수행할 때 단어 구분에 사용할 로캘입니다. 인식할 단어의 BCP 47 언어 태그를 지정합니다(예: ja, zh-CN, zh-Hant-TW 등).\",\"단어 관련 탐색 또는 작업을 수행할 때 단어 구분에 사용할 로캘입니다. 인식할 단어의 BCP 47 언어 태그를 지정합니다(예: ja, zh-CN, zh-Hant-TW 등).\",\"들여쓰기가 없습니다. 줄 바꿈 행이 열 1에서 시작됩니다.\",\"줄 바꿈 행의 들여쓰기가 부모와 동일합니다.\",\"줄 바꿈 행이 부모 쪽으로 +1만큼 들여쓰기됩니다.\",\"줄 바꿈 행이 부모 쪽으로 +2만큼 들여쓰기됩니다.\",\"줄 바꿈 행의 들여쓰기를 제어합니다.\",\"편집기에서 파일을 여는 대신 `shift`를 누른 채 파일을 텍스트 편집기로 끌어서 놓을 수 있는지 여부를 제어합니다.\",\"편집기에 파일을 끌어 놓을 때 위젯을 표시할지 여부를 제어합니다. 이 위젯을 사용하면 파일을 드롭하는 방법을 제어할 수 있습니다.\",\"파일이 편집기에 드롭된 후 드롭 선택기 위젯을 표시합니다.\",\"드롭 선택기 위젯을 표시하지 않습니다. 대신 기본 드롭 공급자가 항상 사용됩니다.\",\"콘텐츠를 다른 방법으로 붙여넣을 수 있는지 여부를 제어합니다.\",\"콘텐츠를 편집기에 붙여넣을 때 위젯을 표시할지 여부를 제어합니다. 이 위젯을 사용하여 파일을 붙여넣는 방법을 제어할 수 있습니다.\",\"콘텐츠를 편집기에 붙여넣은 후 붙여넣기 선택기 위젯을 표시합니다.\",\"붙여넣기 선택기 위젯을 표시하지 않습니다. 대신 기본 붙여넣기 동작이 항상 사용됩니다.\",\"커밋 문자에 대한 제안을 허용할지를 제어합니다. 예를 들어 JavaScript에서는 세미콜론(';')이 제안을 허용하고 해당 문자를 입력하는 커밋 문자일 수 있습니다.\",\"텍스트를 변경할 때 `Enter` 키를 사용한 제안만 허용합니다.\",\"'Tab' 키 외에 'Enter' 키에 대한 제안도 허용할지를 제어합니다. 새 줄을 삽입하는 동작과 제안을 허용하는 동작 간의 모호함을 없앨 수 있습니다.\",\"화면 읽기 프로그램에서 한 번에 읽을 수 있는 편집기 줄 수를 제어합니다. 화면 읽기 프로그램을 검색하면 기본값이 500으로 자동 설정됩니다. 경고: 기본값보다 큰 수의 경우 성능에 영향을 미칩니다.\",\"편집기 콘텐츠\",\"화면 읽기 프로그램에서 인라인 제안을 발표하는지 여부를 제어합니다.\",\"언어 구성을 사용하여 대괄호를 자동으로 닫을 경우를 결정합니다.\",\"커서가 공백의 왼쪽에 있는 경우에만 대괄호를 자동으로 닫습니다.\",\"사용자가 여는 괄호를 추가한 후 편집기에서 괄호를 자동으로 닫을지 여부를 제어합니다.\",\"언어 구성을 사용하여 주석을 자동으로 닫을 경우를 결정합니다.\",\"커서가 공백의 왼쪽에 있는 경우에만 주석을 자동으로 닫습니다.\",\"사용자가 여는 주석을 추가한 후 편집기에서 주석을 자동으로 닫을지 여부를 제어합니다.\",\"인접한 닫는 따옴표 또는 대괄호가 자동으로 삽입된 경우에만 제거합니다.\",\"삭제할 때 편집기에서 인접한 닫는 따옴표 또는 대괄호를 제거해야 할지를 제어합니다.\",\"닫기 따옴표 또는 대괄호가 자동으로 삽입된 경우에만 해당 항목 위에 입력합니다.\",\"편집자가 닫는 따옴표 또는 대괄호 위에 입력할지 여부를 제어합니다.\",\"언어 구성을 사용하여 따옴표를 자동으로 닫을 경우를 결정합니다.\",\"커서가 공백의 왼쪽에 있는 경우에만 따옴표를 자동으로 닫습니다.\",\"사용자가 여는 따옴표를 추가한 후 편집기에서 따옴표를 자동으로 닫을지 여부를 제어합니다.\",\"편집기는 들여쓰기를 자동으로 삽입하지 않습니다.\",\"편집기는 현재 줄의 들여쓰기를 유지합니다.\",\"편집기는 현재 줄의 들여쓰기를 유지하고 언어 정의 대괄호를 사용합니다.\",\"편집기는 현재 줄의 들여쓰기를 유지하고 언어 정의 대괄호를 존중하며 언어별로 정의된 특별 EnterRules를 호출합니다.\",\"편집기는 현재 줄의 들여쓰기를 유지하고, 언어 정의 대괄호를 존중하고, 언어에 의해 정의된 특별 EnterRules를 호출하고, 언어에 의해 정의된 들여쓰기 규칙을 존중합니다.\",\"사용자가 줄을 입력, 붙여넣기, 이동 또는 들여쓰기 할 때 편집기에서 들여쓰기를 자동으로 조정하도록 할지 여부를 제어합니다.\",\"언어 구성을 사용하여 선택 항목을 자동으로 둘러쌀 경우를 결정합니다.\",\"대괄호가 아닌 따옴표로 둘러쌉니다.\",\"따옴표가 아닌 대괄호로 둘러쌉니다.\",\"따옴표 또는 대괄호 입력 시 편집기가 자동으로 선택 영역을 둘러쌀지 여부를 제어합니다.\",\"들여쓰기에 공백을 사용할 때 탭 문자의 선택 동작을 에뮬레이트합니다. 선택 영역이 탭 정지에 고정됩니다.\",\"편집기에서 CodeLens를 표시할 것인지 여부를 제어합니다.\",\"CodeLens의 글꼴 패밀리를 제어합니다.\",\"CodeLens의 글꼴 크기(픽셀)를 제어합니다. 0으로 설정하면 `#editor.fontSize#`의 90%가 사용됩니다.\",\"편집기에서 인라인 색 데코레이터 및 색 선택을 렌더링할지를 제어합니다.\",\"색 데코레이터를 클릭하고 마우스로 가리킬 때 색 선택기를 표시합니다.\",\"색 데코레이터를 마우스로 가리키면 색 선택기가 표시되도록 설정\",\"색 데코레이터를 클릭할 때 색 선택기를 표시합니다.\",\"색 데코레이터에서 색 선택기를 표시하도록 조건을 제어합니다.\",\"편집기에서 한 번에 렌더링할 수 있는 최대 색 데코레이터 수를 제어합니다.\",\"마우스와 키로 선택한 영역에서 열을 선택하도록 설정합니다.\",\"구문 강조 표시를 클립보드로 복사할지 여부를 제어합니다.\",\"커서 애니메이션 스타일을 제어합니다.\",\"부드러운 캐럿 애니메이션을 사용할 수 없습니다.\",\"부드러운 캐럿 애니메이션은 사용자가 명시적 제스처를 사용하여 커서를 이동할 때만 사용됩니다.\",\"부드러운 캐럿 애니메이션은 항상 사용됩니다.\",\"매끄러운 캐럿 애니메이션의 사용 여부를 제어합니다.\",\"삽입 입력 모드에서 커서 스타일을 제어합니다.\",\"커서 주변에 표시되는 선행 줄(최소 0)과 후행 줄(최소 1)의 최소 수를 제어합니다. 일부 다른 편집기에서는 'scrollOff' 또는 'scrollOffset'으로 알려져 있습니다.\",\"'cursorSurroundingLines'는 키보드 나 API를 통해 트리거될 때만 적용됩니다.\",\"`cursorSurroundingLines`는 항상 적용됩니다.\",\"`#editor.cursorSurroundingLines#`를 적용해야 하는 경우를 제어합니다.\",\"`#editor.cursorStyle#` 설정이 'line'으로 설정되어 있을 때 커서의 넓이를 제어합니다.\",\"편집기에서 끌어서 놓기로 선택 영역을 이동할 수 있는지 여부를 제어합니다.\",\"svgs와 함께 새 렌더링 메서드를 사용합니다.\",\"글꼴 문자와 함께 새 렌더링 방법을 사용합니다.\",\"안정적인 렌더링 방법을 사용합니다.\",\"공백이 새로운 실험적 메서드로 렌더링되는지 여부를 제어합니다.\",\"'Alt' 키를 누를 때 스크롤 속도 승수입니다.\",\"편집기에 코드 접기가 사용하도록 설정되는지 여부를 제어합니다.\",\"사용 가능한 경우 언어별 접기 전략을 사용합니다. 그렇지 않은 경우 들여쓰기 기반 전략을 사용합니다.\",\"들여쓰기 기반 접기 전략을 사용합니다.\",\"접기 범위를 계산하기 위한 전략을 제어합니다.\",\"편집기에서 접힌 범위를 강조 표시할지 여부를 제어합니다.\",\"편집기에서 가져오기 범위를 자동으로 축소할지 여부를 제어합니다.\",\"폴더블 영역의 최대 수입니다. 현재 원본에 폴더블 영역이 많을 때 이 값을 늘리면 편집기의 반응이 떨어질 수 있습니다.\",\"접힌 줄이 줄을 펼친 후 빈 콘텐츠를 클릭할지 여부를 제어합니다.\",\"글꼴 패밀리를 제어합니다.\",\"붙여넣은 콘텐츠의 서식을 편집기에서 자동으로 지정할지 여부를 제어합니다. 포맷터를 사용할 수 있어야 하며 포맷터가 문서에서 범위의 서식을 지정할 수 있어야 합니다.\",\"입력 후 편집기에서 자동으로 줄의 서식을 지정할지 여부를 제어합니다.\",\"편집기에서 세로 문자 모양 여백을 렌더링할지 여부를 제어합니다. 문자 모양 여백은 주로 디버깅에 사용됩니다.\",\"커서가 개요 눈금자에서 가려져야 하는지 여부를 제어합니다.\",\"문자 간격(픽셀)을 제어합니다.\",\"편집기에서 연결된 편집이 사용하도록 설정되었는지를 제어합니다. 언어에 따라 관련 기호(예: HTML 태그)가 편집 중에 업데이트됩니다.\",\"편집기에서 링크를 감지하고 클릭할 수 있게 만들지 여부를 제어합니다.\",\"일치하는 대괄호를 강조 표시합니다.\",\"마우스 휠 스크롤 이벤트의 `deltaX` 및 `deltaY`에서 사용할 승수입니다.\",\"마우스 휠을 사용할 때 'Cmd` 키를 누르고 있으면 편집기의 글꼴을 확대/축소합니다.\",\"마우스 휠을 사용할 때 'Ctrl' 키를 누르고 있으면 편집기의 글꼴을 확대/축소합니다.\",\"여러 커서가 겹치는 경우 커서를 병합합니다.\",\"Windows와 Linux의 'Control'을 macOS의 'Command'로 매핑합니다.\",\"Windows와 Linux의 'Alt'를 macOS의 'Option'으로 매핑합니다.\",\"마우스로 여러 커서를 추가할 때 사용할 수정자입니다. [정의로 이동] 및 [링크 열기] 마우스 제스처가 [멀티커서 수정자와](https://code.visualstudio.com/docs/editor/codebasics#_multicursor-modifier) 충돌하지 않도록 조정됩니다.\",\"각 커서는 텍스트 한 줄을 붙여넣습니다.\",\"각 커서는 전체 텍스트를 붙여넣습니다.\",\"붙여넣은 텍스트의 줄 수가 커서 수와 일치하는 경우 붙여넣기를 제어합니다.\",\"한 번에 활성 편집기에 있을 수 있는 최대 커서 수를 제어합니다.\",\"발생 항목을 강조 표시하지 않습니다.\",\"현재 파일의 발생 항목만 강조 표시합니다.\",\"실험적: 모든 유효한 열린 파일에서 발생 항목을 강조 표시합니다.\",\"열린 파일 전체에서 발생 수를 강조 표시할지 여부를 제어합니다.\",\"개요 눈금자 주위에 테두리를 그릴지 여부를 제어합니다.\",\"Peek를 여는 동안 트리에 포커스\",\"미리 보기를 열 때 편집기에 포커스\",\"미리 보기 위젯에서 인라인 편집기에 포커스를 둘지 또는 트리에 포커스를 둘지를 제어합니다.\",\"이동 정의 마우스 제스처가 항상 미리 보기 위젯을 열지 여부를 제어합니다.\",\"빠른 제안을 표시하기 전까지의 지연 시간(밀리초)을 제어합니다.\",\"편집기가 유형에 따라 자동으로 이름을 바꿀지 여부를 제어합니다.\",\"사용되지 않습니다. 대신 `editor.linkedEditing`을 사용하세요.\",\"편집기에서 제어 문자를 렌더링할지를 제어합니다.\",\"파일이 줄 바꿈으로 끝나면 마지막 줄 번호를 렌더링합니다.\",\"제본용 여백과 현재 줄을 모두 강조 표시합니다.\",\"편집기가 현재 줄 강조 표시를 렌더링하는 방식을 제어합니다.\",\"편집기에 포커스가 있는 경우에만 편집기에서 현재 줄 강조 표시를 렌더링해야 하는지 제어합니다.\",\"단어 사이의 공백 하나를 제외한 공백 문자를 렌더링합니다.\",\"선택한 텍스트에서만 공백 문자를 렌더링합니다.\",\"후행 공백 문자만 렌더링합니다.\",\"편집기에서 공백 문자를 렌더링할 방법을 제어합니다.\",\"선택 항목의 모서리를 둥글게 할지 여부를 제어합니다.\",\"편집기에서 가로로 스크롤되는 범위를 벗어나는 추가 문자의 수를 제어합니다.\",\"편집기에서 마지막 줄 이후로 스크롤할지 여부를 제어합니다.\",\"세로와 가로로 동시에 스크롤할 때에만 주축을 따라서 스크롤합니다. 트랙패드에서 세로로 스크롤할 때 가로 드리프트를 방지합니다.\",\"Linux 주 클립보드의 지원 여부를 제어합니다.\",\"편집기가 선택 항목과 유사한 일치 항목을 강조 표시해야하는지 여부를 제어합니다.\",\"접기 컨트롤을 항상 표시합니다.\",\"접기 컨트롤을 표시하지 않고 여백 크기를 줄이세요.\",\"마우스가 여백 위에 있을 때에만 접기 컨트롤을 표시합니다.\",\"여백의 접기 컨트롤이 표시되는 시기를 제어합니다.\",\"사용하지 않는 코드의 페이드 아웃을 제어합니다.\",\"취소선 사용되지 않는 변수를 제어합니다.\",\"다른 제안 위에 조각 제안을 표시합니다.\",\"다른 제안 아래에 조각 제안을 표시합니다.\",\"다른 제안과 함께 조각 제안을 표시합니다.\",\"코드 조각 제안을 표시하지 않습니다.\",\"코드 조각이 다른 추천과 함께 표시되는지 여부 및 정렬 방법을 제어합니다.\",\"편집기에서 애니메이션을 사용하여 스크롤할지 여부를 제어합니다.\",\"인라인 완성이 표시될 때 화면 읽기 프로그램 사용자에게 접근성 힌트를 제공해야 하는지 여부를 제어합니다.\",\"제안 위젯의 글꼴 크기입니다. {0}(으)로 설정하면 {1} 값이 사용됩니다.\",\"제안 위젯의 줄 높이입니다. {0}(으)로 설정하면 {1} 값이 사용됩니다. 최소값은 8입니다.\",\"트리거 문자를 입력할 때 제안을 자동으로 표시할지 여부를 제어합니다.\",\"항상 첫 번째 제안을 선택합니다.\",\"`log`가 최근에 완료되었으므로 추가 입력에서 제안을 선택하지 않은 경우 최근 제안을 선택하세요(예: `console.| -> console.log`).\",\"해당 제안을 완료한 이전 접두사에 따라 제안을 선택합니다(예: `co -> console` 및 `con -> const`).\",\"제안 목록을 표시할 때 제한이 미리 선택되는 방식을 제어합니다.\",\"탭 완료는 탭을 누를 때 가장 일치하는 제안을 삽입합니다.\",\"탭 완성을 사용하지 않도록 설정합니다.\",\"접두사가 일치하는 경우 코드 조각을 탭 완료합니다. 'quickSuggestions'를 사용하지 않을 때 가장 잘 작동합니다.\",\"탭 완성을 사용하도록 설정합니다.\",\"비정상적인 줄 종결자가 자동으로 제거됩니다.\",\"비정상적인 줄 종결자가 무시됩니다.\",\"제거할 비정상적인 줄 종결자 프롬프트입니다.\",\"문제를 일으킬 수 있는 비정상적인 줄 종결자를 제거합니다.\",\"탭 정지에 맞춰 공백과 탭이 삽입되고 삭제됩니다.\",\"기본 줄 바꿈 규칙을 사용합니다.\",\"단어 분리는 중국어/일본어/한국어(CJK) 텍스트에 사용할 수 없습니다. CJK가 아닌 텍스트 동작은 일반 텍스트 동작과 같습니다.\",\"중국어/일본어/한국어(CJK) 텍스트에 사용되는 단어 분리 규칙을 제어합니다.\",\"단어 관련 탐색 또는 작업을 수행할 때 단어 구분 기호로 사용할 문자입니다.\",\"줄이 바뀌지 않습니다.\",\"뷰포트 너비에서 줄이 바뀝니다.\",\"`#editor.wordWrapColumn#`에서 줄이 바뀝니다.\",\"뷰포트의 최소값 및 `#editor.wordWrapColumn#`에서 줄이 바뀝니다.\",\"줄 바꿈 여부를 제어합니다.\",\"`#editor.wordWrap#`이 `wordWrapColumn` 또는 'bounded'인 경우 편집기의 열 줄 바꿈을 제어합니다.\",\"기본 문서 색 공급자를 사용하여 인라인 색 장식을 표시할지 여부를 제어합니다.\",\"편집기에서 탭을 받을지 또는 탐색을 위해 워크벤치로 미룰지를 제어합니다.\",\"커서 위치의 줄 강조 표시에 대한 배경색입니다.\",\"커서 위치의 줄 테두리에 대한 배경색입니다.\",\"빠른 열기 및 찾기 기능 등을 통해 강조 표시된 영역의 배경색입니다. 기본 장식을 숨기지 않도록 색은 불투명하지 않아야 합니다.\",\"강조 영역 주변의 테두리에 대한 배경색입니다\",\"강조 표시된 기호(예: 정의로 이동 또는 다음/이전 기호로 이동)의 배경색입니다. 이 색상은 기본 장식을 숨기지 않도록 불투명하지 않아야 합니다.\",\"강조 표시된 기호 주위의 테두리 배경색입니다.\",\"편집기 커서 색입니다.\",\"편집기 커서의 배경색입니다. 블록 커서와 겹치는 글자의 색상을 사용자 정의할 수 있습니다.\",\"여러 커서가 있는 경우 기본 편집기 커서의 색입니다.\",\"여러 커서가 있는 경우 기본 편집기 커서의 배경색입니다. 블록 커서와 겹치는 문자의 색을 사용자 지정할 수 있습니다.\",\"여러 커서가 있는 경우 보조 편집기 커서의 색입니다.\",\"여러 커서가 있는 경우 보조 편집기 커서의 배경색입니다. 블록 커서와 겹치는 문자의 색을 사용자 지정할 수 있습니다.\",\"편집기의 공백 문자 색입니다.\",\"편집기 줄 번호 색입니다.\",\"편집기 들여쓰기 안내선 색입니다.\",\"'editorIndentGuide.background'는 더 이상 사용되지 않습니다. 대신 'editorIndentGuide.background1'을 사용하세요.\",\"활성 편집기 들여쓰기 안내선 색입니다.\",\"'editorIndentGuide.activeBackground'는 더 이상 사용되지 않습니다. 대신 'editorIndentGuide.activeBackground1'을 사용하세요.\",\"편집기 들여쓰기 안내선 색(1).\",\"편집기 들여쓰기 안내선 색(2).\",\"편집기 들여쓰기 안내선 색(3).\",\"편집기 들여쓰기 안내선 색(4).\",\"편집기 들여쓰기 안내선 색(5).\",\"편집기 들여쓰기 안내선 색(6).\",\"활성 편집기 들여쓰기 안내선 색(1).\",\"활성 편집기 들여쓰기 안내선 색(2).\",\"활성 편집기 들여쓰기 안내선 색(3).\",\"활성 편집기 들여쓰기 안내선 색(4).\",\"활성 편집기 들여쓰기 안내선 색(5).\",\"활성 편집기 들여쓰기 안내선 색(6).\",\"편집기 활성 영역 줄번호 색상\",\"ID는 사용되지 않습니다. 대신 'editorLineNumber.activeForeground'를 사용하세요.\",\"편집기 활성 영역 줄번호 색상\",\"editor.renderFinalNewline이 흐리게 설정된 경우 최종 편집기 줄의 색입니다.\",\"편집기 눈금의 색상입니다.\",\"편집기 코드 렌즈의 전경색입니다.\",\"일치하는 괄호 뒤의 배경색\",\"일치하는 브래킷 박스의 색상\",\"개요 눈금 경계의 색상입니다.\",\"편집기 개요 눈금자의 배경색입니다.\",\"편집기 거터의 배경색입니다. 거터에는 글리프 여백과 행 수가 있습니다.\",\"편집기의 불필요한(사용하지 않는) 소스 코드 테두리 색입니다.\",\"편집기의 불필요한(사용하지 않는) 소스 코드 불투명도입니다. 예를 들어 \\\"#000000c0\\\"은 75% 불투명도로 코드를 렌더링합니다. 고대비 테마의 경우 페이드 아웃하지 않고 'editorUnnecessaryCode.border' 테마 색을 사용하여 불필요한 코드에 밑줄을 그으세요.\",\"편집기에서 고스트 텍스트의 테두리 색입니다.\",\"편집기에서 고스트 텍스트의 전경색입니다.\",\"편집기에서 고스트 텍스트의 배경색입니다.\",\"범위의 개요 눈금자 표식 색이 강조 표시됩니다. 기본 장식을 숨기지 않도록 색은 불투명하지 않아야 합니다.\",\"오류의 개요 눈금자 마커 색입니다.\",\"경고의 개요 눈금자 마커 색입니다.\",\"정보의 개요 눈금자 마커 색입니다.\",\"대괄호의 전경색(1)입니다. 대괄호 쌍 색 지정을 사용하도록 설정해야 합니다.\",\"대괄호의 전경색(2)입니다. 대괄호 쌍 색 지정을 사용하도록 설정해야 합니다.\",\"대괄호의 전경색(3)입니다. 대괄호 쌍 색 지정을 사용하도록 설정해야 합니다.\",\"대괄호의 전경색(4)입니다. 대괄호 쌍 색 지정을 사용하도록 설정해야 합니다.\",\"대괄호의 전경색(5)입니다. 대괄호 쌍 색 지정을 사용하도록 설정해야 합니다.\",\"대괄호의 전경색(6)입니다. 대괄호 쌍 색 지정을 사용하도록 설정해야 합니다.\",\"예기치 않은 대괄호의 전경색입니다.\",\"비활성 대괄호 쌍 안내선의 배경색입니다(1). 대괄호 쌍 안내선을 사용하도록 설정해야 합니다.\",\"비활성 대괄호 쌍 안내선의 배경색입니다(2). 대괄호 쌍 안내선을 사용하도록 설정해야 합니다.\",\"비활성 대괄호 쌍 안내선의 배경색입니다(3). 대괄호 쌍 안내선을 사용하도록 설정해야 합니다.\",\"비활성 대괄호 쌍 안내선의 배경색입니다(4). 대괄호 쌍 안내선을 사용하도록 설정해야 합니다.\",\"비활성 대괄호 쌍 안내선의 배경색입니다(5). 대괄호 쌍 안내선을 사용하도록 설정해야 합니다.\",\"비활성 대괄호 쌍 안내선의 배경색입니다(6). 대괄호 쌍 안내선을 사용하도록 설정해야 합니다.\",\"활성 대괄호 쌍 안내선의 배경색입니다(1). 대괄호 쌍 안내선을 사용하도록 설정해야 합니다.\",\"활성 대괄호 쌍 안내선의 배경색입니다(2). 대괄호 쌍 안내선을 사용하도록 설정해야 합니다.\",\"활성 대괄호 쌍 안내선의 배경색입니다(3). 대괄호 쌍 안내선을 사용하도록 설정해야 합니다.\",\"활성 대괄호 쌍 안내선의 배경색입니다(4). 대괄호 쌍 안내선을 사용하도록 설정해야 합니다.\",\"활성 대괄호 쌍 안내선의 배경색입니다(5). 대괄호 쌍 안내선을 사용하도록 설정해야 합니다.\",\"활성 대괄호 쌍 안내선의 배경색입니다(6). 대괄호 쌍 안내선을 사용하도록 설정해야 합니다.\",\"유니코드 문자를 강조 표시하는 데 사용되는 테두리 색입니다.\",\"유니코드 문자를 강조 표시하는 데 사용되는 배경색입니다.\",\"편집기 텍스트에 포커스가 있는지 여부(커서가 깜박임)\",\"편집기 또는 편집기 위젯에 포커스가 있는지 여부(예: 포커스가 찾기 위젯에 있음)\",\"편집기 또는 서식 있는 텍스트 입력에 포커스가 있는지 여부(커서가 깜박임)\",\"편집기가 읽기 전용인지 여부\",\"컨텍스트가 diff 편집기인지 여부\",\"컨텍스트가 포함된 diff 편집기인지 여부\",null,\"다중 diff 편집기의 모든 파일이 축소되는지 여부\",\"diff 편집기에 변경 사항이 있는지 여부\",\"이동된 코드 블록이 비교를 위해 선택되었는지 여부\",\"액세스 가능한 Diff 뷰어 표시 여부\",\"diff 편집기에서 나란히 인라인 중단점에 연결할지 여부\",\"인라인 모드가 활성 상태인지 여부\",\"diff 편집기에서 수정된 내용을 쓸 수 있는지 여부\",\"diff 편집기에서 수정된 내용을 쓸 수 있는지 여부\",\"원본 문서의 URI입니다.\",\"수정된 문서의 URI\",\"'editor.columnSelection'을 사용하도록 설정되어 있는지 여부\",\"편집기에 선택된 텍스트가 있는지 여부\",\"편집기에 여러 개의 선택 항목이 있는지 여부\",\"'Tab' 키를 누르면 편집기 밖으로 포커스가 이동하는지 여부\",\"편집기 호버가 표시되는지 여부\",\"편집기 가리키기에 포커스가 있는지 여부\",\"스티키 스크롤의 포커스 여부\",\"스티키 스크롤의 가시성 여부\",\"독립 실행형 색 편집기가 표시되는지 여부\",\"독립 실행형 색 편집기가 포커스되는지 여부\",\"편집기가 더 큰 편집기(예: 전자 필기장)에 속해 있는지 여부\",\"편집기의 언어 식별자\",\"편집기에 완성 항목 공급자가 있는지 여부\",\"편집기에 코드 작업 공급자가 있는지 여부\",\"편집기에 CodeLens 공급자가 있는지 여부\",\"편집기에 정의 공급자가 있는지 여부\",\"편집기에 선언 공급자가 있는지 여부\",\"편집기에 구현 공급자가 있는지 여부\",\"편집기에 형식 정의 공급자가 있는지 여부\",\"편집기에 호버 공급자가 있는지 여부\",\"편집기에 문서 강조 표시 공급자가 있는지 여부\",\"편집기에 문서 기호 공급자가 있는지 여부\",\"편집기에 참조 공급자가 있는지 여부\",\"편집기에 이름 바꾸기 공급자가 있는지 여부\",\"편집기에 시그니처 도움말 공급자가 있는지 여부\",\"편집기에 인라인 힌트 공급자가 있는지 여부\",\"편집기에 문서 서식 공급자가 있는지 여부\",\"편집기에 문서 선택 서식 공급자가 있는지 여부\",\"편집기에 여러 개의 문서 서식 공급자가 있는지 여부\",\"편집기에 여러 개의 문서 선택 서식 공급자가 있는지 여부\",\"배열\",\"부울\",\"클래스\",\"상수\",\"생성자\",\"열거형\",\"열거형 멤버\",\"이벤트\",\"필드\",\"파일\",\"함수\",\"인터페이스\",\"키\",\"메서드\",\"모듈\",\"네임스페이스\",\"Null\",\"숫자\",\"개체\",\"연산자\",\"패키지\",\"속성\",\"문자열\",\"구조체\",\"형식 매개 변수\",\"변수\",\"{0}({1})\",\"일반 텍스트\",\"입력하는 중\",\"개발자: 검사 토큰\",\"줄/열로 이동...\",\"빠른 액세스 공급자 모두 표시\",\"명령 팔레트\",\"명령 표시 및 실행\",\"기호로 가서...\",\"범주별 기호로 이동...\",\"편집기 콘텐츠\",\"고대비 테마로 전환\",\"{1} 파일에서 편집을 {0}개 했습니다.\",\"자세히 표시({0})\",\"{0}자\",\"선택 앵커 지점\",\"{0}에 설정된 앵커: {1}\",\"선택 앵커 지점 설정\",\"선택 앵커 지점으로 이동\",\"앵커에서 커서로 선택\",\"선택 앵커 지점 취소\",\"괄호에 해당하는 영역을 표시자에 채색하여 표시합니다.\",\"대괄호로 이동\",\"괄호까지 선택\",\"대괄호 제거\",\"대괄호로 이동(&&B)\",\"대괄호 또는 중괄호를 포함하여 내부 텍스트를 선택합니다.\",\"선택한 텍스트를 왼쪽으로 이동\",\"선택한 텍스트를 오른쪽으로 이동\",\"문자 바꾸기\",\"잘라내기(&&T)\",\"잘라내기\",\"잘라내기\",\"잘라내기\",\"복사(&&C)\",\"복사\",\"복사\",\"복사\",\"붙여넣기(&&P)\",\"붙여넣기\",\"붙여넣기\",\"붙여넣기\",\"구문을 강조 표시하여 복사\",\"다음으로 복사\",\"다음으로 복사\",\"공유\",\"공유\",\"코드 작업을 적용하는 중 알 수 없는 오류가 발생했습니다.\",\"실행할 코드 작업의 종류입니다.\",\"반환된 작업이 적용되는 경우를 제어합니다.\",\"항상 반환된 첫 번째 코드 작업을 적용합니다.\",\"첫 번째 반환된 코드 작업을 적용합니다(이 작업만 있는 경우).\",\"반환된 코드 작업을 적용하지 마세요.\",\"기본 코드 작업만 반환되도록 할지 여부를 제어합니다.\",\"빠른 수정...\",\"사용 가능한 코드 동작이 없습니다.\",\"'{0}'에 대한 기본 코드 작업을 사용할 수 없음\",\"'{0}'에 대한 코드 작업을 사용할 수 없음\",\"사용할 수 있는 기본 코드 작업 없음\",\"사용 가능한 코드 동작이 없습니다.\",\"리팩터링...\",\"'{0}'에 대한 기본 리팩터링 없음\",\"'{0}'에 대한 리팩터링 없음\",\"기본 설정 리팩터링을 사용할 수 없음\",\"사용 가능한 리펙터링이 없습니다.\",\"소스 작업...\",\"'{0}'에 대한 기본 소스 작업을 사용할 수 없음\",\"'{0}'에 대한 소스 작업을 사용할 수 없음\",\"사용할 수 있는 기본 원본 작업 없음\",\"사용 가능한 소스 작업이 없습니다.\",\"가져오기 구성\",\"사용 가능한 가져오기 구성 작업이 없습니다.\",\"모두 수정\",\"모든 작업 수정 사용 불가\",\"자동 수정...\",\"사용할 수 있는 자동 수정 없음\",\"코드 작업 메뉴에 그룹 헤더 표시를 활성화/비활성화합니다.\",\"현재 진단 중이 아닐 때 줄 내에서 가장 가까운 빠른 수정 표시를 사용/사용 안 함으로 설정합니다.\",\"{1}이(가) {2}(으)로 설정될 때 {0} 트리거 활성화 코드 동작이 창 변경 및 포커스 변경에 대해 트리거되려면 {3}(으)로 설정되어야 합니다.\",\"컨텍스트: 줄 {1} 및 열 {2}의 {0}입니다.\",\"사용하지 않는 항목 숨기기\",\"비활성화된 항목 표시\",\"추가 작업...\",\"빠른 수정\",\"추출\",\"인라인\",\"재작성\",\"이동\",\"코드 감싸기\",\"소스 작업\",\"편집기에서 공간이 없을 때 여백에서 코드 동작 메뉴를 생성하는 아이콘입니다.\",\"편집기에 공간이 없고 빠른 수정을 사용할 수 있는 경우 여백에서 코드 작업 메뉴를 생성하는 아이콘입니다.\",\"편집기에 공간이 없고 AI 수정을 사용할 수 있는 경우 여백에서 코드 작업 메뉴를 생성하는 아이콘입니다.\",\"편집기에 공간이 없고 AI 수정 및 빠른 수정을 사용할 수 있는 경우 여백에서 코드 작업 메뉴를 생성하는 아이콘입니다.\",\"편집기에 공간이 없고 AI 수정 및 빠른 수정을 사용할 수 있는 경우 여백에서 코드 작업 메뉴를 생성하는 아이콘입니다.\",\"실행: {0}\",\"코드 작업 표시. 기본 설정 빠른 수정 사용 가능({0})\",\"코드 작업 표시({0})\",\"코드 작업 표시\",\"현재 줄에 대한 코드 렌즈 명령 표시\",\"명령 선택\",null,null,null,null,null,null,null,null,null,\"줄 주석 설정/해제\",\"줄 주석 설정/해제(&&T)\",\"줄 주석 추가\",\"줄 주석 제거\",\"블록 주석 설정/해제\",\"블록 주석 설정/해제(&&B)\",\"미니맵\",\"문자 렌더링\",\"세로 크기\",\"비례\",\"채우기\",\"맞춤\",\"슬라이더\",\"마우스 위로\",\"항상\",\"편집기 상황에 맞는 메뉴 표시\",\"커서 실행 취소\",\"커서 다시 실행\",\"적용할 붙여넣기 편집의 종류입니다.\\r\\n이러한 종류의 편집이 여러 개 있는 경우 편집기에 선택기가 표시됩니다. 이러한 종류의 편집이 없으면 편집기에서 오류 메시지가 표시됩니다.\",\"다른 이름으로 붙여넣기...\",\"텍스트로 붙여넣기\",\"붙여넣기 위젯이 표시되는지 여부\",\"붙여넣기 옵션 표시...\",\"'{0}'에 대한 붙여넣기 편집을 찾을 수 없습니다.\",\"붙여넣기 편집을 해결하는 중입니다. 취소하려면 클릭\",\"붙여넣기 처리기를 실행하는 중. 취소하고 기본 붙여넣기를 수행하려면 클릭하세요.\",\"붙여넣기 작업 선택\",\"붙여넣기 처리기를 실행하는 중\",\"일반 텍스트 삽입\",\"URI 삽입\",\"URI 삽입\",\"경로 삽입\",\"경로 삽입\",\"상대 경로 삽입\",\"상대 경로 삽입\",\"HTML 삽입\",null,\"드롭 위젯이 표시되는지 여부\",\"드롭 옵션 표시...\",\"드롭 처리기를 실행하는 중입니다. 취소하려면 클릭하세요.\",\"'{0}' 편집을 확인하는 동안 오류 발생:\\r\\n{1}\",\"'{0}' 편집을 적용하는 동안 오류 발생:\\r\\n{1}\",\"편집기에서 취소 가능한 작업(예: '참조 피킹')을 실행하는지 여부\",\"파일이 너무 커서 모두 바꾸기 작업을 수행할 수 없습니다.\",\"찾기\",\"찾기(&&F)\",\"인수로 찾기\",\"선택 영역에서 찾기\",\"다음 찾기\",\"이전 찾기\",\"일치 항목으로 이동...\",\"일치하는 항목이 없습니다. 다른 내용으로 검색해 보세요.\",\"특정 일치 항목으로 이동하려면 숫자를 입력하세요(1~{0} 사이).\",\"1에서 {0} 사이의 숫자를 입력하세요\",\"1에서 {0} 사이의 숫자를 입력하세요\",\"다음 선택 찾기\",\"이전 선택 찾기\",\"바꾸기\",\"바꾸기(&&R)\",\"편집기 찾기 위젯이 축소되었음을 나타내는 아이콘입니다.\",\"편집기 찾기 위젯이 확장되었음을 나타내는 아이콘입니다.\",\"편집기 찾기 위젯에서 '선택 영역에서 찾기'의 아이콘입니다.\",\"편집기 찾기 위젯에서 '바꾸기'의 아이콘입니다.\",\"편집기 찾기 위젯에서 '모두 바꾸기'의 아이콘입니다.\",\"편집기 찾기 위젯에서 '이전 찾기'의 아이콘입니다.\",\"편집기 찾기 위젯에서 '다음 찾기'의 아이콘입니다.\",\"찾기/바꾸기\",\"찾기\",\"찾기\",\"이전 검색 결과\",\"다음 검색 결과\",\"선택 항목에서 찾기\",\"닫기\",\"바꾸기\",\"바꾸기\",\"바꾸기\",\"모두 바꾸기\",\"바꾸기 설정/해제\",\"처음 {0}개의 결과가 강조 표시되지만 모든 찾기 작업은 전체 텍스트에 대해 수행됩니다.\",\"{1}의 {0}\",\"결과 없음\",\"{0}개 찾음\",\"'{1}'에 대한 {0}을(를) 찾음\",\"{2}에서 '{1}'에 대한 {0}을(를) 찾음\",\"'{1}'에 대한 {0}을(를) 찾음\",\"Ctrl+Enter를 누르면 이제 모든 항목을 바꾸지 않고 줄 바꿈을 삽입합니다. editor.action.replaceAll의 키 바인딩을 수정하여 이 동작을 재정의할 수 있습니다.\",\"펼치기\",\"재귀적으로 펼치기\",\"접기\",\"접기 전환\",\"재귀적으로 접기\",\"재귀적으로 접기 토글\",\"모든 블록 코멘트를 접기\",\"모든 영역 접기\",\"모든 영역 펼치기\",\"선택한 항목을 제외한 모든 항목 접기\",\"선택한 항목을 제외한 모든 항목 표시\",\"모두 접기\",\"모두 펼치기\",\"부모 폴딩으로 이동\",\"이전 접기 범위로 이동\",\"다음 접기 범위로 이동\",\"선택 영역에서 접기 범위 만들기\",\"수동 폴딩 범위 제거\",\"수준 {0} 접기\",\"접힌 범위의 배경색입니다. 색은 기본 장식을 숨기지 않기 위해 불투명해서는 안 됩니다.\",\"접힌 범위의 첫 번째 줄 뒤에 있는 축소된 텍스트의 색입니다.\",\"편집기 여백의 접기 컨트롤 색입니다.\",\"편집기 문자 모양 여백에서 확장된 범위의 아이콘입니다.\",\"편집기 문자 모양 여백에서 축소된 범위의 아이콘입니다.\",\"편집기 문자 모양 여백에서 수동으로 축소된 범위에 대한 아이콘입니다.\",\"편집기 문자 모양 여백에서 수동으로 확장된 범위에 대한 아이콘입니다.\",\"범위를 확장하려면 클릭합니다.\",\"범위를 축소하려면 클릭합니다.\",\"편집기 글꼴 크기 늘리기\",\"편집기 글꼴 크기 줄이기\",\"편집기 글꼴 크기 다시 설정\",\"문서 서식\",\"선택 영역 서식\",\"다음 문제로 이동 (오류, 경고, 정보)\",\"다음 마커로 이동의 아이콘입니다.\",\"이전 문제로 이동 (오류, 경고, 정보)\",\"이전 마커로 이동의 아이콘입니다.\",\"파일의 다음 문제로 이동 (오류, 경고, 정보)\",\"다음 문제(&&P)\",\"파일의 이전 문제로 이동 (오류, 경고, 정보)\",\"이전 문제(&&P)\",\"오류\",\"경고\",\"정보\",\"힌트\",\"{1}의 {0}입니다. \",\"문제 {1}개 중 {0}개\",\"문제 {1}개 중 {0}개\",\"편집기 표식 탐색 위젯 오류 색입니다.\",\"편집기 마커 탐색 위젯 오류 제목 배경.\",\"편집기 표식 탐색 위젯 경고 색입니다.\",\"편집기 마커 탐색 위젯 경고 제목 배경.\",\"편집기 표식 탐색 위젯 정보 색입니다.\",\"편집기 마커 탐색 위젯 정보 제목 배경.\",\"편집기 표식 탐색 위젯 배경입니다.\",\"피킹\",\"정의\",\"'{0}'에 대한 정의를 찾을 수 없습니다.\",\"정의를 찾을 수 없음\",\"정의로 이동(&&D)\",\"선언\",\"'{0}'에 대한 선언을 찾을 수 없음\",\"선언을 찾을 수 없음\",\"선언으로 이동(&&D)\",\"'{0}'에 대한 선언을 찾을 수 없음\",\"선언을 찾을 수 없음\",\"형식 정의\",\"'{0}'에 대한 형식 정의를 찾을 수 없습니다.\",\"형식 정의를 찾을 수 없습니다.\",\"형식 정의로 이동(&&T)\",\"구현\",\"'{0}'에 대한 구현을 찾을 수 없습니다.\",\"구현을 찾을 수 없습니다.\",\"구현으로 이동(&&I)\",\"'{0}'에 대한 참조가 없습니다.\",\"참조가 없습니다.\",\"참조로 이동(&&R)\",\"참조\",\"참조\",\"위치\",\"'{0}'에 대한 검색 결과가 없음\",\"참조\",\"정의로 이동\",\"측면에서 정의 열기\",\"정의 피킹\",\"선언으로 이동\",\"선언 미리 보기\",\"형식 정의로 이동\",\"형식 정의 미리 보기\",\"구현으로 이동\",\"피킹 구현\",\"참조로 이동\",\"참조 미리 보기\",\"임의의 기호로 이동\",\"{0}개 정의를 표시하려면 클릭하세요.\",\"'참조 피킹' 또는 '정의 피킹'과 같이 참조 피킹이 표시되는지 여부\",\"로드 중...\",\"{0}({1})\",\"참조 {0}개\",\"참조 {0}개\",\"참조\",\"미리 보기를 사용할 수 없음\",\"결과 없음\",\"참조\",\"{2} 열에 있는 {1} 행의 {0}에\",\"{3} 열에서 {2} 행의 {1}에 {0}\",\"{0}의 기호 1개, 전체 경로 {1}\",\"{1}의 기호 {0}개, 전체 경로 {2}\",\"결과 없음\",\"{0}에서 기호 1개를 찾았습니다.\",\"{1}에서 기호 {0}개를 찾았습니다.\",\"{1}개 파일에서 기호 {0}개를 찾았습니다.\",\"키보드만으로 탐색할 수 있는 기호 위치가 있는지 여부\",\"{1}의 {0} 기호, 다음의 경우 {2}\",\"{1}의 기호 {0}\",\"가리키기 세부 정보 표시 수준 늘리기\",\"가리키기 세부 정보 표시 수준 감소\",\"가리키기 또는 포커스 표시\",\"마우스로 가리켜도 포커스가 옮겨 가지 않습니다.\",\"마우스로 가리키면 이미 표시된 경우에만 포커스가 옮겨 갑니다.\",\"마우스로 가리키면 포커스가 나타나는 경우 포커스가 자동으로 옮겨 갑니다.\",\"정의 미리 보기 가리킨 항목 표시\",\"위로 스크롤 가리키기\",\"아래로 스크롤 가리키기\",\"왼쪽으로 스크롤 가리키기\",\"오른쪽으로 스크롤 가리키기\",\"페이지 위로 가리키기\",\"페이지 아래쪽 가리키기\",\"위쪽 가리키기로 이동\",\"아래쪽 가리키기로 이동\",\"현재 커서 위치에 있는 기호에 대한 설명서, 참조 및 기타 콘텐츠를 표시하는 편집기 가리키기를 표시하거나 포커스를 지정합니다.\",\"편집기에서 정의 미리 보기 가리키기 표시.\",\"편집기 가리키기를 위로 스크롤.\",\"편집기 가리키기 아래로 스크롤.\",\"편집기 가리키기 왼쪽 스크롤.\",\"편집기 가리키기 오른쪽으로 스크롤.\",\"편집기 가리키기 페이지 위로.\",\"에디터 가리키기 페이지 아래로.\",\"편집기 가리키기의 상단으로 이동.\",\"편집기 가리키기 맨 아래로 이동.\",\"가리키기 세부 정보 표시를 늘리기 위한 아이콘입니다.\",\"가리키기 세부 정보 표시를 줄이는 아이콘입니다.\",\"로드 중...\",\"성능상의 이유로 긴 줄로 인해 렌더링이 일시 중지되었습니다. `editor.stopRenderingLineAfter`를 통해 구성할 수 있습니다.\",\"성능상의 이유로 긴 줄의 경우 토큰화를 건너뜁니다. 이 항목은 'editor.maxTokenizationLineLength'를 통해 구성할 수 있습니다.\",\"가리키기 세부 정보 표시 증가({0})\",\"가리키기 세부 정보 표시 증가\",\"가리키기 세부 정보 표시 감소({0})\",\"가리키기 세부 정보 표시 감소\",\"문제 보기\",\"빠른 수정을 사용할 수 없음\",\"빠른 수정을 확인하는 중...\",\"빠른 수정을 사용할 수 없음\",\"빠른 수정...\",\"들여쓰기를 공백으로 변환\",\"들여쓰기를 탭으로 변환\",\"구성된 탭 크기\",\"기본 탭 크기\",\"현재 탭 크기\",\"현재 파일의 탭 크기 선택\",\"탭을 사용한 들여쓰기\",\"공백을 사용한 들여쓰기\",\"탭 표시 크기 변경\",\"콘텐츠에서 들여쓰기 감지\",\"줄 다시 들여쓰기\",\"선택한 줄 다시 들여쓰기\",\"탭 들여쓰기를 공백으로 변환합니다.\",\"공백 들여쓰기를 탭으로 변환합니다.\",\"탭으로 들여쓰기를 사용합니다.\",\"공백으로 들여쓰기를 사용합니다.\",\"탭에 해당하는 공간 크기를 변경합니다.\",\"콘텐츠에서 들여쓰기를 검색합니다.\",\"편집기의 줄을 다시 입력합니다.\",\"선택한 편집기 줄을 다시 들여쓰기합니다.\",\"삽입하려면 두 번 클릭\",\"Cmd+클릭\",\"Ctrl+클릭\",\"Option+클릭\",\"Alt+클릭\",\"정의({0})로 이동하여 자세히 알아보려면 마우스 오른쪽 단추를 클릭합니다.\",\"정의로 이동({0})\",\"명령 실행\",\"다음 인라인 제안 표시\",\"이전 인라인 제안 표시\",\"인라인 제안 트리거\",\"인라인 제안의 다음 단어 수락\",\"단어 수락\",\"인라인 제안의 다음 줄 수락\",\"줄 수락\",\"인라인 추천 수락\",\"수락\",\"인라인 제안 숨기기\",\"항상 도구 모음 표시\",\"인라인 제안 표시 여부\",\"인라인 제안이 공백으로 시작하는지 여부\",\"인라인 제안이 탭에 의해 삽입되는 것보다 작은 공백으로 시작하는지 여부\",\"현재 제안에 대한 제안 표시 여부\",\"접근성 보기에서 이를 검사({0})\",\"제안:\",\"다음 매개 변수 힌트 표시의 아이콘입니다.\",\"이전 매개 변수 힌트 표시의 아이콘입니다.\",\"{0}({1})\",\"이전\",\"다음\",null,null,null,null,null,null,null,null,\"이전 값으로 바꾸기\",\"다음 값으로 바꾸기\",\"선 선택 영역 확장\",\"위에 줄 복사\",\"위에 줄 복사(&&C)\",\"아래에 줄 복사\",\"아래에 줄 복사(&&P)\",\"중복된 선택 영역\",\"중복된 선택 영역(&&D)\",\"줄 위로 이동\",\"줄 위로 이동(&&V)\",\"줄 아래로 이동\",\"줄 아래로 이동(&&L)\",\"줄을 오름차순 정렬\",\"줄을 내림차순으로 정렬\",\"중복 라인 삭제\",\"후행 공백 자르기\",\"줄 삭제\",\"줄 들여쓰기\",\"줄 내어쓰기\",\"위에 줄 삽입\",\"아래에 줄 삽입\",\"왼쪽 모두 삭제\",\"우측에 있는 항목 삭제\",\"줄 연결\",\"커서 주위 문자 바꾸기\",\"대문자로 변환\",\"소문자로 변환\",\"단어의 첫 글자를 대문자로 변환\",\"스네이크 표기법으로 변환\",\"Camel Case로 변환\",\"Pascal Case 변환\",\"Kebab 사례로 변환\",\"연결된 편집 시작\",\"형식의 편집기에서 자동으로 이름을 바꿀 때의 배경색입니다.\",\"{0} 형식이 올바르지 않으므로 이 링크를 열지 못했습니다\",\"대상이 없으므로 이 링크를 열지 못했습니다.\",\"명령 실행\",\"링크로 이동\",\"Cmd+클릭\",\"Ctrl+클릭\",\"Option+클릭\",\"Alt+클릭\",\"명령 {0} 실행\",\"링크 열기\",\"편집기에서 현재 인라인 메시지를 표시하는지 여부\",\"커서가 추가됨: {0}\",\"커서가 추가됨: {0}\",\"위에 커서 추가\",\"위에 커서 추가(&&A)\",\"아래에 커서 추가\",\"아래에 커서 추가(&&D)\",\"줄 끝에 커서 추가\",\"줄 끝에 커서 추가(&&U)\",\"맨 아래에 커서 추가\",\"맨 위에 커서 추가\",\"다음 일치 항목 찾기에 선택 항목 추가\",\"다음 항목 추가(&&N)\",\"이전 일치 항목 찾기에 선택 항목 추가\",\"이전 항목 추가(&&R)\",\"다음 일치 항목 찾기로 마지막 선택 항목 이동\",\"마지막 선택 항목을 이전 일치 항목 찾기로 이동\",\"일치 항목 찾기의 모든 항목 선택\",\"모든 항목 선택(&&O)\",\"모든 항목 변경\",\"다음 커서 포커스\",\"다음 커서에 포커스를 맞춥니다.\",\"이전 커서 포커스\",\"이전 커서에 포커스를 맞춥니다.\",\"매개 변수 힌트 트리거\",\"다음 매개 변수 힌트 표시의 아이콘입니다.\",\"이전 매개 변수 힌트 표시의 아이콘입니다.\",\"{0}, 힌트\",\"매개 변수 힌트에 있는 활성 항목의 전경색입니다.\",\"현재 코드 편집기가 피킹 내부에 포함되었는지 여부\",\"닫기\",\"Peek 뷰 제목 영역의 배경색입니다.\",\"Peek 뷰 제목 색입니다.\",\"Peek 뷰 제목 정보 색입니다.\",\"Peek 뷰 테두리 및 화살표 색입니다.\",\"Peek 뷰 결과 목록의 배경색입니다.\",\"Peek 뷰 결과 목록에서 라인 노드의 전경색입니다.\",\"Peek 뷰 결과 목록에서 파일 노드의 전경색입니다.\",\"Peek 뷰 결과 목록에서 선택된 항목의 배경색입니다.\",\"Peek 뷰 결과 목록에서 선택된 항목의 전경색입니다.\",\"Peek 뷰 편집기의 배경색입니다.\",\"Peek 뷰 편집기의 거터 배경색입니다.\",\"피킹 뷰 편집기의 고정 스크롤 배경색입니다.\",\"Peek 뷰 결과 목록의 일치 항목 강조 표시 색입니다.\",\"Peek 뷰 편집기의 일치 항목 강조 표시 색입니다.\",\"Peek 뷰 편집기의 일치 항목 강조 표시 테두리입니다.\",\"편집기에서 자리 표시자 텍스트의 전경색입니다.\",\"우선 텍스트 편집기를 열고 줄로 이동합니다.\",\"줄 {0} 및 문자 {1}(으)로 이동합니다.\",\"{0} 줄로 이동합니다.\",\"현재 줄: {0}, 문자: {1} 이동할 줄 1~{2} 사이의 번호를 입력합니다.\",\"현재 줄: {0}, 문자: {1}. 이동할 줄 번호를 입력합니다.\",\"기호로 이동하려면 먼저 기호 정보가 있는 텍스트 편집기를 엽니다.\",\"활성 상태의 텍스트 편집기는 기호 정보를 제공하지 않습니다.\",\"일치하는 편집기 기호 없음\",\"편집기 기호 없음\",\"측면에서 열기\",\"하단에 열기\",\"기호({0})\",\"속성({0})\",\"메서드({0})\",\"함수({0})\",\"생성자({0})\",\"변수({0})\",\"클래스({0})\",\"구조체({0})\",\"이벤트({0})\",\"연산자({0})\",\"인터페이스({0})\",\"네임스페이스({0})\",\"패키지({0})\",\"형식 매개 변수({0})\",\"모듈({0})\",\"속성({0})\",\"열거형({0})\",\"열거형 멤버({0})\",\"문자열({0})\",\"파일({0})\",\"배열({0})\",\"숫자({0})\",\"부울({0})\",\"개체({0})\",\"키({0})\",\"필드({0})\",\"상수({0})\",\"읽기 전용 입력에서는 편집할 수 없습니다.\",\"읽기 전용 편집기에서는 편집할 수 없습니다.\",\"결과가 없습니다.\",\"위치 이름을 바꾸는 중 알 수 없는 오류가 발생했습니다.\",\"'{0}'에서 '{1}'(으)로 이름을 바꾸는 중\",\"{1}에 {0} 이름 바꾸기\",\"'{0}'을(를) '{1}'(으)로 이름을 변경했습니다. 요약: {2}\",\"이름 바꾸기를 통해 편집 내용을 적용하지 못했습니다.\",\"이름 바꾸기를 통해 편집 내용을 계산하지 못했습니다.\",\"기호 이름 바꾸기\",\"이름을 바꾸기 전에 변경 내용을 미리 볼 수 있는 기능 사용/사용 안 함\",\"포커스 다음 이름 바꾸기 제안\",\"이전 이름 바꾸기 제안 포커스\",\"입력 이름 바꾸기 위젯이 표시되는지 여부\",\"입력 이름 바꾸기 위젯이 포커스되었는지 여부\",\"이름 바꾸기 {0}, 미리 보기 {1}\",\"{0} 이름 바꾸기 제안을 받습니다.\",\"입력 이름을 바꾸세요. 새 이름을 입력한 다음 [Enter] 키를 눌러 커밋하세요.\",\"새 이름 제안 생성\",\"취소\",\"선택 영역 확장\",\"선택 영역 확장(&&E)\",\"선택 영역 축소\",\"선택 영역 축소(&&S)\",\"현재 편집기가 코드 조각 모드인지 여부\",\"코드 조각 모드일 때 다음 탭 정지가 있는지 여부\",\"코드 조각 모드일 때 이전 탭 정지가 있는지 여부\",\"다음 자리 표시자로 이동...\",\"일요일\",\"월요일\",\"화요일\",\"수요일\",\"목요일\",\"금요일\",\"토요일\",\"일\",\"월\",\"화\",\"수\",\"목\",\"금\",\"토\",\"1월\",\"2월\",\"3월\",\"4월\",\"5월\",\"6월\",\"7월\",\"8월\",\"9월\",\"10월\",\"11월\",\"12월\",\"1월\",\"2월\",\"3월\",\"4월\",\"5월\",\"6월\",\"7월\",\"8월\",\"9월\",\"10월\",\"11월\",\"12월\",\"편집기 고정 스크롤 토글(&토글)\",\"고정 스크롤\",\"고정 스크롤(&&S)\",\"고정 스크롤 포커스(&&F)\",\"편집기 고정 스크롤 토글\",\"뷰포트 맨 위에 중첩된 범위를 표시하는 편집기 고정 스크롤을 토글/활성화합니다.\",\"편집기 고정 스크롤에 포커스\",\"다음 편집기 고정 스크롤 선 선택\",\"이전 고정 스크롤 선 선택\",\"포커스가 있는 고정 스크롤 선으로 이동\",\"편집기 선택\",\"제안에 초점을 맞추는지 여부\",\"제안 세부 정보가 표시되는지 여부\",\"선택할 수 있는 여러 제안이 있는지 여부\",\"현재 제안을 삽입하면 변경 내용이 생성되는지 또는 모든 항목이 이미 입력되었는지 여부\",\"<Enter> 키를 누를 때 제안이 삽입되는지 여부\",\"현재 제안에 삽입 및 바꾸기 동작이 있는지 여부\",\"기본 동작이 삽입인지 또는 바꾸기인지 여부\",\"현재 제안에서 추가 세부 정보를 확인하도록 지원하는지 여부\",\"{0}의 {1}개의 수정사항을 수락하는 중\",\"제안 항목 트리거\",\"삽입\",\"삽입\",\"바꾸기\",\"바꾸기\",\"삽입\",\"간단히 표시\",\"자세히 표시\",\"제안 위젯 크기 다시 설정\",\"제안 위젯의 배경색입니다.\",\"제안 위젯의 테두리 색입니다.\",\"제안 위젯의 전경색입니다.\",\"제한 위젯에서 선택된 항목의 전경색입니다.\",\"제한 위젯에서 선택된 항목의 아이콘 전경색입니다.\",\"제한 위젯에서 선택된 항목의 배경색입니다.\",\"제안 위젯의 일치 항목 강조 표시 색입니다.\",\"항목에 포커스가 있을 때 추천 위젯에서 일치하는 항목의 색이 강조 표시됩니다.\",\"제안 위젯 상태의 배경색입니다.\",\"로드 중...\",\"제안 항목이 없습니다.\",\"제안\",\"{0} {1}, {2}\",\"{0} {1}\",\"{0}, {1}\",\"{0}, 문서: {1}\",\"닫기\",\"로드 중...\",\"제안 위젯에서 자세한 정보의 아이콘입니다.\",\"자세한 정보\",\"배열 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 나타납니다.\",\"부울 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 나타납니다.\",\"클래스 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 나타납니다.\",\"색 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안에 표시됩니다.\",\"상수 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 나타납니다.\",\"생성자 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 표시됩니다.\",\"열거자 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 표시됩니다.\",\"열거자 멤버 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 나타납니다.\",\"이벤트 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 나타납니다.\",\"필드 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 표시됩니다.\",\"파일 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 나타납니다.\",\"폴더 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 나타납니다.\",\"함수 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 나타납니다.\",\"인터페이스 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 표시됩니다.\",\"키 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 표시됩니다.\",\"키워드 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 나타납니다.\",\"메서드 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 표시됩니다.\",\"모듈 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 나타납니다.\",\"네임스페이스 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 나타납니다.\",\"null 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 나타납니다.\",\"숫자 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 표시됩니다.\",\"개체 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 나타납니다.\",\"연산자 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 나타납니다.\",\"패키지 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 나타납니다.\",\"속성 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 나타납니다.\",\"참조 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 나타납니다.\",\"코드 조각 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 표시됩니다.\",\"문자열 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 표시됩니다.\",\"구조 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 표시됩니다.\",\"텍스트 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 나타납니다.\",\"형식 매개변수 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 표시됩니다.\",\"단위 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 표시됩니다.\",\"변수 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 표시됩니다.\",\"이제 <Tab> 키를 누르면 포커스가 다음 포커스 가능한 요소로 이동합니다.\",\"이제 <Tab> 키를 누르면 탭 문자가 삽입됩니다.\",\"<Tab> 키로 포커스 이동 설정/해제\",\"탭 키가 워크벤치 주위로 포커스를 이동할지 또는 현재 편집기에 탭 문자를 삽입할지 여부를 결정합니다. 이를 탭 트래핑, 탭 탐색 또는 탭 포커스 모드라고도 합니다.\",\"개발자: 강제로 다시 토큰화\",\"확장 편집기에 경고 메시지와 함께 표시되는 아이콘입니다.\",\"이 문서에는 기본 ASCII 유니코드 문자가 아닌 문자가 많이 포함되어 있습니다.\",\"이 문서에는 모호한 유니코드 문자가 많이 포함되어 있습니다.\",\"이 문서에는 보이지 않는 유니코드 문자가 많이 포함되어 있습니다.\",\"유니코드 강조 표시 옵션 구성\",\"문자 {0}은(는) 소스 코드에서 더 일반적인 ASCII 문자 {1}과(와) 혼동될 수 있습니다.\",\"{0} 문자는 소스 코드에서 더 일반적인 {1} 문자와 혼동될 수 있습니다.\",\"{0} 문자가 보이지 않습니다.\",\"{0} 문자는 기본 ASCII 문자가 아닙니다.\",\"설정 조정\",\"메모에서 강조 표시 사용 안 함\",\"메모에서 문자 강조 표시 사용 안 함\",\"문자열에서 강조 표시 사용 안 함\",\"문자열에서 문자 강조 표시 사용 안 함\",\"모호한 강조 사용 안 함\",\"모호한 문자 강조 표시 사용 안 함\",\"보이지 않는 강조 사용 안 함\",\"보이지 않는 문자 강조 표시 사용 안 함\",\"ASCII가 문자가 아닌 강조 사용 안 함\",\"기본이 아닌 ASCII 문자 강조 표시 사용 안 함\",\"제외 옵션 표시\",\"{0}(보이지 않는 문자)이(가) 강조 표시되지 않도록 제외\",\"강조 표시에서 {0} 제외\",\"언어 \\\"{0}\\\"에서 더 일반적인 유니코드 문자를 허용합니다.\",\"비정상적인 줄 종결자\",\"비정상적인 줄 종결자가 검색됨\",\"이 파일 ‘\\r\\n’에 LS(줄 구분 기호) 또는 PS(단락 구분 기호) 같은 하나 이상의 비정상적인 줄 종결자 문자가 포함되어 있습니다.{0}\\r\\n파일에서 제거하는 것이 좋습니다. `editor.unusualLineTerminators`를 통해 구성할 수 있습니다.\",\"비정상적인 줄 종결자 제거(&&R)\",\"무시\",\"변수 읽기와 같은 읽기 액세스 중 기호의 배경색입니다. 기본 장식을 숨기지 않도록 색은 불투명하지 않아야 합니다.\",\"변수에 쓰기와 같은 쓰기 액세스 중 기호의 배경색입니다. 기본 장식을 숨기지 않도록 색은 불투명하지 않아야 합니다.\",\"기호에 대한 텍스트 항목의 배경색입니다. 기본 장식을 숨기지 않도록 색은 불투명하지 않아야 합니다.\",\"변수 읽기와 같은 읽기 액세스 중 기호의 테두리 색입니다.\",\"변수에 쓰기와 같은 쓰기 액세스 중 기호의 테두리 색입니다.\",\"기호에 대한 텍스트 항목의 테두리 색입니다.\",\"기호 강조 표시의 개요 눈금자 표식 색입니다. 기본 장식을 숨기지 않도록 색은 불투명하지 않아야 합니다.\",\"쓰기 액세스 기호에 대한 개요 눈금자 표식 색이 강조 표시됩니다. 기본 장식을 숨기지 않도록 색은 불투명하지 않아야 합니다.\",\"기호에 대한 텍스트 항목의 개요 눈금자 마커 색입니다. 기본 장식을 숨기지 않도록 색은 불투명하지 않아야 합니다.\",\"다음 강조 기호로 이동\",\"이전 강조 기호로 이동\",\"기호 강조 표시 트리거\",\"단어 삭제\",\"위치에 오류 발생\",\"오류\",\"위치의 경고\",\"경고\",\"줄에 대한 오류\",\"줄에 대한 오류\",\"줄에 대한 경고\",\"줄에 대한 경고\",\"줄의 접힌 부분\",\"접힘\",\"줄의 중단점\",\"중단점\",\"줄의 인라인 제안\",\"터미널 빠른 수정\",\"빠른 수정\",\"중단점에서 중지된 디버거\",\"중단점\",\"줄의 인레이 힌트 없음\",\"인레이 힌트 없음\",\"작업 완료\",\"완료된 작업\",\"작업 실패\",\"작업 실패\",\"터미널 명령 실패\",\"명령이 실패했습니다.\",\"터미널 명령 성공\",\"명령 성공\",\"터미널 벨\",\"터미널 벨\",\"Notebook 셀 완료됨\",\"Notebook 셀 완료됨\",\"Notebook 셀 실패\",\"Notebook 셀 실패\",\"Diff 줄 삽입됨\",\"Diff 줄 삭제됨\",\"Diff 줄 수정됨\",\"채팅 요청 전송됨\",\"채팅 요청 전송됨\",\"채팅 응답 수신됨\",\"진행률\",\"진행률\",\"지우기\",\"지우기\",\"저장\",\"저장\",\"형식\",\"서식\",\"음성 녹음 시작됨\",\"음성 녹음 중지됨\",\"보기\",\"도움말\",\"테스트\",\"파일\",\"기본 설정\",\"개발자\",\"{0}({1})\",\"{0}({1})\",\"{0}\\r\\n[{1}] {2}\",\"{1}부터 {0}까지\",\"{0}({1})\",\"숨기기\",\"메뉴 다시 설정\",\"'{0}' 숨기기\",\"키 바인딩 구성\",\"적용하려면 {0}, 미리 보기하려면 {1}\",\"신청하려면 {0}\",\"{0}, 사용 안 함 이유: {1}\",\"작업 위젯\",\"작업 표시줄에서 토글된 작업 항목의 배경색입니다.\",\"작업 위젯 목록 표시 여부\",\"작업 위젯 숨기기\",\"이전 작업 선택\",\"다음 작업 선택\",\"선택한 작업 수락\",\"선택한 작업 미리 보기\",\"기본 언어 구성 재정의\",\"{0}에서 재정의할 설정을 구성합니다.\",\"언어에 대해 재정의할 편집기 설정을 구성합니다.\",\"이 설정은 언어별 구성을 지원하지 않습니다.\",\"언어에 대해 재정의할 편집기 설정을 구성합니다.\",\"이 설정은 언어별 구성을 지원하지 않습니다.\",\"빈 속성을 등록할 수 없음\",\"'{0}'을(를) 등록할 수 없습니다. 이는 언어별 편집기 설정을 설명하는 속성 패턴인 '\\\\\\\\[.*\\\\\\\\]$'과(와) 일치합니다. 'configurationDefaults' 기여를 사용하세요.\",\"'{0}'을(를) 등록할 수 없습니다. 이 속성은 이미 등록되어 있습니다.\",\"'{0}'을(를) 등록할 수 없습니다. 연결된 정책 {1}이(가) 이미 {2}에 등록되어 있습니다.\",\"컨텍스트 키에 대한 정보를 반환하는 명령\",\"빈 컨텍스트 키 식\",\"식 쓰는 것을 잊으셨나요? 항상 'false' 또는 'true'를 넣어 각각 false 또는 true로 평가할 수도 있습니다.\",\"'not' 뒤에 'in'이 있습니다.\",\"닫는 괄호 ')'\",\"예기치 않은 토큰\",\"토큰 앞에 && 또는 ||를 입력하는 것을 잊으셨나요?\",\"필요하지 않은 식의 끝\",\"컨텍스트 키를 입력하는 것을 잊으셨나요?\",\"예상: {0}\\r\\n수신됨: '{1}'.\",\"운영 체제가 macOS인지 여부\",\"운영 체제가 Linux인지 여부\",\"운영 체제가 Windows인지 여부\",\"플랫폼이 웹 브라우저인지 여부\",\"브라우저 기반이 아닌 플랫폼에서 운영 체제가 macOS인지 여부\",\"운영 체제가 iOS인지 여부\",\"플랫폼이 모바일 웹 브라우저인지 여부\",\"VS 코드의 품질 유형\",\"키보드 포커스가 입력 상자 내에 있는지 여부\",\"{0}을(를) 사용하시겠습니까?\",\"{0} 또는 {1}을(를) 사용하시겠습니까?\",\"{0}, {1} 또는 {2}을(를) 사용하시겠습니까?\",\"견적을 열거나 닫는 것을 잊으셨나요?\",\"'/'(슬래시) 문자를 이스케이프하는 것을 잊으셨나요? 이스케이프하려면 앞에 백슬라시 두 개(예: '\\\\\\\\/')를 넣습니다.\",\"제안이 표시되는지 여부\",\"({0})을(를) 눌렀습니다. 둘째 키는 잠시 기다렸다가 누르십시오...\",\"({0})을(를) 눌렀습니다. 코드의 다음 키를 기다리는 중...\",\"키 조합({0}, {1})은 명령이 아닙니다.\",\"키 조합({0}, {1})은 명령이 아닙니다.\",\"워크벤치\",\"Windows와 Linux의 'Control'을 macOS의 'Command'로 매핑합니다.\",\"Windows와 Linux의 'Alt'를 macOS의 'Option'으로 매핑합니다.\",\"마우스로 트리와 목록의 항목을 다중 선택에 추가할 때 사용할 한정자입니다(예를 들어 탐색기에서 편집기와 SCM 보기를 여는 경우). '옆에서 열기' 마우스 제스처(지원되는 경우)는 다중 선택 한정자와 충돌하지 않도록 조정됩니다.\",\"트리와 목록에서 마우스를 사용하여 항목을 여는 방법을 제어합니다(지원되는 경우). 일부 트리와 목록에서는 이 설정을 적용할 수 없는 경우 무시하도록 선택할 수 있습니다.\",\"워크벤치에서 목록 및 트리의 가로 스크롤 여부를 제어합니다. 경고: 이 설정을 켜면 성능에 영향을 미칩니다.\",\"스크롤 막대 스크롤 페이지의 페이지별 클릭 여부를 제어합니다.\",\"트리 들여쓰기를 픽셀 단위로 제어합니다.\",\"트리에서 들여쓰기 가이드를 렌더링할지 여부를 제어합니다.\",\"목록과 트리에 부드러운 화면 이동 기능이 있는지를 제어합니다.\",\"마우스 휠 스크롤 이벤트의 `deltaX` 및 `deltaY`에서 사용할 승수입니다.\",\"'Alt' 키를 누를 때 스크롤 속도 승수입니다.\",\"검색할 때 요소를 강조 표시합니다. 추가 위아래 탐색은 강조 표시된 요소만 탐색합니다.\",\"검색할 때 요소를 필터링합니다.\",\"워크벤치에서 목록 및 트리의 기본 찾기 모드를 제어합니다.\",\"간단한 키보드 탐색에서는 키보드 입력과 일치하는 요소에 집중합니다. 일치는 접두사에서만 수행됩니다.\",\"키보드 탐색 강조 표시에서는 키보드 입력과 일치하는 요소를 강조 표시합니다. 이후로 탐색에서 위 및 아래로 이동하는 경우 강조 표시된 요소만 트래버스합니다.\",\"키보드 탐색 필터링에서는 키보드 입력과 일치하지 않는 요소를 모두 필터링하여 숨깁니다.\",\"워크벤치의 목록 및 트리 키보드 탐색 스타일을 제어합니다. 간소화하고, 강조 표시하고, 필터링할 수 있습니다.\",\"대신 'workbench.list.defaultFindMode' 및 'workbench.list.typeNavigationMode'를 사용하세요.\",\"검색할 때 유사 항목 일치를 사용합니다.\",\"검색할 때 연속 일치를 사용합니다.\",\"워크벤치에서 목록 및 트리를 검색할 때 사용하는 일치 유형을 제어합니다.\",\"폴더 이름을 클릭할 때 트리 폴더가 확장되는 방법을 제어합니다. 일부 트리와 목록에서는 이 설정을 적용할 수 없는 경우 무시하도록 선택할 수 있습니다.\",\"트리에서 고정 스크롤을 사용할지 여부를 제어합니다.\",\"{0}을(를) 사용하도록 설정한 경우 트리에 표시되는 고정 요소의 수를 제어합니다.\",\"워크벤치의 목록 및 트리에서 형식 탐색이 작동하는 방식을 제어합니다. 'trigger'로 설정 시 'list.triggerTypeNavigation' 명령이 실행되면 형식 탐색이 시작됩니다.\",\"오류\",\"경고\",\"정보\",\"최근에 사용한 항목\",\"유사한 명령\",\"일반적으로 사용됨\",\"기타 명령\",\"유사한 명령\",\"{0}, {1}\",\"'{0}' 명령에서 오류가 발생했습니다.\",\"{0}, {1}\",\"키보드 포커스가 빠른 입력 컨트롤 내에 있는지 여부\",\"현재 표시되는 빠른 입력 형식임\",\"빠른 입력의 커서가 입력 상자의 끝에 있는지 여부\",\"뒤로\",\"입력을 확인하려면 'Enter' 키를 누르고, 취소하려면 'Esc' 키를 누르세요.\",\"{0} / {1}\",\"결과의 범위를 축소하려면 입력하세요.\",\"빠른 선택 컨텍스트에서 사용됩니다. 이 명령에 대한 하나의 키 바인딩을 변경하는 경우 이 명령의 다른 모든 키 바인딩(한정자 변형)도 변경해야 합니다.\",\"빠른 액세스 모드에 있는 경우 다음 항목으로 이동합니다. 빠른 액세스 모드가 아닌 경우 다음 구분 기호로 이동합니다.\",\"빠른 액세스 모드인 경우 이전 항목으로 이동합니다. 빠른 액세스 모드가 아닌 경우 이전 구분 기호로 이동합니다.\",\"모든 확인란 선택/해제\",\"{0}개 결과\",\"{0} 선택됨\",\"확인\",\"사용자 지정\",\"뒤로({0})\",\"뒤로\",\"빠른 입력\",\"'{0}' 명령을 실행하려면 클릭\",\"전체 전경색입니다. 이 색은 구성 요소에서 재정의하지 않은 경우에만 사용됩니다.\",\"비활성화된 요소의 전체 전경입니다. 이 색은 구성 요소에서 재정의하지 않는 경우에만 사용됩니다.\",\"오류 메시지에 대한 전체 전경색입니다. 이 색은 구성 요소에서 재정의하지 않은 경우에만 사용됩니다.\",\"레이블과 같이 추가 정보를 제공하는 설명 텍스트의 전경색입니다.\",\"워크벤치 아이콘의 기본 색상입니다.\",\"포커스가 있는 요소의 전체 테두리 색입니다. 이 색은 구성 요소에서 재정의하지 않은 경우에만 사용됩니다.\",\"더 뚜렷이 대비되도록 요소를 다른 요소와 구분하는 요소 주위의 추가 테두리입니다.\",\"더 뚜렷이 대비되도록 요소를 다른 요소와 구분하는 활성 요소 주위의 추가 테두리입니다.\",\"워크벤치의 텍스트 선택(예: 입력 필드 또는 텍스트 영역) 전경색입니다. 편집기 내의 선택에는 적용되지 않습니다.\",\"텍스트 내 링크의 전경색입니다.\",\"클릭하고 마우스가 올라간 상태의 텍스트 내 링크의 전경색입니다.\",\"텍스트 구분자 색상입니다.\",\"미리 서식이 지정된 텍스트 세그먼트의 전경색입니다.\",\"미리 서식이 지정된 텍스트 세그먼트의 배경색입니다.\",\"텍스트 내 블록 인용의 전경색입니다.\",\"텍스트 내 블록 인용의 테두리 색입니다.\",\"텍스트 내 코드 블록의 전경색입니다.\",\"차트에 사용된 전경색입니다.\",\"차트 가로줄에 사용된 색입니다.\",\"차트 시각화에 사용되는 빨간색입니다.\",\"차트 시각화에 사용되는 파란색입니다.\",\"차트 시각화에 사용되는 노란색입니다.\",\"차트 시각화에 사용되는 주황색입니다.\",\"차트 시각화에 사용되는 녹색입니다.\",\"차트 시각화에 사용되는 자주색입니다.\",\"편집기 배경색입니다.\",\"편집기 기본 전경색입니다.\",\"편집기에서 고정 스크롤의 배경색\",\"편집기에서 마우스로 가리킬 때 고정 스크롤의 배경색\",\"편집기에서 고정 스크롤의 테두리 색\",\" 편집기에서 고정 스크롤의 그림자 색\",\"찾기/바꾸기 같은 편집기 위젯의 배경색입니다.\",\"찾기/바꾸기와 같은 편집기 위젯의 전경색입니다.\",\"편집기 위젯의 테두리 색입니다. 위젯에 테두리가 있고 위젯이 색상을 무시하지 않을 때만 사용됩니다.\",\"편집기 위젯 크기 조정 막대의 테두리 색입니다. 이 색은 위젯에서 크기 조정 막대를 표시하도록 선택하고 위젯에서 색을 재지정하지 않는 경우에만 사용됩니다.\",\"편집기에서 오류 텍스트의 배경색입니다. 기본 장식을 숨기지 않도록 색은 불투명하지 않아야 합니다.\",\"편집기 내 오류 표시선의 전경색입니다.\",\"설정된 경우 편집기에서 오류를 나타내는 이중 밑줄의 색입니다.\",\"편집기에서 경고 텍스트의 배경색입니다. 기본 장식을 숨기지 않도록 색은 불투명하지 않아야 합니다.\",\"편집기 내 경고 표시선의 전경색입니다.\",\"설정된 경우 편집기에서 경고를 나타내는 이중 밑줄의 색입니다.\",\"편집기에서 정보 텍스트의 배경색입니다. 기본 장식을 숨기지 않도록 색은 불투명하지 않아야 합니다.\",\"편집기 내 정보 표시선의 전경색입니다.\",\"설정된 경우 편집기에서 정보를 나타내는 이중 밑줄 색입니다.\",\"편집기에서 힌트 표시선의 전경색입니다.\",\"설정된 경우 편집기에서 힌트를 나타내는 이중 밑줄 색입니다.\",\"활성 링크의 색입니다.\",\"편집기 선택 영역의 색입니다.\",\"고대비를 위한 선택 텍스트의 색입니다.\",\"비활성 편집기의 선택 항목 색입니다. 기본 장식을 숨기지 않도록 색은 불투명하지 않아야 합니다.\",\"선택 영역과 동일한 콘텐츠가 있는 영역의 색입니다. 기본 장식을 숨기지 않도록 색은 불투명하지 않아야 합니다.\",\"선택 영역과 동일한 콘텐츠가 있는 영역의 테두리 색입니다.\",\"현재 검색 일치 항목의 색입니다.\",\"현재 검색 일치 항목의 텍스트 색입니다.\",\"기타 검색 일치 항목의 색입니다. 기본 장식을 숨기지 않도록 색은 불투명하지 않아야 합니다.\",\"다른 검색 일치 항목의 전경색입니다.\",\"검색을 제한하는 범위의 색입니다. 기본 장식을 숨기지 않도록 색은 불투명하지 않아야 합니다.\",\"현재 검색과 일치하는 테두리 색입니다.\",\"다른 검색과 일치하는 테두리 색입니다.\",\"검색을 제한하는 범위의 테두리 색입니다. 기본 장식을 숨기지 않도록 색은 불투명하지 않아야 합니다.\",\"호버가 표시된 단어 아래를 강조 표시합니다. 기본 장식을 숨기지 않도록 색은 불투명하지 않아야 합니다.\",\"편집기 호버의 배경색.\",\"편집기 호버의 전경색입니다.\",\"편집기 호버의 테두리 색입니다.\",\"편집기 호버 상태 표시줄의 배경색입니다.\",\"인라인 힌트의 전경색\",\"인라인 힌트의 배경색\",\"형식에 대한 인라인 힌트의 전경색\",\"형식에 대한 인라인 힌트의 배경색\",\"매개 변수에 대한 인라인 힌트의 전경색\",\"매개 변수에 대한 인라인 힌트의 배경색\",\"전구 작업 아이콘에 사용되는 색상입니다.\",\"전구 자동 수정 작업 아이콘에 사용되는 색상입니다.\",\"전구 AI 아이콘에 사용되는 색상입니다.\",\"코드 조각 탭 정지의 강조 표시 배경색입니다.\",\"코드 조각 탭 정지의 강조 표시 테두리 색입니다.\",\"코드 조각 마지막 탭 정지의 강조 표시 배경색입니다.\",\"코드 조각 마지막 탭 정지의 강조 표시 배경색입니다.\",\"삽입된 텍스트의 배경색입니다. 기본 장식을 숨기지 않도록 색은 불투명하지 않아야 합니다.\",\"제거된 텍스트 배경색입니다. 기본 장식을 숨기지 않도록 색은 불투명하지 않아야 합니다.\",\"삽입된 줄의 배경색입니다. 기본 장식을 숨기지 않도록 색은 불투명하지 않아야 합니다.\",\"제거된 줄의 배경색입니다. 색상은 기본 장식을 숨기지 않도록 불투명하지 않아야 합니다.\",\"줄이 삽입된 여백의 배경색입니다.\",\"줄이 제거된 여백의 배경색입니다.\",\"삽입된 콘텐츠에 대한 차등 개요 눈금자 전경입니다.\",\"제거된 콘텐츠에 대한 차등 개요 눈금자 전경입니다.\",\"삽입된 텍스트의 윤곽선 색입니다.\",\"제거된 텍스트의 윤곽선 색입니다.\",\"두 텍스트 편집기 사이의 테두리 색입니다.\",\"diff 편집기의 대각선 채우기 색입니다. 대각선 채우기는 diff 나란히 보기에서 사용됩니다.\",\"diff 편집기에서 변경되지 않은 블록의 배경색입니다.\",\"diff 편집기에서 변경되지 않은 블록의 전경색입니다.\",\"diff 편집기에서 변경되지 않은 코드의 배경색입니다.\",\"편집기 내에서 찾기/바꾸기 같은 위젯의 그림자 색입니다.\",\"편집기 내에서 찾기/바꾸기와 같은 위젯의 테두리 색입니다.\",\"마우스를 사용하여 작업 위로 마우스를 가져가는 경우 도구 모음 배경\",\"마우스를 사용하여 작업 위로 마우스를 가져가는 경우 도구 모음 윤곽선\",\"작업 위에 마우스를 놓았을 때 도구 모음 배경\",\"포커스가 있는 이동 경로 항목의 색입니다.\",\"이동 경로 항목의 배경색입니다.\",\"포커스가 있는 이동 경로 항목의 색입니다.\",\"선택한 이동 경로 항목의 색입니다.\",\"이동 경로 항목 선택기의 배경색입니다.\",\"인라인 병합 충돌의 현재 헤더 배경입니다. 기본 장식을 숨기지 않도록 색은 불투명하지 않아야 합니다.\",\"인라인 병합 충돌의 현재 콘텐츠 배경입니다. 기본 장식을 숨기지 않도록 색은 불투명하지 않아야 합니다.\",\"인라인 병합 충돌의 들어오는 헤더 배경입니다. 기본 장식을 숨기지 않도록 색은 불투명하지 않아야 합니다.\",\"인라인 병합 충돌의 들어오는 콘텐츠 배경입니다. 기본 장식을 숨기지 않도록 색은 불투명하지 않아야 합니다.\",\"인라인 병합 충돌의 공통 상위 헤더 배경입니다. 기본 장식을 숨기지 않도록 색은 불투명하지 않아야 합니다.\",\"인라인 병합 충돌의 공통 상위 콘텐츠 배경입니다. 기본 장식을 숨기지 않도록 색은 불투명하지 않아야 합니다.\",\"인라인 병합 충돌에서 헤더 및 스플리터의 테두리 색입니다.\",\"인라인 병합 충돌에서 현재 개요 눈금 전경색입니다.\",\"인라인 병합 충돌에서 수신 개요 눈금 전경색입니다.\",\"인라인 병합 충돌에서 공통 과거 개요 눈금 전경색입니다.\",\"일치 항목 찾기의 개요 눈금자 표식 색입니다. 기본 장식을 숨기지 않도록 색은 불투명하지 않아야 합니다.\",\"선택 항목의 개요 눈금자 표식 색이 강조 표시됩니다. 기본 장식을 숨기지 않도록 색은 불투명하지 않아야 합니다.\",\"문제 오류 아이콘에 사용되는 색입니다.\",\"문제 경고 아이콘에 사용되는 색입니다.\",\"문제 정보 아이콘에 사용되는 색입니다.\",\"입력 상자 배경입니다.\",\"입력 상자 전경입니다.\",\"입력 상자 테두리입니다.\",\"입력 필드에서 활성화된 옵션의 테두리 색입니다.\",\"입력 필드에서 활성화된 옵션의 배경색입니다.\",\"입력 필드에 있는 옵션의 배경 가리키기 색입니다.\",\"입력 필드에서 활성화된 옵션의 전경색입니다.\",\"위치 표시자 텍스트에 대한 입력 상자 전경색입니다.\",\"정보 심각도의 입력 유효성 검사 배경색입니다.\",\"정보 심각도의 입력 유효성 검사 전경색입니다.\",\"정보 심각도의 입력 유효성 검사 테두리 색입니다.\",\"경고 심각도의 입력 유효성 검사 배경색입니다.\",\"경고 심각도의 입력 유효성 검사 전경색입니다.\",\"경고 심각도의 입력 유효성 검사 테두리 색입니다.\",\"오류 심각도의 입력 유효성 검사 배경색입니다.\",\"오류 심각도의 입력 유효성 검사 전경색입니다.\",\"오류 심각도의 입력 유효성 검사 테두리 색입니다.\",\"드롭다운 배경입니다.\",\"드롭다운 목록 배경입니다.\",\"드롭다운 전경입니다.\",\"드롭다운 테두리입니다.\",\"단추 기본 전경색입니다.\",\"단추 구분 기호 색입니다.\",\"단추 배경색입니다.\",\"마우스로 가리킬 때 단추 배경색입니다.\",\"버튼 테두리 색입니다.\",\"보조 단추 전경색입니다.\",\"보조 단추 배경색입니다.\",\"마우스로 가리킬 때 보조 단추 배경색입니다.\",\"활성 무선 송수신 장치 옵션의 전경색입니다.\",\"활성 무선 송수신 장치 옵션의 배경색입니다.\",\"활성 무선 송수신 장치 옵션의 테두리 색입니다.\",\"비활성 무선 송수신 장치 옵션의 전경색입니다.\",\"비활성 무선 송수신 장치 옵션의 배경색입니다.\",\"비활성 무선 송수신 장치 옵션의 테두리 색입니다.\",\"마우스로 가리킬 때 비활성 활성 무선 송수신 장치 옵션의 배경색입니다.\",\"확인란 위젯의 배경색입니다.\",\"확인란 위젯이 포함된 요소가 선택된 경우의 확인란 위젯 배경색입니다.\",\"확인란 위젯의 전경색입니다.\",\"확인란 위젯의 테두리 색입니다.\",\"확인란 위젯이 포함된 요소가 선택된 경우의 확인란 위젯 테두리 색입니다.\",\"키 바인딩 레이블 배경색입니다. 키 바인딩 레이블은 바로 가기 키를 나타내는 데 사용됩니다.\",\"키 바인딩 레이블 전경색입니다. 키 바인딩 레이블은 바로 가기 키를 나타내는 데 사용됩니다.\",\"키 바인딩 레이블 테두리 색입니다. 키 바인딩 레이블은 바로 가기 키를 나타내는 데 사용됩니다.\",\"키 바인딩 레이블 테두리 아래쪽 색입니다. 키 바인딩 레이블은 바로 가기 키를 나타내는 데 사용됩니다.\",\"목록/트리가 활성 상태인 경우 포커스가 있는 항목의 목록/트리 배경색입니다. 목록/트리가 활성 상태이면 키보드 포커스를 가지며, 비활성 상태이면 포커스가 없습니다.\",\"목록/트리가 활성 상태인 경우 포커스가 있는 항목의 목록/트리 전경색입니다. 목록/트리가 활성 상태이면 키보드 포커스를 가지며, 비활성 상태이면 포커스가 없습니다.\",\"목록/트리가 활성 상태인 경우 포커스가 있는 항목의 목록/트리 윤곽선 색입니다. 목록/트리가 활성 상태이면 키보드 포커스를 가지며, 비활성 상태이면 포커스가 없습니다.\",\"목록/트리가 활성화되고 선택되었을 때 초점이 맞춰진 항목의 목록/트리 윤곽선 색상입니다. 활성 목록/트리에는 키보드 포커스가 있고 비활성에는 그렇지 않습니다.\",\"목록/트리가 활성 상태인 경우 선택한 항목의 목록/트리 배경색입니다. 목록/트리가 활성 상태이면 키보드 포커스를 가지며, 비활성 상태이면 포커스가 없습니다.\",\"목록/트리가 활성 상태인 경우 선택한 항목의 목록/트리 전경색입니다. 목록/트리가 활성 상태이면 키보드 포커스를 가지며, 비활성 상태이면 포커스가 없습니다.\",\"목록/트리가 활성 상태인 경우 선택한 항목의 목록/트리 아이콘 전경색입니다. 목록/트리가 활성 상태이면 키보드 포커스를 가지며, 비활성 상태이면 포커스가 없습니다.\",\"목록/트리가 비활성 상태인 경우 선택한 항목의 목록/트리 배경색입니다. 목록/트리가 활성 상태이면 키보드 포커스를 가지며, 비활성 상태이면 포커스가 없습니다.\",\"목록/트리가 비활성 상태인 경우 선택한 항목의 목록/트리 전경색입니다. 목록/트리가 활성 상태이면 키보드 포커스를 가지며, 비활성 상태이면 포커스가 없습니다.\",\"목록/트리가 비활성 상태인 경우 선택한 항목의 목록/트리 아이콘 전경색입니다. 목록/트리가 활성 상태이면 키보드 포커스를 가지며, 비활성 상태이면 포커스가 없습니다.\",\"목록/트리가 비활성 상태인 경우 포커스가 있는 항목의 목록/트리 배경색입니다. 목록/트리가 활성 상태이면 키보드 포커스를 가지며, 비활성 상태이면 포커스가 없습니다.\",\"목록/트리가 비활성 상태인 경우 포커스가 있는 항목의 목록/트리 윤곽선 색입니다. 목록/트리가 활성 상태이면 키보드 포커스를 가지며, 비활성 상태이면 포커스가 없습니다.\",\"마우스로 항목을 가리킬 때 목록/트리 배경입니다.\",\"마우스로 항목을 가리킬 때 목록/트리 전경입니다.\",\"마우스로 항목을 이동할 때 목록/트리 끌어서 놓기 배경입니다.\",\"마우스를 사용할 때 항목 간에 항목을 이동할 때 목록/트리 끌어서 놓기 테두리 색입니다.\",\"목록/트리 내에서 검색할 때 일치 항목 강조 표시의 목록/트리 전경색입니다.\",\"목록/트리 내에서 검색할 때 일치 항목의 목록/트리 전경색이 능동적으로 포커스가 있는 항목을 강조 표시합니다.\",\"잘못된 항목에 대한 목록/트리 전경 색(예: 탐색기의 확인할 수 없는 루트).\",\"오류를 포함하는 목록 항목의 전경색입니다.\",\"경고를 포함하는 목록 항목의 전경색입니다.\",\"목록 및 트리에서 형식 필터 위젯의 배경색입니다.\",\"목록 및 트리에서 형식 필터 위젯의 윤곽선 색입니다.\",\"일치하는 항목이 없을 때 목록 및 트리에서 표시되는 형식 필터 위젯의 윤곽선 색입니다.\",\"목록 및 트리에서 유형 필터 위젯의 그림자 색상입니다.\",\"필터링된 일치 항목의 배경색입니다.\",\"필터링된 일치 항목의 테두리 색입니다.\",\"강조되지 않은 항목의 목록/트리 전경색.\",\"들여쓰기 가이드의 트리 스트로크 색입니다.\",\"활성 상태가 아닌 들여쓰기 안내선의 트리 스트로크 색입니다.\",\"열 사이의 표 테두리 색입니다.\",\"홀수 테이블 행의 배경색입니다.\",\"동작 목록 배경색입니다.\",\"동작 목록 전경색입니다.\",\"포커스가 있는 항목의 동작 목록 전경색입니다.\",\"포커스가 있는 항목의 동작 목록 배경색입니다.\",\"메뉴 테두리 색입니다.\",\"메뉴 항목 전경색입니다.\",\"메뉴 항목 배경색입니다.\",\"메뉴의 선택된 메뉴 항목 전경색입니다.\",\"메뉴의 선택된 메뉴 항목 배경색입니다.\",\"메뉴의 선택된 메뉴 항목 테두리 색입니다.\",\"메뉴에서 구분 기호 메뉴 항목의 색입니다.\",\"일치하는 항목을 찾기 위한 미니맵 표식 색입니다.\",\"편집기 선택을 반복하기 위한 미니맵 표식 색입니다.\",\"편집기 선택 작업을 위한 미니맵 마커 색입니다.\",\"정보에 대한 미니맵 마커 색상입니다.\",\"경고의 미니맵 마커 색상입니다.\",\"오류에 대한 미니맵 마커 색상입니다.\",\"미니맵 배경색입니다.\",\"미니맵에서 렌더링된 전경 요소의 불투명도입니다. 예를 들어, \\\"#000000c0\\\"은 불투명도 75%로 요소를 렌더링합니다.\",\"미니맵 슬라이더 배경색입니다.\",\"마우스로 가리킬 때 미니맵 슬라이더 배경색입니다.\",\"클릭했을 때 미니맵 슬라이더 배경색입니다.\",\"활성 섀시의 테두리 색입니다.\",\"배지 배경색입니다. 배지는 검색 결과 수와 같은 소량의 정보 레이블입니다.\",\"배지 전경색입니다. 배지는 검색 결과 수와 같은 소량의 정보 레이블입니다.\",\"스크롤되는 보기를 나타내는 스크롤 막대 그림자입니다.\",\"스크롤 막대 슬라이버 배경색입니다.\",\"마우스로 가리킬 때 스크롤 막대 슬라이더 배경색입니다.\",\"클릭된 상태일 때 스크롤 막대 슬라이더 배경색입니다.\",\"장기 작업을 대상으로 표시될 수 있는 진행률 표시줄의 배경색입니다.\",\"빠른 선택기 배경색. 빠른 선택기 위젯은 명령 팔레트와 같은 선택기를 위한 컨테이너입니다.\",\"빠른 선택기 전경색. 이 빠른 선택기 위젯은 명령 팔레트와 같은 선택기를 위한 컨테이너입니다.\",\"빠른 선택기 제목 배경색. 이 빠른 선택기 위젯은 명령 팔레트와 같은 선택기를 위한 컨테이너입니다.\",\"그룹화 레이블에 대한 빠른 선택기 색입니다.\",\"그룹화 테두리에 대한 빠른 선택기 색입니다.\",\"대신 quickInputList.focusBackground를 사용하세요.\",\"포커스가 있는 항목의 빠른 선택기 전경색입니다.\",\"포커스가 있는 항목의 빠른 선택기 아이콘 전경색입니다.\",\"포커스가 있는 항목의 빠른 선택기 배경색입니다.\",\"검색 뷰렛 완료 메시지의 텍스트 색입니다.\",\"검색 편집기 쿼리의 색상이 일치합니다.\",\"검색 편집기 쿼리의 테두리 색상이 일치합니다.\",\"이 색은 투명해야 합니다. 그렇지 않으면 콘텐츠가 가려집니다.\",\"기본 색을 사용합니다.\",\"사용할 글꼴의 ID입니다. 설정하지 않으면 첫 번째로 정의한 글꼴이 사용됩니다.\",\"아이콘 정의와 연결된 글꼴 문자입니다.\",\"위젯에서 닫기 작업의 아이콘입니다.\",\"이전 편집기 위치로 이동 아이콘입니다.\",\"다음 편집기 위치로 이동 아이콘입니다.\",\"{0} 파일이 닫히고 디스크에서 수정되었습니다.\",\"{0} 파일은 호환되지 않는 방식으로 수정되었습니다.\",\"모든 파일에서 '{0}'을(를) 실행 취소할 수 없습니다. {1}\",\"모든 파일에서 '{0}'을(를) 실행 취소할 수 없습니다. {1}\",\"{1}에 변경 내용이 적용되었으므로 모든 파일에서 '{0}'을(를) 실행 취소할 수 없습니다.\",\"{1}에서 실행 취소 또는 다시 실행 작업이 이미 실행 중이므로 모든 파일에서 '{0}'을(를) 실행 취소할 수 없습니다.\",\"그동안 실행 취소 또는 다시 실행 작업이 발생했기 때문에 모든 파일에서 '{0}'을(를) 실행 취소할 수 없습니다.\",\"모든 파일에서 '{0}'을(를) 실행 취소하시겠습니까?\",\"파일 {0}개에서 실행 취소(&&U)\",\"이 파일 실행 취소(&&F)\",\"실행 취소 또는 다시 실행 작업이 이미 실행 중이므로 '{0}'을(를) 실행 취소할 수 없습니다.\",\"'{0}'을(를) 실행 취소하시겠습니까?\",\"예(&&Y)\",\"아니요\",\"모든 파일에서 '{0}'을(를) 다시 실행할 수 없습니다. {1}\",\"모든 파일에서 '{0}'을(를) 다시 실행할 수 없습니다. {1}\",\"{1}에 변경 내용이 적용되었으므로 모든 파일에서 '{0}'을(를) 다시 실행할 수 없습니다.\",\"{1}에서 실행 취소 또는 다시 실행 작업이 이미 실행 중이므로 모든 파일에서 '{0}'을(를) 다시 실행할 수 없습니다.\",\"그동안 실행 취소 또는 다시 실행 작업이 발생했기 때문에 모든 파일에서 '{0}'을(를) 다시 실행할 수 없습니다.\",\"실행 취소 또는 다시 실행 작업이 이미 실행 중이므로 '{0}'을(를) 다시 실행할 수 없습니다.\",\"코드 작업 영역\"];\nglobalThis._VSCODE_NLS_LANGUAGE=\"ko\";"], "mappings": "AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,+DAOA,WAAW,qBAAqB,CAAC,WAAW,eAAK,yCAAW,kCAAS,kCAAS,eAAK,yCAAW,sIAAkC,+QAA6D,oBAAU,oBAAU,oBAAU,oDAAiB,4BAAa,wCAAU,mCAAU,mBAAS,+BAAW,eAAK,yCAAW,wCAAU,wCAAU,wCAAU,eAAK,4BAAQ,mEAAiB,KAAK,8BAAU,WAAW,mFAAuB,+KAAwC,+KAAwC,2CAAkB,+KAAwC,OAAO,QAAQ,QAAQ,UAAU,OAAO,QAAQ,QAAQ,eAAK,OAAO,QAAQ,eAAK,eAAK,OAAO,QAAQ,QAAQ,UAAU,OAAO,QAAQ,QAAQ,eAAK,KAAK,KAAK,KAAK,KAAK,KAAK,yGAAyB,yGAAyB,qDAAa,iCAAa,4BAAQ,iCAAa,4BAAQ,iCAAa,4BAAQ,mFAAuB,yBAAU,+aAA4J,4DAAe,+EAAmB,yEAAkB,4HAA6B,0BAAW,4BAAQ,yCAAW,+EAAmB,+EAAmB,2BAAO,0DAAkB,kEAAgB,kEAAgB,mGAA6B,mGAA6B,6FAA4B,eAAK,4MAAiD,yCAAW,oCAAW,sCAAa,yFAA4C,4BAAQ,uDAAoB,4DAAyB,sCAAkB,gCAAiB,8GAA8B,yCAAW,yCAAW,yCAAW,yCAAW,8CAAgB,8CAAgB,4DAAe,kGAAuB,4DAAe,wCAAU,qDAAa,uEAAqB,kEAAgB,sCAAa,gIAA4B,kEAAgB,sIAA6B,sCAAa,4DAAe,2GAAgC,iHAAiC,8DAAsB,oEAAuB,wEAAiB,qDAAa,+HAAgC,4IAAmC,kIAA8B,yHAA+B,yHAA+B,mFAAuB,0FAAyB,4FAAsB,+CAAY,qBAAM,qPAA4D,6XAAoI,iQAAmE,8NAAyD,kGAAuB,qPAAuD,mEAAiB,iGAAsB,wIAA+B,wGAAwB,0UAAsE,gJAAkC,mKAAsC,0LAA6D,wKAAqD,qOAA2D,uKAAqC,2MAA2C,sMAA2C,kXAAgF,mZAAsH,mJAAgC,qHAA2B,qHAA2B,yOAAqD,qHAA2B,qHAA2B,+OAA2D,oLAA6C,2LAA+C,0JAAuC,6MAA6C,oOAAqD,qOAAsD,kNAAkD,kOAAwD,yIAAqC,4DAAe,qFAAoB,0FAAyB,iGAAsB,2FAAqB,2KAAyC,wIAA+B,uKAAqC,iMAA2C,qKAAwC,kRAAgE,uRAA2D,uLAA2C,6IAA+B,oJAAiC,kMAA4C,+IAAiC,6NAAmD,6HAA8B,+KAAwC,8IAAgC,0NAAgD,uIAA8B,kLAAgD,+IAAiC,qHAA2B,0LAAyC,gKAAmC,2KAAmD,8WAAsF,uQAA0D,8VAA8G,8PAA+E,0YAA6G,iUAAyH,0QAA+G,2XAAsI,gFAAoB,oJAA+C,oNAA6D,wEAAsB,mHAA8B,4MAAiD,4TAAuJ,2KAAyC,wLAA4C,6JAAoD,iLAA0C,2KAAyC,6KAA2C,0LAA8C,mLAA4C,mLAA4C,6KAA2C,+EAAmB,2JAAmC,wMAA6C,mQAA0E,wMAA6C,+aAAgG,iWAA8E,+RAA8D,2HAA4B,mKAAsC,oMAA8C,6IAA+B,6KAAsC,mGAAwB,icAAmG,+NAAgD,sIAA6B,+EAAmB,mLAA4C,+LAA8C,+EAAmB,8WAAsF,yNAAoD,sKAAoC;AAAA;AAAA;AAAA,+HAA+H,qFAAoB,0HAA2B,uKAAqC,uOAAmD,8OAAqD,8EAAkB,uGAAuB,gIAA4B,0GAA+B,uHAA6B,uKAAqC,mLAAuC,4KAA0C,uIAA8B,6TAAwE,wKAAsC,6KAAsC,+NAAgD,mPAAqD,+GAA0B,2FAAqB,2FAAqB,wGAAwB,kGAAuB,kIAA8B,gvBAAuK,qFAAoB,kGAAuB,gJAAkC,wHAA8B,4FAAsB,+IAAiC,+EAAmB,yYAAwF,iIAA6B,kGAAuB,4FAAsB,qHAA2B,iIAA6B,kGAAuB,4FAAsB,qHAA2B,qFAAoB,qFAAoB,kNAA6C,0LAAyC,wVAAmG,6MAA6C,mTAAwE,sMAA2C,4MAA4C,2HAA4B,mLAAuC,4KAAqC,8KAAuC,0LAAyC,qHAA2B,wIAA+B,yZAAyF,8GAAyB,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,gQAA6D,qLAAyC,oHAA0B,0LAAyC,iGAAsB,qHAA2B,gMAA0C,6KAAsC,8GAAyB,kIAA8B,oLAAwC,wJAAgC,8GAAyB,mNAA8C,2HAA4B,yLAAwC,0JAAkC,6IAA+B,8QAA4D,oLAAwC,uKAAqC,mRAAqF,8IAA0C,8IAA0C,0JAA4C,wIAAyC,kbAAkH,iKAAoC,0HAA2B,wIAA+B,6IAA+B,wRAA4D,uNAAkD,iRAA4G,qJAA4C,+IAA2C,qJAA4C,wKAAgD,ikBAAsL,+IAA2C,+IAA2C,qJAA4C,+IAA2C,iKAA8C,+IAA2C,+IAA2C,qJAA4C,qJAA4C,+IAA2C,yIAA0C,+IAA2C,qJAA4C,6IAAmD,qJAA4C,qJAA4C,yIAA0C,+IAA2C,+IAA2C,yKAAiD,+IAA2C,uJAAwD,4JAA8C,uIAA6C,gJAAiD,+IAAiC,mKAA0D,yUAAmG,yUAAmG,iJAAmC,qHAA2B,8HAA+B,8HAA+B,kGAAuB,4RAAqE,oVAA2E,2JAAmC,qNAAgD,uKAAqC,yVAA2E,mLAAuC,uOAAmD,kYAAgG,gJAAuC,6VAAyF,mfAAkH,wCAAU,yLAAwC,6KAAsC,6KAAsC,sOAAkD,uKAAqC,uKAAqC,sOAAkD,gMAA0C,gOAAiD,oNAA+C,oLAAwC,6KAAsC,6KAAsC,kPAAoD,sIAA6B,oHAA0B,gMAA0C,mSAAuE,qaAAqG,uUAAwE,0LAAyC,iGAAsB,iGAAsB,uOAAmD,oRAA6D,yIAAqC,uFAA2B,0MAAwE,gMAA0C,0LAAyC,4KAAqC,wIAA+B,sKAAoC,kMAA4C,gKAAmC,0JAAkC,uGAAuB,iIAA6B,8PAAsD,0HAA2B,6IAA+B,2HAA4B,yWAA0G,wJAAyD,8EAAsC,mIAAwD,kLAA+D,uMAA4C,6GAA6B,4HAA6B,iGAAsB,4KAAqC,yGAA8B,4KAAqC,wQAA2D,wGAAwB,2HAA4B,0JAAkC,kLAAsC,gTAAqE,yKAAuC,wEAAiB,ubAA8F,0LAAyC,gSAA+D,gKAAmC,gFAAoB,kVAA8E,0LAAyC,iGAAsB,gKAAkD,8MAAmD,+MAAoD,qHAA2B,wGAAsD,yGAAkD,2cAAoK,yGAAyB,wGAAwB,uMAA4C,oKAAuC,kGAAuB,+GAA0B,yKAAuC,wKAAsC,oJAAiC,6EAAsB,4FAAsB,mPAAqD,kMAA4C,wKAAsC,6KAAsC,+HAA+C,sIAA6B,2JAAmC,4HAA6B,iKAAoC,+PAAuD,2JAAmC,gIAA4B,qFAAoB,6IAA+B,8IAAgC,4MAA4C,gKAAmC,uVAAyE,8GAA8B,yNAA+C,qFAAoB,wIAA+B,2JAAmC,uIAA8B,iIAA6B,8GAAyB,yGAAyB,+GAA0B,+GAA0B,kGAAuB,kMAA4C,iLAAqC,yRAA6D,2KAA8C,mNAAwD,0LAAyC,sFAAqB,4RAAyF,0MAAwE,wKAAsC,sJAAmC,wGAAwB,kQAAyE,2FAAqB,0HAA2B,iGAAsB,0HAA2B,2JAAmC,kIAA8B,sFAAqB,uTAA4E,+LAA8C,mMAA6C,4DAAe,qFAAoB,+EAAuC,kIAAkD,yEAAkB,iLAA6E,8MAA8C,sMAA2C,4HAA6B,qHAA2B,yUAA0E,0HAA2B,iXAAoF,2HAA4B,4DAAe,8OAAqD,yIAAgC,+SAAoE,yIAAgC,+SAAoE,+EAAmB,mEAAiB,2FAAqB,iMAA6F,wGAAwB,6MAAyG,4EAAqB,4EAAqB,4EAAqB,4EAAqB,4EAAqB,4EAAqB,yFAAwB,yFAAwB,yFAAwB,yFAAwB,yFAAwB,yFAAwB,+EAAmB,qJAAgE,+EAAmB,4JAAwD,wEAAiB,2FAAqB,wEAAiB,8EAAkB,+EAAmB,iGAAsB,2LAA0C,kKAAqC,gkBAAmK,0HAA2B,mHAAyB,mHAAyB,qRAA8D,4FAAsB,4FAAsB,4FAAsB,oMAA8C,oMAA8C,oMAA8C,oMAA8C,oMAA8C,oMAA8C,iGAAsB,gPAAuD,gPAAuD,gPAAuD,gPAAuD,gPAAuD,gPAAuD,0OAAsD,0OAAsD,0OAAsD,0OAAsD,0OAAsD,0OAAsD,iKAAoC,0JAAkC,8IAAgC,gNAAgD,kMAA4C,8EAAkB,kFAAsB,qGAA0B,KAAK,yHAA+B,gGAA0B,uIAA8B,oFAAwB,2IAAkC,2FAAqB,0HAAgC,0HAAgC,yDAAiB,4CAAc,yHAA8C,uGAAuB,qHAA2B,mJAAqC,oFAAmB,6GAAwB,8EAAkB,8EAAkB,8GAAyB,oHAA0B,wJAAqC,2DAAc,8GAAyB,8GAAyB,6FAA4B,iGAAsB,iGAAsB,iGAAsB,8GAAyB,iGAAsB,2HAA4B,8GAAyB,iGAAsB,oHAA0B,gIAA4B,oHAA0B,8GAAyB,2HAA4B,wIAA+B,qJAAkC,eAAK,eAAK,qBAAM,eAAK,qBAAM,qBAAM,kCAAS,qBAAM,eAAK,eAAK,eAAK,iCAAQ,SAAI,qBAAM,eAAK,uCAAS,OAAO,eAAK,eAAK,qBAAM,qBAAM,eAAK,qBAAM,qBAAM,yCAAW,eAAK,WAAW,kCAAS,kCAAS,gDAAa,sCAAa,+EAAmB,kCAAS,gDAAa,qCAAY,wDAAgB,wCAAU,qDAAa,sFAA0B,uCAAc,YAAO,yCAAW,iDAAmB,sDAAc,kEAAgB,2DAAc,sDAAc,mJAAgC,wCAAU,wCAAU,kCAAS,6CAAe,0JAAkC,oFAAmB,0FAAoB,kCAAS,gCAAY,2BAAO,2BAAO,2BAAO,oBAAU,eAAK,eAAK,eAAK,gCAAY,2BAAO,2BAAO,2BAAO,wEAAiB,wCAAU,wCAAU,eAAK,eAAK,sJAAmC,qFAAoB,oHAA0B,sHAA4B,8JAAsC,kGAAuB,8IAAgC,+BAAW,4FAAsB,+GAA+B,kGAA4B,6FAAuB,4FAAsB,8BAAU,8EAAuB,iEAAoB,kGAAuB,2FAAqB,+BAAW,+GAA+B,kGAA4B,6FAAuB,4FAAsB,wCAAU,qHAA2B,4BAAQ,mEAAiB,+BAAW,gFAAoB,2JAAmC,wPAA0D,kUAAuF,sFAA+B,wEAAiB,2DAAc,+BAAW,4BAAQ,eAAK,qBAAM,qBAAM,eAAK,kCAAS,4BAAQ,6MAA6C,oRAA6D,0QAA6D,2SAAqE,2SAAqE,oBAAU,6HAAmC,8CAAgB,yCAAW,6FAAuB,4BAAQ,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,gDAAa,qDAAkB,mCAAU,mCAAU,sDAAc,2DAAmB,qBAAM,kCAAS,4BAAQ,eAAK,qBAAM,eAAK,2BAAO,kCAAS,eAAK,+EAAmB,yCAAW,yCAAW;AAAA,2WAAsG,oEAAkB,oDAAY,0FAAoB,wDAAgB,qHAAgC,6IAA+B,yNAA+C,qDAAa,oFAAmB,+CAAY,mBAAS,mBAAS,4BAAQ,4BAAQ,yCAAW,yCAAW,oBAAU,KAAK,8EAAkB,4CAAc,0JAAkC;AAAA,KAAkC;AAAA,KAAkC,qKAAwC,sJAAmC,eAAK,oBAAU,kCAAS,qDAAa,4BAAQ,4BAAQ,wDAAgB,qJAAkC,2JAAwC,yFAAwB,yFAAwB,yCAAW,yCAAW,qBAAM,0BAAW,yJAAiC,yJAAiC,4JAAoC,4HAA6B,yIAAgC,mIAA+B,mIAA+B,kCAAS,eAAK,eAAK,yCAAW,yCAAW,qDAAa,eAAK,qBAAM,qBAAM,qBAAM,kCAAS,+CAAY,8NAAoD,gBAAW,4BAAQ,yBAAU,0DAAuB,0EAA6B,0DAAuB,yVAAyG,qBAAM,oDAAY,eAAK,4BAAQ,8CAAW,2DAAc,kEAAgB,yCAAW,+CAAY,kGAAuB,kGAAuB,4BAAQ,kCAAS,qDAAa,4DAAe,4DAAe,qFAAoB,sDAAc,gCAAY,kOAAmD,6JAAqC,kGAAuB,oJAAiC,oJAAiC,0LAAyC,0LAAyC,oFAAmB,oFAAmB,kEAAgB,kEAAgB,yEAAkB,4BAAQ,yCAAW,0FAAyB,2FAAqB,0FAAyB,2FAAqB,6GAA6B,iCAAa,6GAA6B,iCAAa,eAAK,eAAK,eAAK,eAAK,oCAAgB,0CAAiB,0CAAiB,mGAAwB,oGAAyB,mGAAwB,oGAAyB,mGAAwB,oGAAyB,4FAAsB,eAAK,eAAK,4FAA2B,sDAAc,uCAAc,eAAK,+EAAwB,sDAAc,6CAAe,+EAAwB,sDAAc,4BAAQ,yGAA8B,gFAAoB,oDAAiB,eAAK,4FAA2B,mEAAiB,6CAAe,wEAAsB,+CAAY,uCAAc,eAAK,eAAK,eAAK,wEAAsB,eAAK,kCAAS,qDAAa,4BAAQ,wCAAU,yCAAW,+CAAY,sDAAc,wCAAU,4BAAQ,kCAAS,yCAAW,qDAAa,8FAAwB,sKAAyC,yBAAU,WAAW,yBAAU,yBAAU,eAAK,yEAAkB,4BAAQ,eAAK,2DAAwB,wDAA0B,gEAAwB,kEAA0B,4BAAQ,6EAAsB,+EAAwB,kGAA4B,8IAAgC,kEAA0B,6BAAc,kGAAuB,4FAAsB,wEAAiB,iIAA6B,uKAAqC,sMAA2C,sFAAqB,2DAAc,iEAAe,uEAAgB,6EAAiB,2DAAc,iEAAe,2DAAc,iEAAe,6UAAyE,+GAA0B,qFAAoB,qFAAoB,+EAAmB,iGAAsB,+EAAmB,qFAAoB,2FAAqB,sFAAqB,8IAAgC,iIAA6B,yBAAU,wQAAoF,4QAAwF,oFAAwB,+EAAmB,oFAAwB,+EAAmB,4BAAQ,yEAAkB,qEAAmB,yEAAkB,+BAAW,uEAAgB,iEAAe,yCAAW,mCAAU,mCAAU,mEAAiB,2DAAc,iEAAe,gDAAa,uEAAgB,+CAAY,kEAAgB,iGAAsB,iGAAsB,oFAAmB,0FAAoB,wGAAwB,gGAAqB,qFAAoB,8GAAyB,4DAAe,mBAAS,oBAAU,sBAAY,mBAAS,8LAA6C,uCAAc,4BAAQ,4DAAe,4DAAe,qDAAa,+EAAmB,4BAAQ,yEAAkB,sBAAO,+CAAY,eAAK,qDAAa,sDAAc,4DAAe,6GAAwB,gMAA0C,sFAAqB,6EAAsB,gBAAM,+GAA0B,+GAA0B,WAAW,eAAK,eAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,qDAAa,qDAAa,gDAAa,mCAAU,wCAAe,yCAAW,8CAAgB,+CAAY,oDAAiB,mCAAU,wCAAe,yCAAW,8CAAgB,qDAAa,iEAAe,yCAAW,+CAAY,sBAAO,kCAAS,kCAAS,mCAAU,yCAAW,yCAAW,4DAAe,sBAAO,4DAAe,wCAAU,wCAAU,qFAAoB,uEAAgB,gCAAiB,2BAAiB,wCAAe,+CAAY,gKAAmC,iJAAmC,qHAA2B,4BAAQ,kCAAS,mBAAS,oBAAU,sBAAY,mBAAS,gCAAY,4BAAQ,sIAA6B,6CAAe,6CAAe,yCAAW,8CAAgB,+CAAY,oDAAiB,gDAAa,qDAAkB,sDAAc,gDAAa,mGAAwB,8CAAgB,mGAAwB,8CAAgB,sHAA4B,4HAA6B,sFAAqB,8CAAgB,yCAAW,+CAAY,qFAAoB,+CAAY,qFAAoB,4DAAe,+GAA0B,+GAA0B,oBAAU,kIAA8B,uIAA8B,eAAK,oFAAwB,qDAAkB,kEAAqB,qFAAyB,oFAAwB,0HAAgC,0HAAgC,gIAAiC,gIAAiC,6EAAsB,0FAAyB,qHAA2B,4HAAkC,qHAAgC,iIAAkC,gIAA4B,qHAA2B,mFAA4B,mDAAgB,oJAAgD,4HAAuC,8KAAuC,iKAAoC,wEAAiB,+CAAY,wCAAU,kCAAS,oBAAU,oBAAU,0BAAW,oBAAU,0BAAW,oBAAU,0BAAW,0BAAW,0BAAW,0BAAW,sCAAa,4CAAc,0BAAW,8CAAgB,oBAAU,oBAAU,0BAAW,uCAAc,0BAAW,oBAAU,oBAAU,oBAAU,oBAAU,oBAAU,cAAS,oBAAU,oBAAU,+GAA0B,qHAA2B,+CAAY,gJAAkC,qFAA8B,gDAAkB,qHAA0C,8IAAgC,8IAAgC,+CAAY,kLAA2C,+EAAmB,+EAAmB,8GAAyB,0HAA2B,qEAAwB,mFAAuB,wLAAiD,gDAAa,eAAK,yCAAW,8CAAgB,yCAAW,8CAAgB,wGAAwB,6HAA8B,6HAA8B,qEAAmB,qBAAM,qBAAM,qBAAM,qBAAM,qBAAM,qBAAM,qBAAM,SAAI,SAAI,SAAI,SAAI,SAAI,SAAI,SAAI,UAAK,UAAK,UAAK,UAAK,UAAK,UAAK,UAAK,UAAK,UAAK,WAAM,WAAM,WAAM,UAAK,UAAK,UAAK,UAAK,UAAK,UAAK,UAAK,UAAK,UAAK,WAAM,WAAM,WAAM,iFAAqB,kCAAS,uCAAc,0DAAkB,kEAAgB,oNAA+C,8EAAkB,sFAAqB,mEAAiB,wGAAwB,kCAAS,8EAAkB,2FAAqB,yGAAyB,sOAAkD,0GAA+B,4HAA6B,oHAA0B,gKAAmC,2FAA0B,+CAAY,eAAK,eAAK,qBAAM,qBAAM,eAAK,kCAAS,kCAAS,mEAAiB,wEAAiB,+EAAmB,wEAAiB,oHAA0B,uIAA8B,oHAA0B,gHAA2B,8MAA8C,qFAAoB,yBAAU,4DAAe,eAAK,eAAe,UAAU,WAAW,yBAAe,eAAK,yBAAU,oHAA0B,kCAAS,6NAAmD,6NAAmD,mOAAoD,0MAA+C,6NAAmD,mOAAoD,mOAAoD,gPAAuD,mOAAoD,6NAAmD,6NAAmD,6NAAmD,6NAAmD,+OAAsD,uNAAkD,mOAAoD,mOAAoD,6NAAmD,qPAAuD,qNAAqD,6NAAmD,6NAAmD,mOAAoD,mOAAoD,6NAAmD,6NAAmD,0OAAsD,mOAAoD,6NAAmD,mOAAoD,sPAAwD,6NAAmD,6NAAmD,oLAA6C,+GAA+B,+EAAwB,maAA8F,yEAAkB,0JAAkC,iMAAgD,iKAAoC,8KAAuC,+EAAmB,gMAAyD,0KAA6C,sEAAoB,yFAA6B,4BAAQ,gFAAoB,6FAAuB,sFAAqB,mGAAwB,6DAAgB,uFAAsB,0EAAmB,oGAAyB,sFAA0B,0GAA+B,yCAAW,wIAAoC,yDAAiB,6IAAsC,2DAAc,oFAAmB,4fAAyJ,6EAAsB,eAAK,wSAAkE,8SAAmE,uQAA0D,sJAAmC,4JAAoC,qHAA2B,+QAA6D,kUAAwE,wSAAkE,4DAAe,4DAAe,4DAAe,4BAAQ,+CAAY,eAAK,kCAAS,eAAK,yCAAW,yCAAW,yCAAW,yCAAW,yCAAW,eAAK,kCAAS,qBAAM,+CAAY,+CAAY,4BAAQ,uEAAgB,qBAAM,4DAAe,+CAAY,4BAAQ,kCAAS,4BAAQ,4BAAQ,+CAAY,2DAAc,+CAAY,4BAAQ,4BAAQ,4BAAQ,qCAAiB,qCAAiB,+BAAgB,+BAAgB,iCAAa,iCAAa,iCAAa,+CAAY,+CAAY,+CAAY,qBAAM,qBAAM,qBAAM,qBAAM,eAAK,eAAK,eAAK,eAAK,+CAAY,+CAAY,eAAK,qBAAM,qBAAM,eAAK,4BAAQ,qBAAM,WAAW,WAAW;AAAA,WAAmB,kCAAc,WAAW,qBAAM,yCAAW,2BAAY,yCAAW,sFAA0B,qCAAY,oDAAsB,4BAAQ,uIAA8B,mEAAiB,+CAAY,yCAAW,yCAAW,+CAAY,4DAAe,4DAAe,8FAAwB,iIAA6B,qHAA2B,iIAA6B,qHAA2B,mEAAiB,kVAAiH,oKAA4C,sMAA0D,8GAAyB,gDAAa,oOAAyE,0DAAuB,gCAAY,+CAAY,2HAAiC,4DAAe,8GAAyB;AAAA,4BAAyB,iEAAoB,iEAAoB,mEAAsB,oFAAmB,yJAAsC,+DAAkB,uGAAuB,kDAAe,qHAA2B,sEAAoB,uFAA2B,4FAAgC,kGAAuB,2RAAyE,iEAAe,8JAA2C,sIAAuC,mFAA4B,mFAA4B,2BAAO,wGAAsD,yGAAkD,+jBAAkI,0bAAiG,sRAA+D,uKAAqC,8GAAyB,+JAAkC,uKAAqC,gKAAkD,yGAA8B,kOAAmD,qFAAoB,2JAAmC,iRAA0D,+ZAA0F,4OAAmD,4RAAgE,iIAAoF,yGAAyB,4FAAsB,iMAA2C,wYAAuF,6IAA+B,4MAAiD,uWAA6G,eAAK,eAAK,eAAK,qDAAa,kCAAS,oDAAY,4BAAQ,kCAAS,WAAW,0FAAyB,WAAW,wIAA+B,qFAAoB,kIAA8B,eAAK,yKAAiD,YAAY,uGAAuB,mYAAuF,+SAAoE,kSAAiE,4DAAe,yBAAU,yBAAU,eAAK,kCAAS,oBAAU,eAAK,4BAAQ,uEAAqB,oNAA+C,gQAAwD,uQAA0D,6KAAsC,iGAAsB,oRAA6D,0NAAgD,uOAAmD,mSAAkE,qFAAoB,6KAAsC,wEAAiB,6IAA+B,6IAA+B,kGAAuB,yGAAyB,kGAAuB,8EAAkB,qFAAoB,uGAAuB,uGAAuB,uGAAuB,uGAAuB,iGAAsB,uGAAuB,2DAAc,wEAAiB,0FAAoB,6IAA+B,iGAAsB,kGAAuB,2HAA4B,iIAA6B,uQAA0D,yZAAyF,sQAAyD,wGAAwB,uKAAqC,sQAAyD,wGAAwB,uKAAqC,sQAAyD,wGAAwB,iKAAoC,6GAAwB,iKAAoC,4DAAe,+EAAmB,wGAAwB,2PAAwD,iSAAgE,2JAAmC,sFAAqB,yGAAyB,+OAAsD,kGAAuB,oPAAsD,wGAAwB,wGAAwB,uQAA0D,8QAA4D,4DAAe,8EAAkB,qFAAoB,8GAAyB,2DAAc,2DAAc,2FAAqB,2FAAqB,wGAAwB,wGAAwB,8GAAyB,wIAA+B,oGAAyB,sHAA4B,6HAA8B,yIAAgC,yIAAgC,6OAAoD,uOAAmD,iOAAkD,uOAAmD,2FAAqB,2FAAqB,wIAA+B,wIAA+B,2FAAqB,2FAAqB,+GAA0B,6NAAwD,qIAAiC,qIAAiC,qIAAiC,qJAAkC,2JAAmC,oLAAwC,0LAAyC,sHAA4B,+GAA0B,qFAAoB,+GAA0B,4FAAsB,wGAAwB,wQAA2D,8QAA4D,oRAA6D,0RAA8D,qRAA8D,2RAA+D,2JAAmC,wIAA+B,wIAA+B,qJAAkC,+QAA6D,kSAAiE,wGAAwB,wGAAwB,wGAAwB,4DAAe,4DAAe,kEAAgB,iIAA6B,0HAA2B,kIAA8B,0HAA2B,wIAA+B,2HAA4B,2HAA4B,kIAA8B,2HAA4B,2HAA4B,kIAA8B,2HAA4B,2HAA4B,kIAA8B,2DAAc,wEAAiB,2DAAc,iEAAe,kEAAgB,mEAAiB,qDAAa,wGAAwB,4DAAe,kEAAgB,kEAAgB,qHAA2B,qHAA2B,qHAA2B,4HAA6B,2HAA4B,2HAA4B,kIAA8B,2LAA0C,8EAAkB,0LAAyC,8EAAkB,qFAAoB,iMAA2C,+OAAsD,+OAAsD,sPAAwD,yQAA4D,6aAA8F,6aAA8F,obAAgG,qaAA2F,0ZAA0F,0ZAA0F,6aAA8F,gaAA2F,gaAA2F,mbAA+F,mbAA+F,0bAAiG,kIAA8B,kIAA8B,kKAAqC,mOAAoD,mMAA6C,iSAAgE,0LAA8C,oHAA0B,oHAA0B,kIAA8B,yIAAgC,kOAAmD,+IAAiC,iGAAsB,wGAAwB,yGAAyB,oHAA0B,iKAAoC,gFAAoB,qFAAoB,kEAAgB,kEAAgB,2HAA4B,2HAA4B,4DAAe,kEAAgB,kEAAgB,wGAAwB,wGAAwB,+GAA0B,+GAA0B,kIAA8B,wIAA+B,4HAA6B,kGAAuB,qFAAoB,kGAAuB,2DAAc,+QAAyE,oFAAmB,uIAA8B,oHAA0B,+EAAmB,kMAA4C,kMAA4C,mJAAgC,iGAAsB,oJAAiC,8IAAgC,oLAAwC,8OAAqD,qPAAuD,kQAA0D,qHAA2B,qHAA2B,oFAA4C,iIAA6B,oJAAiC,iIAA6B,+GAA0B,wGAAwB,2HAA4B,kKAAqC,4DAAe,0MAA+C,wGAAwB,iGAAsB,wGAAwB,wGAAwB,uHAA6B,oIAAgC,iIAAuC,iIAAuC,kNAAuD,oRAAuE,gRAAmE,2HAAiC,oEAAuB,qDAAkB,8NAAyD,qFAAyB,cAAS,qBAAM,iIAAuC,iIAAuC,kNAAuD,oRAAuE,gRAAmE,8NAAyD,wCAAU,EAC/onD,WAAW,qBAAqB", "names": [], "file": "nls.messages.ko.js"}